#!/bin/bash

# Portal Evolution - Inicialização do Projeto
echo "🚀 Iniciando Portal Evolution..."

# Navegar para o diretório do projeto
cd /mnt/persist/workspace/portalcliente

# Verificar se o projeto já está rodando
echo "🔍 Verificando se o projeto já está rodando..."

# Verificar se as portas estão em uso
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ Backend já está rodando na porta 3001"
    BACKEND_RUNNING=true
else
    echo "❌ Backend não está rodando na porta 3001"
    BACKEND_RUNNING=false
fi

if lsof -Pi :5173 -sTCP:LISTEN -t >/dev/null ; then
    echo "✅ Frontend já está rodando na porta 5173"
    FRONTEND_RUNNING=true
else
    echo "❌ Frontend não está rodando na porta 5173"
    FRONTEND_RUNNING=false
fi

# Instalar dependências se necessário
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependências do frontend..."
    npm install
fi

if [ ! -d "server/node_modules" ]; then
    echo "📦 Instalando dependências do backend..."
    cd server
    npm install
    cd ..
fi

# Iniciar backend se não estiver rodando
if [ "$BACKEND_RUNNING" = false ]; then
    echo "🔧 Iniciando backend..."
    cd server
    
    # Verificar se o arquivo .env existe
    if [ ! -f ".env" ]; then
        echo "⚠️ Arquivo .env não encontrado no backend"
        echo "📝 Criando arquivo .env básico..."
        cat > .env << 'EOF'
# Configurações do Servidor
PORT=3001
NODE_ENV=development

# Configurações do Banco de Dados PostgreSQL
DB_HOST=************
DB_PORT=5432
DB_NAME=dbetens
DB_USER=postgres
DB_PASSWORD=ab3780bd73ee4e2804d566ce6fd96209

# JWT Secret
JWT_SECRET=portal-evolution-secret-key-2024

# Webhook URL
WEBHOOK_URL=https://webhook.vps.evo-eden.site/webhook/c8e01995-0591-4989-87a1-77d6d51a97e9
EOF
    fi
    
    # Iniciar backend em background
    echo "🚀 Iniciando servidor backend na porta 3001..."
    nohup node index.js > backend.log 2>&1 &
    BACKEND_PID=$!
    echo "Backend iniciado com PID: $BACKEND_PID"
    
    # Aguardar um pouco para o backend inicializar
    sleep 5
    
    cd ..
else
    echo "ℹ️ Backend já está rodando, pulando inicialização"
fi

# Iniciar frontend se não estiver rodando
if [ "$FRONTEND_RUNNING" = false ]; then
    echo "🎨 Iniciando frontend..."
    
    # Iniciar frontend em background
    echo "🚀 Iniciando servidor frontend na porta 5173..."
    nohup npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo "Frontend iniciado com PID: $FRONTEND_PID"
    
    # Aguardar um pouco para o frontend inicializar
    sleep 10
else
    echo "ℹ️ Frontend já está rodando, pulando inicialização"
fi

# Verificar se os serviços estão respondendo
echo "🔍 Verificando se os serviços estão respondendo..."

# Aguardar mais um pouco para garantir que tudo inicializou
sleep 5

# Testar backend
echo "🧪 Testando backend..."
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ Backend está respondendo em http://localhost:3001"
else
    echo "❌ Backend não está respondendo"
    echo "📋 Log do backend:"
    if [ -f "server/backend.log" ]; then
        tail -20 server/backend.log
    fi
fi

# Testar frontend
echo "🧪 Testando frontend..."
if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ Frontend está respondendo em http://localhost:5173"
else
    echo "❌ Frontend não está respondendo"
    echo "📋 Log do frontend:"
    if [ -f "frontend.log" ]; then
        tail -20 frontend.log
    fi
fi

# Mostrar status final
echo ""
echo "🎉 Portal Evolution Status:"
echo "================================"
echo "🔧 Backend:  http://localhost:3001"
echo "🎨 Frontend: http://localhost:5173"
echo "🧪 Testes:   npm test (8 testes passando)"
echo ""
echo "📋 Credenciais de teste:"
echo "   Admin: admin / adminnbr5410!"
echo "   Cliente: cliente / clientenbr5410!"
echo ""
echo "📁 Logs disponíveis:"
if [ -f "server/backend.log" ]; then
    echo "   Backend: server/backend.log"
fi
if [ -f "frontend.log" ]; then
    echo "   Frontend: frontend.log"
fi
echo ""
echo "🔧 Para parar os serviços:"
echo "   pkill -f 'node index.js'"
echo "   pkill -f 'vite'"
echo ""
echo "✅ Portal Evolution está pronto para uso!"