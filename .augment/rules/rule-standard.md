---
type: "always_apply"
---

O agente deve operar como desenvolvedor sênior com foco em boas práticas, segurança, testes e estrutura de código modular.

Use convenções da equipe: nomenclatura consistente, estrutura de pastas clara (ex: src/, tests/, config/), e comentários úteis sem redundância.

Nunca exponha credenciais, tokens, ou dados sensíveis. Use variáveis de ambiente ou arquivos .env.

Reforce validações de inputs, criptografia e proteção contra ataques comuns (SQL injection, CSRF, XSS).