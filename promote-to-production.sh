#!/bin/bash

# Script para promover alterações do ambiente de desenvolvimento para produção
# Portal Evolution - Ultrathink

echo "🚀 PORTAL EVOLUTION - PROMOÇÃO PARA PRODUÇÃO"
echo "=============================================="
echo ""

# Verificar se o usuário confirmou
read -p "⚠️  Tem certeza que deseja promover as alterações do DEV para PRODUÇÃO? (s/N): " confirm
if [[ $confirm != [sS] ]]; then
    echo "❌ Operação cancelada pelo usuário."
    exit 1
fi

echo ""
echo "📋 INICIANDO PROCESSO DE PROMOÇÃO..."
echo ""

# Etapa 1: Parar serviços de produção
echo "🛑 ETAPA 1: Parando serviços de produção..."
docker service update --replicas 0 portal-evolution_portalevo-backend
docker service update --replicas 0 portal-evolution_portalevo-frontend
sleep 10

# Etapa 2: Remover imagens antigas de produção
echo "🗑️  ETAPA 2: Removendo imagens antigas de produção..."
docker rmi portal-evolution-backend:latest 2>/dev/null || true
docker rmi portal-evolution-frontend:latest 2>/dev/null || true

# Etapa 3: Retaggear imagens de desenvolvimento para produção
echo "🏷️  ETAPA 3: Promovendo imagens de desenvolvimento..."
docker tag portal-evolution-backend-dev:latest portal-evolution-backend:latest
docker tag portal-evolution-frontend-dev:latest portal-evolution-frontend:latest

# Etapa 4: Atualizar stack de produção
echo "🔄 ETAPA 4: Atualizando stack de produção..."
docker stack deploy -c docker-compose.yml portal-evolution

# Etapa 5: Aguardar estabilização
echo "⏳ ETAPA 5: Aguardando estabilização dos serviços..."
sleep 20

# Etapa 6: Verificar status
echo "✅ ETAPA 6: Verificando status dos serviços..."
echo ""
echo "📊 STATUS DOS SERVIÇOS:"
docker service ls | grep portal-evolution_

echo ""
echo "🐳 CONTAINERS ATIVOS:"
docker ps | grep portal-evolution_

echo ""
echo "🎉 PROMOÇÃO CONCLUÍDA COM SUCESSO!"
echo ""
echo "🌐 Acesse: https://portal.evo-eden.site"
echo "🔧 Dev: https://portaldev.evo-eden.site"
echo ""
