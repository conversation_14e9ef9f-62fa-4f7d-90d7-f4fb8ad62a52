# 🚀 GUIA DE DEPLOY - PORTAL EVOLUTION

## 📋 PRÉ-REQUISITOS
- Git configurado com suas credenciais
- Acesso SSH à VPS (31.97.19.106)
- Usuário: vscode
- Senha: nbr5410!

## 🔄 PROCESSO COMPLETO DE DEPLOY

### PASSO 1: Commit e Push Local
```bash
# No diretório: C:\Users\<USER>\Documents\portalevo\portalcliente
git add .
git commit -m "Suas alterações aqui"
git push origin master
```

### PASSO 2: Conectar na VPS
```bash
ssh vscode@31.97.19.106
# Senha: nbr5410!
```

### PASSO 3: Navegar para o Projeto
```bash
cd /home/<USER>/portalevo/portalcliente
```

### PASSO 4: Atualizar <PERSON>
```bash
git pull origin master
```

### PASSO 5: Parar Serviços Atuais
```bash
sudo docker stack rm portal-evolution

# Apagar imagens antigas
sudo docker image prune -f

# Aguardar 10 segundos
sleep 10
```

### PASSO 6: Construir Backend
```bash
sudo docker build -f Dockerfile.backend -t portal-evolution-backend:latest . --no-cache
```

### PASSO 7: Construir Frontend
```bash
sudo docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest . --no-cache
```

### PASSO 8: Construir Sistema de Logs
```bash
sudo docker build -f Dockerfile.logs -t portal-evolution-logs:latest . --no-cache
```

### PASSO 9: Deploy do Stack Completo
```bash
sudo docker stack deploy -c docker-compose.prod.yml portal-evolution
```

### PASSO 10: Aguardar Inicialização
```bash
# Aguardar 30 segundos para os serviços iniciarem
sleep 30
```

### PASSO 11: Executar Script de Correção do Banco
```bash
node fix-all-issues.js
```

### PASSO 11.5: Executar Teste Final (Opcional)
```bash
node test-final.js
```

### PASSO 12: Verificar Status dos Serviços
```bash
sudo docker stack services portal-evolution
```

### PASSO 13: Testar APIs
```bash
# Testar Health
curl -s https://portal.evo-eden.site/api/health

# Testar Login Cliente
curl -s -X POST https://portal.evo-eden.site/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"54321"}'

# Testar Login Admin
curl -s -X POST https://portal.evo-eden.site/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"adminnbr5410!"}'
```

### PASSO 14: Verificar Logs (se necessário)
```bash
# Logs do Backend
sudo docker service logs portal-evolution_portal_backend --tail 20

# Logs do Frontend
sudo docker service logs portal-evolution_portal_frontend --tail 20

# Logs do Sistema de Logs
sudo docker service logs portal-evolution_portal_logs --tail 20
```

### PASSO 15: Sair da VPS
```bash
exit
```

## 🌐 ACESSOS APÓS DEPLOY

- **Portal Principal:** https://portal.evo-eden.site
- **Sistema de Logs:** https://logs.portal.evo-eden.site
- **API Health:** https://portal.evo-eden.site/api/health

## 📊 MONITORAMENTO DE LOGS

O sistema de logs web permite:
- ✅ Visualizar logs em tempo real de todos os serviços
- ✅ Filtrar por nível (error, warning, info, debug)
- ✅ Auto-refresh a cada 2 segundos
- ✅ Interface web responsiva e intuitiva
- ✅ Acesso direto via https://logs.portal.evo-eden.site

## 🔐 CREDENCIAIS DE TESTE

### Admin:
- Email: <EMAIL>
- Senha: adminnbr5410!

### Cliente:
- Email: <EMAIL>
- Senha: 54321

## ⚠️ TROUBLESHOOTING

### Se algum serviço não iniciar:
```bash
# Verificar status
sudo docker stack services portal-evolution

# Verificar logs específicos
sudo docker service logs portal-evolution_[nome-do-servico] --tail 50

# Reiniciar stack se necessário
sudo docker stack rm portal-evolution
sleep 10
sudo docker stack deploy -c docker-compose.prod.yml portal-evolution
```

### Se o banco não conectar:
```bash
# Verificar se PostgreSQL está rodando
sudo docker ps | grep postgres

# Executar script de correção novamente
node fix-all-issues.js
```

## 🎯 VERIFICAÇÃO FINAL

1. ✅ Portal carrega: https://portal.evo-eden.site
2. ✅ Login funciona com credenciais de teste
3. ✅ Logs acessíveis: https://logs.portal.evo-eden.site
4. ✅ APIs respondem corretamente
5. ✅ Sepultamentos carregam sem erro 500
6. ✅ Redirecionamento após login funciona

---
**📝 Nota:** Execute cada passo na ordem. Se algum comando falhar, verifique os logs antes de continuar.
