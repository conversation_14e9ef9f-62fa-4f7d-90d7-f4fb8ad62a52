# 🔒 VALIDAÇÕES DE INTEGRIDADE REFERENCIAL

## 📋 Resumo Executivo

Este documento descreve todas as validações de integridade referencial implementadas no Portal Evolution para garantir a consistência dos dados e prevenir operações que possam comprometer a estrutura hierárquica do sistema.

## 🏗️ Estrutura Hierárquica

```
CLIENTE
└── PRODUTO (ESTAÇÃO)
    └── BLOCO
        └── SUB-BLOCO
            └── GAVETA
                └── SEPULTAMENTO
```

## 🚫 Regras de Validação Implementadas

### 1. **PRODUTOS (ESTAÇÕES)**

#### ❌ **Deleção Bloqueada Quando:**
- Existem **blocos** associados (ativos ou inativos)
- Existem **sub-blocos** associados
- Existem **gavetas** associadas
- Existem **sepultamentos** (especialmente ativos)

#### ❌ **Edição Bloqueada Quando:**
- Tentativa de alterar `codigo_estacao` com **blocos** existentes
- Tentativa de alterar `codigo_cliente` com **dependências**

#### ✅ **Permitido:**
- <PERSON>erar `denominacao`, `meses_para_exumar`, `observacao`
- Alterar `ativo` (status)

### 2. **BLOCOS**

#### ❌ **Deleção Bloqueada Quando:**
- Existem **sub-blocos** associados (ativos)
- Existem **gavetas** associadas
- Existem **sepultamentos** ativos

#### ❌ **Edição Bloqueada Quando:**
- Tentativa de alterar `codigo_bloco` com **sub-blocos** existentes

#### ✅ **Permitido:**
- Alterar `denominacao`
- Alterar `ativo` (status)

### 3. **SUB-BLOCOS**

#### ❌ **Deleção Bloqueada Quando:**
- Existem **gavetas** associadas (ativas)
- Existem **numerações de gavetas** associadas
- Existem **sepultamentos** ativos

#### ❌ **Edição Bloqueada Quando:**
- Tentativa de alterar `codigo_sub_bloco` com **gavetas** existentes

#### ✅ **Permitido:**
- Alterar `denominacao`
- Alterar `ativo` (status)

### 4. **GAVETAS**

#### ❌ **Deleção Bloqueada Quando:**
- Existem **sepultamentos** ativos (não exumados)

#### ❌ **Edição Bloqueada Quando:**
- Tentativa de alterar `numero_gaveta` com **sepultamentos** existentes

#### ✅ **Permitido:**
- Alterar `disponivel` (status)
- Alterar `observacao`

### 5. **SEPULTAMENTOS**

#### ❌ **Deleção Bloqueada Quando:**
- Sepultamento está **ativo** (não exumado)
- Existem **documentos** associados

#### ✅ **Permitido:**
- Exumar sepultamento (`exumado_em` não nulo)
- Alterar dados do sepultado (antes da exumação)

## 🔍 Tipos de Erro Implementados

### `SEPULTAMENTOS_ATIVOS`
- **Criticidade:** 🚨 ALTA
- **Ação:** Bloqueio total da operação
- **Mensagem:** Específica sobre sepultamentos que precisam ser exumados

### `INTEGRIDADE_REFERENCIAL`
- **Criticidade:** ⚠️ MÉDIA
- **Ação:** Bloqueio com orientação
- **Mensagem:** Lista de passos necessários para resolver

### `ALTERACAO_CODIGO_BLOQUEADA`
- **Criticidade:** 🔒 MÉDIA
- **Ação:** Bloqueio de alteração de códigos críticos
- **Mensagem:** Explicação sobre dependências

## 📊 Validações por Endpoint

### `PUT /api/produtos/:codigo_cliente/:codigo_estacao`
- ✅ Validação de campos obrigatórios
- ✅ Verificação de blocos existentes antes de alterar `codigo_estacao`
- ✅ Log de tentativas de alteração bloqueadas

### `DELETE /api/produtos/:id`
- ✅ Validação hierárquica completa
- ✅ Contagem de dependências (blocos, sub-blocos, gavetas, sepultamentos)
- ✅ Mensagens específicas por tipo de dependência
- ✅ Bloqueio especial para sepultamentos ativos

### `PUT /api/produtos/:codigo_cliente/:codigo_estacao/:codigo_bloco`
- ✅ Verificação de sub-blocos antes de alterar `codigo_bloco`
- ✅ Validação de gavetas associadas

### `DELETE /api/produtos/:codigo_cliente/:codigo_estacao/:codigo_bloco`
- ✅ Validação de sub-blocos ativos
- ✅ Verificação de gavetas e sepultamentos

## 🎯 Mensagens de Feedback

### Frontend (JavaScript)
```javascript
// Sepultamentos Ativos
🚨 OPERAÇÃO BLOQUEADA 🚨
Existem X sepultamentos ativos que precisam ser exumados primeiro.
⚠️ AÇÃO OBRIGATÓRIA: Exumar X sepultamentos ativos

// Integridade Referencial
❌ Não é possível deletar o produto "NOME".
Para deletar este produto, você deve primeiro:
1. Exumar X sepultamentos ativos
2. Remover X gavetas
3. Remover X sub-blocos
4. Remover X blocos

// Alteração Bloqueada
🔒 ALTERAÇÃO BLOQUEADA
O código da estação não pode ser alterado pois existem X blocos associados.
```

### Backend (JSON)
```json
{
  "error": "Operação bloqueada por integridade referencial",
  "message": "Mensagem específica do erro",
  "tipo_erro": "SEPULTAMENTOS_ATIVOS|INTEGRIDADE_REFERENCIAL|ALTERACAO_CODIGO_BLOQUEADA",
  "dependencias": {
    "blocos": 0,
    "sub_blocos": 0,
    "gavetas": 0,
    "sepultamentos_total": 0,
    "sepultamentos_ativos": 0
  },
  "passos_necessarios": ["Lista de ações obrigatórias"],
  "codigo_produto": "CODIGO_ESTACAO"
}
```

## 🧪 Testes Implementados

### Arquivo: `test-integridade-crud.js`
- ✅ Teste de deleção com dependências
- ✅ Teste de alteração de código bloqueada
- ✅ Teste de mensagens de erro específicas
- ✅ Validação de autenticação e autorização

### Execução:
```bash
node portalcliente/test-integridade-crud.js
```

## 🔧 Logs de Auditoria

### Operações Logadas:
- ✅ Tentativas de alteração bloqueadas
- ✅ Tentativas de deleção bloqueadas
- ✅ Operações bem-sucedidas
- ✅ Dados antes e depois das alterações

### Informações Capturadas:
- 👤 Usuário responsável
- 🕐 Timestamp da operação
- 🌐 IP de origem
- 🖥️ User-Agent
- 📋 Dados alterados (antes/depois)

## 🚀 Implementação Técnica

### Backend (Node.js + PostgreSQL)
- **Arquivo Principal:** `portalcliente/server/routes/produtos_new.js`
- **Validações:** Função `validarIntegridadeCompleta()`
- **Logs:** Função `logProduto()`, `logBloco()`, etc.

### Frontend (React + Material-UI)
- **Arquivo Principal:** `portalcliente/src/pages/CadastrosProdutosPage.jsx`
- **Modal:** `portalcliente/src/components/ProdutoModal.jsx`
- **Tratamento:** Mensagens específicas por tipo de erro

## 📈 Benefícios Implementados

1. **🔒 Integridade de Dados:** Prevenção de operações que quebrariam a estrutura
2. **👥 Experiência do Usuário:** Mensagens claras sobre o que fazer
3. **📊 Auditoria Completa:** Rastreamento de todas as operações
4. **🛡️ Segurança:** Validações em múltiplas camadas
5. **🔧 Manutenibilidade:** Código organizado e documentado

---

**📅 Última Atualização:** 2025-07-17  
**👨‍💻 Implementado por:** Orquestração de 20 Agentes IA  
**🎯 Status:** ✅ IMPLEMENTADO E TESTADO
