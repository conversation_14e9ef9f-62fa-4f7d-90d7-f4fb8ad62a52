# Implementação do Cadastro de Ossuários - Portal Evolution

## Visão Geral da Funcionalidade

A implementação do cadastro de ossuários introduzirá uma nova modalidade de produto no sistema, diferenciando-se dos ETENs pela estrutura de gavetas com múltiplas urnas. O sistema manterá a hierarquia existente (Cliente → Produto → Bloco → Sub-Bloco → Gaveta) mas com adaptações específicas para ossuários.

## Alterações no Banco de Dados PostgreSQL

### 1. Modificação da Tabela `produtos`

**Alteração Necessária:**
- A tabela já possui o campo `tipo` com CHECK constraint para 'ETEN' e 'OSSUARIO'
- Nenhuma alteração estrutural necessária na tabela produtos

### 2. Modificação da Tabela `blocos`

**Nova Coluna Necessária:**
```sql
ALTER TABLE blocos 
ADD COLUMN tipo_capacidade INTEGER DEFAULT 1 
CHECK (tipo_capacidade IN (1, 2, 3, 4));

COMMENT ON COLUMN blocos.tipo_capacidade IS 'Capacidade de urnas por gaveta: 1=Simples, 2=Duplo, 3=Triplo, 4=Quádruplo';
```

**Justificativa:**
- Campo `tipo_capacidade` define quantas urnas cada gaveta do bloco pode comportar
- Valores: 1 (Simples), 2 (Duplo), 3 (Triplo), 4 (Quádruplo)
- Aplicável apenas para produtos do tipo 'OSSUARIO'
- Para produtos 'ETEN', o valor permanece 1 (padrão)

### 3. Nova Tabela `urnas`

**Criação da Tabela:**
```sql
CREATE TABLE IF NOT EXISTS urnas (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    posicao_urna INTEGER NOT NULL CHECK (posicao_urna BETWEEN 1 AND 4),
    disponivel BOOLEAN DEFAULT true,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao_urna),
    FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta) 
        REFERENCES gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
);

COMMENT ON TABLE urnas IS 'Urnas individuais dentro das gavetas para ossuários';
COMMENT ON COLUMN urnas.posicao_urna IS 'Posição da urna dentro da gaveta (1 a 4)';
```

### 4. Modificação da Tabela `sepultamentos`

**Nova Coluna Necessária:**
```sql
ALTER TABLE sepultamentos 
ADD COLUMN posicao_urna INTEGER DEFAULT NULL;

COMMENT ON COLUMN sepultamentos.posicao_urna IS 'Posição da urna na gaveta (apenas para ossuários)';
```

**Constraint Adicional:**
```sql
ALTER TABLE sepultamentos 
ADD CONSTRAINT chk_posicao_urna_ossuario 
CHECK (
    (posicao_urna IS NULL) OR 
    (posicao_urna BETWEEN 1 AND 4)
);
```

### 5. Índices para Performance

**Novos Índices:**
```sql
CREATE INDEX idx_urnas_disponibilidade 
ON urnas(codigo_cliente, codigo_estacao, disponivel) 
WHERE ativo = true;

CREATE INDEX idx_sepultamentos_urna 
ON sepultamentos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao_urna) 
WHERE ativo = true;
```

## Lógica de Negócio e Regras

### 1. Criação de Ranges para Ossuários

**Processo Automatizado:**
- Quando um range é criado para um bloco de ossuário (ex: gavetas 1-1000)
- O sistema cria automaticamente as gavetas (1000 gavetas)
- Para cada gaveta, cria as urnas baseadas no `tipo_capacidade` do bloco
- Exemplo: Bloco "Duplo" (tipo_capacidade=2) com range 1-1000 = 2000 urnas totais

**Algoritmo de Criação:**
```
Para cada número de gaveta no range (1 a 1000):
    1. Criar gaveta na tabela `gavetas`
    2. Para cada posição (1 até tipo_capacidade do bloco):
        - Criar urna na tabela `urnas` com posicao_urna = posição
        - Marcar urna como disponível = true
```

### 2. Regras de Disponibilidade

**Para ETENs (comportamento atual):**
- Gaveta disponível = true/false
- Um sepultamento por gaveta

**Para Ossuários (novo comportamento):**
- Gaveta disponível = todas as urnas da gaveta estão disponíveis
- Urna disponível = true/false individual
- Múltiplos sepultamentos por gaveta (um por urna)

### 3. Validações Específicas

**Validação de Tipo de Produto:**
- Produtos 'ETEN': tipo_capacidade deve ser sempre 1
- Produtos 'OSSUARIO': tipo_capacidade pode ser 1, 2, 3 ou 4
- Não permitir alteração de tipo após criação de gavetas/urnas

**Validação de Sepultamento:**
- Para ETENs: posicao_urna deve ser NULL
- Para Ossuários: posicao_urna deve ser informada (1 a tipo_capacidade)
- Verificar disponibilidade da urna específica antes do sepultamento

## Alterações no Backend (Node.js/Express)

### 1. Novos Endpoints da API

**Endpoint para Urnas:**
```
GET /api/urnas?codigo_cliente&codigo_estacao&codigo_bloco&codigo_sub_bloco&numero_gaveta
- Lista urnas de uma gaveta específica
- Filtro por disponibilidade

GET /api/urnas/disponiveis?codigo_cliente&codigo_estacao
- Lista todas as urnas disponíveis para sepultamento
- Agrupadas por gaveta para facilitar seleção

PATCH /api/urnas/:id/disponibilidade
- Altera disponibilidade de uma urna específica
```

**Modificação nos Endpoints Existentes:**
```
POST /api/ranges (modificado)
- Detectar tipo do produto (ETEN/OSSUARIO)
- Criar gavetas + urnas conforme tipo_capacidade

POST /api/sepultamentos (modificado)
- Aceitar campo posicao_urna para ossuários
- Validar disponibilidade da urna específica
- Marcar urna como indisponível após sepultamento

DELETE /api/sepultamentos/:id (modificado)
- Liberar urna específica (marcar como disponível)
- Para ETENs, liberar gaveta inteira
```

### 2. Novos Services

**urnaService.js:**
```javascript
// Listar urnas de uma gaveta
async listUrnasByGaveta(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)

// Listar urnas disponíveis para sepultamento
async listUrnasDisponiveis(codigo_cliente, codigo_estacao)

// Atualizar disponibilidade de urna
async updateDisponibilidade(urnaId, disponivel)

// Verificar capacidade total vs ocupada de uma gaveta
async getOcupacaoGaveta(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
```

### 3. Modificações nos Services Existentes

**rangeService.js (modificado):**
```javascript
async createRange(rangeData) {
    // 1. Criar gavetas (comportamento atual)
    // 2. NOVO: Verificar tipo do produto
    // 3. NOVO: Se OSSUARIO, criar urnas baseadas no tipo_capacidade
    // 4. NOVO: Calcular total de urnas criadas para retorno
}
```

**sepultamentoService.js (modificado):**
```javascript
async createSepultamento(sepultamentoData) {
    // 1. NOVO: Verificar se é ETEN ou OSSUARIO
    // 2. Para OSSUARIO: validar posicao_urna
    // 3. Para OSSUARIO: verificar disponibilidade da urna específica
    // 4. Criar sepultamento com posicao_urna
    // 5. Marcar urna como indisponível
}
```

## Alterações no Frontend (React)

### 1. Modificação do Componente ProdutoModal

**Novo Campo Tipo de Produto:**
```jsx
<FormControl fullWidth required>
    <InputLabel>Tipo de Produto</InputLabel>
    <Select
        value={formData.tipo}
        onChange={handleChange}
        name="tipo"
    >
        <MenuItem value="ETEN">ETEN (Estação de Tratamento)</MenuItem>
        <MenuItem value="OSSUARIO">Ossuário</MenuItem>
    </Select>
</FormControl>
```

**Validação Condicional:**
- Mostrar campo "Meses para Exumar" apenas para ETENs
- Para Ossuários, usar valor padrão (ex: 36 meses)

### 2. Modificação do Componente BlocoModal

**Novo Campo Tipo de Capacidade (apenas para Ossuários):**
```jsx
{produtoTipo === 'OSSUARIO' && (
    <FormControl fullWidth required>
        <InputLabel>Tipo de Capacidade</InputLabel>
        <Select
            value={formData.tipo_capacidade}
            onChange={handleChange}
            name="tipo_capacidade"
        >
            <MenuItem value={1}>Simples (1 urna por gaveta)</MenuItem>
            <MenuItem value={2}>Duplo (2 urnas por gaveta)</MenuItem>
            <MenuItem value={3}>Triplo (3 urnas por gaveta)</MenuItem>
            <MenuItem value={4}>Quádruplo (4 urnas por gaveta)</MenuItem>
        </Select>
    </FormControl>
)}
```

**Informações Contextuais:**
- Mostrar aviso sobre impacto na criação de urnas
- Calcular total de urnas que serão criadas baseado nos ranges existentes

### 3. Modificação do Gerenciamento de Ranges

**Exibição Diferenciada:**
```jsx
// Para ETENs (atual)
<Typography>Range 1-1000: 1000 gavetas</Typography>

// Para Ossuários (novo)
<Typography>
    Range 1-1000: 1000 gavetas × {tipo_capacidade} urnas = {1000 * tipo_capacidade} urnas totais
</Typography>
```

**Validação de Criação:**
- Alertar sobre quantidade de urnas que serão criadas
- Confirmar operação para ranges grandes em ossuários

### 4. Novo Componente SepultamentoModal (Ossuários)

**Seleção de Urna:**
```jsx
{produtoTipo === 'OSSUARIO' && (
    <>
        <FormControl fullWidth required>
            <InputLabel>Gaveta</InputLabel>
            <Select value={selectedGaveta} onChange={handleGavetaChange}>
                {gavetasDisponiveis.map(gaveta => (
                    <MenuItem key={gaveta.numero} value={gaveta.numero}>
                        Gaveta {gaveta.numero} ({gaveta.urnas_disponiveis}/{gaveta.urnas_totais} urnas disponíveis)
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
        
        <FormControl fullWidth required>
            <InputLabel>Posição da Urna</InputLabel>
            <Select value={selectedUrna} onChange={handleUrnaChange}>
                {urnasDisponiveis.map(urna => (
                    <MenuItem key={urna.posicao} value={urna.posicao}>
                        Posição {urna.posicao}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    </>
)}
```

### 5. Modificação do Dashboard

**Métricas Diferenciadas:**
```jsx
// Para ETENs
<Card>
    <Typography>Gavetas Ocupadas: {gavetasOcupadas}</Typography>
    <Typography>Gavetas Disponíveis: {gavetasDisponiveis}</Typography>
</Card>

// Para Ossuários
<Card>
    <Typography>Urnas Ocupadas: {urnasOcupadas}</Typography>
    <Typography>Urnas Disponíveis: {urnasDisponiveis}</Typography>
    <Typography>Gavetas com Vagas: {gavetasComVagas}</Typography>
    <Typography>Gavetas Lotadas: {gavetasLotadas}</Typography>
</Card>
```

## Fluxo de Trabalho Completo

### 1. Cadastro de Produto Ossuário
1. Usuário seleciona "Novo Produto"
2. Escolhe tipo "OSSUARIO"
3. Preenche denominação e observações
4. Sistema cria produto com tipo='OSSUARIO'

### 2. Cadastro de Bloco para Ossuário
1. Usuário acessa produto ossuário
2. Clica em "Novo Bloco"
3. Seleciona tipo de capacidade (Simples/Duplo/Triplo/Quádruplo)
4. Sistema cria bloco com tipo_capacidade definido

### 3. Criação de Range de Gavetas
1. Usuário define range (ex: 1-1000)
2. Sistema cria 1000 gavetas
3. Para cada gaveta, cria urnas baseadas no tipo_capacidade
4. Exemplo: Bloco Duplo = 2000 urnas totais (1000 gavetas × 2 urnas)

### 4. Registro de Sepultamento
1. Usuário seleciona gaveta com urnas disponíveis
2. Escolhe posição específica da urna (1, 2, 3 ou 4)
3. Preenche dados do sepultamento
4. Sistema marca urna específica como ocupada

### 5. Visualização e Relatórios
1. Dashboard mostra estatísticas de urnas vs gavetas
2. Relatórios incluem informações de posição das urnas
3. Filtros permitem busca por gaveta e posição específica

## Considerações de Performance

### 1. Impacto na Criação de Ranges
- Range 1-1000 em bloco Quádruplo = 4000 registros na tabela urnas
- Implementar criação em lotes (batch insert)
- Mostrar progresso para ranges grandes

### 2. Consultas Otimizadas
- Índices específicos para consultas de disponibilidade
- Views materializadas para estatísticas de ocupação
- Cache de contadores de urnas disponíveis

### 3. Validações de Integridade
- Triggers para manter consistência gaveta ↔ urnas
- Validação de tipo_capacidade vs quantidade de urnas
- Prevenção de alteração de tipo após criação de dados

## Migração de Dados Existentes

### 1. Produtos ETENs Existentes
- Todos os produtos existentes recebem tipo='ETEN'
- Todos os blocos existentes recebem tipo_capacidade=1
- Nenhuma urna é criada para ETENs existentes

### 2. Script de Migração
```sql
-- Atualizar produtos existentes
UPDATE produtos SET tipo = 'ETEN' WHERE tipo IS NULL;

-- Atualizar blocos existentes
UPDATE blocos SET tipo_capacidade = 1 WHERE tipo_capacidade IS NULL;

-- Não criar urnas para ETENs existentes (comportamento mantido)
```

Esta implementação mantém total compatibilidade com o sistema atual de ETENs enquanto introduz a funcionalidade completa de ossuários com múltiplas urnas por gaveta.
