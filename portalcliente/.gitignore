# Dependencies
node_modules/
*/node_modules/

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.production
.env.staging
config/environments/production.env
config/environments/staging.env

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Backup files
backup-*/
*.backup

# Test files
test-*.js
debug-*.js
fix-*.js

# Temporary files
*.tmp
*.temp
