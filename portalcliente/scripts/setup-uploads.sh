#!/bin/bash

# Script para configurar diretório de uploads na VPS
# Executa na VPS antes do deploy

echo "🔧 Configurando diretório de uploads para Portal Evolution..."

# Definir diretório base
UPLOAD_DIR="/opt/portal-evolution/uploads"
LOGOS_DIR="$UPLOAD_DIR/logos"

# Criar diretórios se não existirem
echo "📁 Criando diretórios de upload..."
sudo mkdir -p "$LOGOS_DIR"

# Definir permissões corretas
echo "🔒 Configurando permissões..."
sudo chown -R 1001:1001 "$UPLOAD_DIR"
sudo chmod -R 755 "$UPLOAD_DIR"

# Verificar se os diretórios foram criados
if [ -d "$LOGOS_DIR" ]; then
    echo "✅ Diretório de logos criado: $LOGOS_DIR"
else
    echo "❌ Erro ao criar diretório de logos"
    exit 1
fi

# Listar estrutura criada
echo "📋 Estrutura de uploads criada:"
ls -la "$UPLOAD_DIR"

echo "🎉 Configuração de uploads concluída com sucesso!"
echo ""
echo "📝 Próximos passos:"
echo "   1. Execute o deploy do sistema"
echo "   2. Teste o upload de logos"
echo "   3. Verifique a persistência após restart"
