#!/bin/bash

# ===================================
# SCRIPT PARA PREPARAR TODOS OS SCRIPTS
# ===================================
# Execute: bash scripts/preparar-scripts.sh

echo "🔧 Preparando scripts do Portal Evolution..."

# Tornar todos os scripts executáveis
# Para verificar permissões: ls -la scripts/
chmod +x scripts/*.sh

echo "✅ Scripts preparados:"
echo ""

# Listar scripts com descrição
echo "📋 Scripts disponíveis:"
echo "   🚀 iniciar-servico.sh     - Iniciar todos os serviços"
echo "   💾 backup-dados.sh        - Fazer backup completo"
echo "   🔄 atualizar-containers.sh - Atualizar sistema"
echo "   📊 monitorar-servico.sh   - Monitorar status"
echo "   🔧 preparar-scripts.sh    - Este script"

echo ""
echo "🎯 Para usar:"
echo "   bash scripts/iniciar-servico.sh"
echo "   bash scripts/backup-dados.sh"
echo "   bash scripts/atualizar-containers.sh"
echo "   bash scripts/monitorar-servico.sh"

echo ""
echo "📊 Para monitoramento contínuo:"
echo "   watch -n 30 'bash scripts/monitorar-servico.sh'"

echo ""
echo "✅ Todos os scripts estão prontos para uso!"
