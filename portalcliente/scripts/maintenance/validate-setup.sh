#!/bin/bash

# ===================================
# VALIDAR CONFIGURAÇÃO DO PORTAL EVOLUTION
# ===================================
# Script para validar se tudo está configurado corretamente
# Execute: bash validate-setup.sh

set -e

echo "🔍 Validando configuração do Portal Evolution..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Contadores
ERRORS=0
WARNINGS=0
SUCCESS=0

# Função para log
log() {
    echo -e "${GREEN}✅ $1${NC}"
    ((SUCCESS++))
}

warn() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
    ((WARNINGS++))
}

error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
    ((ERRORS++))
}

info() {
    echo -e "${BLUE}ℹ️  INFO: $1${NC}"
}

echo "================================"

# 1. Verificar Docker
info "Verificando Docker..."
if command -v docker &> /dev/null; then
    log "Docker está instalado"
    
    # Verificar se Docker está rodando
    if docker info &> /dev/null; then
        log "Docker está rodando"
    else
        error "Docker não está rodando"
    fi
else
    error "Docker não está instalado"
fi

# 2. Verificar Docker Swarm
info "Verificando Docker Swarm..."
if docker info | grep -q "Swarm: active"; then
    log "Docker Swarm está ativo"
else
    error "Docker Swarm não está ativo. Execute: docker swarm init"
fi

# 3. Verificar rede redeinterna
info "Verificando rede redeinterna..."
if docker network ls | grep -q "redeinterna"; then
    log "Rede 'redeinterna' existe"
else
    error "Rede 'redeinterna' não existe. Verifique configuração do Traefik"
fi

# 4. Verificar Traefik
info "Verificando Traefik..."
if docker service ls | grep -q "traefik_traefik"; then
    log "Traefik está rodando"
    
    # Verificar se Traefik está respondendo
    if curl -f -s http://localhost > /dev/null 2>&1; then
        log "Traefik está respondendo"
    else
        warn "Traefik não está respondendo na porta 80"
    fi
else
    error "Traefik não está rodando"
fi

# 5. Verificar arquivo de configuração
info "Verificando configuração..."
if [[ -f "configuracao.env" ]]; then
    log "Arquivo configuracao.env existe"
    
    # Carregar configurações
    source configuracao.env
    
    # Verificar configurações obrigatórias
    if [[ -n "$DOMAIN" ]]; then
        log "DOMAIN configurado: $DOMAIN"
    else
        error "DOMAIN não configurado"
    fi
    
    if [[ -n "$DB_PASSWORD" && "$DB_PASSWORD" != "ALTERE_ESTA_SENHA_PARA_UMA_SEGURA" ]]; then
        log "DB_PASSWORD configurado"
    else
        error "DB_PASSWORD não configurado ou usando valor padrão"
    fi
    
    if [[ -n "$JWT_SECRET" && "$JWT_SECRET" != "ALTERE_ESTA_CHAVE_JWT_PARA_UMA_SEGURA" ]]; then
        log "JWT_SECRET configurado"
    else
        error "JWT_SECRET não configurado ou usando valor padrão"
    fi
    
    if [[ -n "$SSL_EMAIL" ]]; then
        log "SSL_EMAIL configurado: $SSL_EMAIL"
    else
        warn "SSL_EMAIL não configurado"
    fi
    
else
    error "Arquivo configuracao.env não encontrado"
    info "Execute: cp configuracao.env.example configuracao.env"
fi

# 6. Verificar arquivos necessários
info "Verificando arquivos necessários..."
required_files=(
    "docker-compose.prod.yml"
    "Dockerfile.backend"
    "Dockerfile.frontend"
    "nginx-traefik.conf"
    "deploy-traefik.sh"
    "monitor.sh"
    "remove-stack.sh"
)

for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        log "Arquivo $file existe"
    else
        error "Arquivo $file não encontrado"
    fi
done

# 7. Verificar estrutura de pastas
info "Verificando estrutura de pastas..."
required_dirs=(
    "src"
    "server"
    "public"
)

for dir in "${required_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
        log "Diretório $dir existe"
    else
        error "Diretório $dir não encontrado"
    fi
done

# 8. Verificar se domínio resolve
if [[ -n "$DOMAIN" ]]; then
    info "Verificando resolução DNS..."
    if nslookup "$DOMAIN" &> /dev/null; then
        log "Domínio $DOMAIN resolve"
    else
        warn "Domínio $DOMAIN não resolve ou DNS não configurado"
    fi
fi

# 9. Verificar portas em uso
info "Verificando portas..."
if netstat -tuln 2>/dev/null | grep -q ":80 "; then
    log "Porta 80 está em uso (Traefik)"
else
    warn "Porta 80 não está em uso"
fi

if netstat -tuln 2>/dev/null | grep -q ":443 "; then
    log "Porta 443 está em uso (Traefik)"
else
    warn "Porta 443 não está em uso"
fi

# Resumo
echo ""
echo "================================"
echo "📊 RESUMO DA VALIDAÇÃO"
echo "================================"
echo -e "✅ Sucessos: ${GREEN}$SUCCESS${NC}"
echo -e "⚠️  Avisos: ${YELLOW}$WARNINGS${NC}"
echo -e "❌ Erros: ${RED}$ERRORS${NC}"
echo ""

if [[ $ERRORS -eq 0 ]]; then
    echo -e "${GREEN}🎉 Configuração válida! Pronto para deploy.${NC}"
    echo ""
    echo "Execute: bash deploy-traefik.sh"
elif [[ $ERRORS -le 2 ]]; then
    echo -e "${YELLOW}⚠️  Configuração com problemas menores.${NC}"
    echo "Corrija os erros antes do deploy."
else
    echo -e "${RED}❌ Configuração com problemas graves.${NC}"
    echo "Corrija todos os erros antes de prosseguir."
fi

echo ""
echo "📋 Próximos passos:"
echo "1. Corrigir erros encontrados"
echo "2. bash deploy-traefik.sh"
echo "3. bash monitor.sh"
