#!/bin/bash

# ===================================
# SCRIPT DE VALIDAÇÃO DE PRODUÇÃO
# ===================================
# Valida se o sistema está funcionando corretamente

set -e

echo "🔍 ====================================="
echo "🔍 VALIDAÇÃO DO SISTEMA EM PRODUÇÃO"
echo "🔍 Portal Evolution"
echo "🔍 ====================================="

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para teste com resultado
test_with_result() {
    local test_name="$1"
    local command="$2"
    local expected="$3"
    
    log "🧪 Testando: $test_name"
    
    if eval "$command" > /dev/null 2>&1; then
        log "✅ $test_name: PASSOU"
        return 0
    else
        log "❌ $test_name: FALHOU"
        return 1
    fi
}

# Contador de testes
TOTAL_TESTS=0
PASSED_TESTS=0

# 1. TESTE DE DOCKER SERVICES
log "🐳 Verificando Docker Services..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if docker stack services portal-evolution | grep -q "1/1"; then
    log "✅ Docker Services: FUNCIONANDO"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Docker Services: PROBLEMA"
fi

# 2. TESTE DE API HEALTH
log "🔌 Verificando API Health..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if curl -s https://portal.evo-eden.site/api/health | grep -q "OK"; then
    log "✅ API Health: FUNCIONANDO"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ API Health: PROBLEMA"
fi

# 3. TESTE DE FRONTEND
log "🌐 Verificando Frontend..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if curl -s https://portal.evo-eden.site | grep -q "Portal Evolution"; then
    log "✅ Frontend: FUNCIONANDO"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Frontend: PROBLEMA"
fi

# 4. TESTE DE LOGS SYSTEM
log "📋 Verificando Sistema de Logs..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if curl -s https://logs.portal.evo-eden.site | grep -q "Portal Evolution"; then
    log "✅ Sistema de Logs: FUNCIONANDO"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Sistema de Logs: PROBLEMA"
fi

# 5. TESTE DE ESTRUTURA DE ARQUIVOS
log "📁 Verificando Estrutura de Arquivos..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ -d "docker/production" ] && [ -d "scripts/deploy" ] && [ -d "config/environments" ]; then
    log "✅ Estrutura de Arquivos: ORGANIZADA"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Estrutura de Arquivos: PROBLEMA"
fi

# 6. TESTE DE SCRIPTS DE DEPLOY
log "🚀 Verificando Scripts de Deploy..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ -x "scripts/deploy/build-production.sh" ] && [ -x "scripts/deploy/deploy-production.sh" ]; then
    log "✅ Scripts de Deploy: DISPONÍVEIS"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Scripts de Deploy: PROBLEMA"
fi

# 7. TESTE DE CONFIGURAÇÕES DE SEGURANÇA
log "🔒 Verificando Configurações de Segurança..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ -f ".gitignore" ] && [ -f ".dockerignore" ] && [ -f "config/environments/.env.example" ]; then
    log "✅ Configurações de Segurança: IMPLEMENTADAS"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Configurações de Segurança: PROBLEMA"
fi

# 8. TESTE DE DOCUMENTAÇÃO
log "📚 Verificando Documentação..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if [ -f "README.md" ] && [ -f "RELATORIO_REORGANIZACAO_FINAL.md" ] && [ -d "docs" ]; then
    log "✅ Documentação: COMPLETA"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Documentação: INCOMPLETA"
fi

# 9. TESTE DE IMAGENS DOCKER
log "🐳 Verificando Imagens Docker..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if docker images | grep -q "portal-evolution-backend" && docker images | grep -q "portal-evolution-frontend"; then
    log "✅ Imagens Docker: DISPONÍVEIS"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Imagens Docker: PROBLEMA"
fi

# 10. TESTE DE FUNCIONALIDADE ESQUECI MINHA SENHA
log "📧 Verificando Funcionalidade Esqueci Minha Senha..."
TOTAL_TESTS=$((TOTAL_TESTS + 1))
if curl -s -X POST https://portal.evo-eden.site/api/auth/forgot-password \
   -H "Content-Type: application/json" \
   -d '{"email":"<EMAIL>"}' | grep -q "error\|message"; then
    log "✅ Esqueci Minha Senha: FUNCIONANDO"
    PASSED_TESTS=$((PASSED_TESTS + 1))
else
    log "❌ Esqueci Minha Senha: PROBLEMA"
fi

# RESUMO DOS TESTES
log "📊 ====================================="
log "📊 RESUMO DOS TESTES"
log "📊 ====================================="

PERCENTAGE=$((PASSED_TESTS * 100 / TOTAL_TESTS))

log "✅ Testes aprovados: $PASSED_TESTS/$TOTAL_TESTS ($PERCENTAGE%)"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    log "🎉 TODOS OS TESTES PASSARAM!"
    log "🎉 Sistema está 100% funcional"
    
    # INFORMAÇÕES ADICIONAIS
    log "📋 ====================================="
    log "📋 INFORMAÇÕES DO SISTEMA"
    log "📋 ====================================="
    
    # Versão das imagens
    BACKEND_IMAGE=$(docker stack services portal-evolution --format "table {{.Image}}" | grep backend | head -1)
    FRONTEND_IMAGE=$(docker stack services portal-evolution --format "table {{.Image}}" | grep frontend | head -1)
    
    log "🐳 Backend Image: $BACKEND_IMAGE"
    log "🐳 Frontend Image: $FRONTEND_IMAGE"
    
    # URLs do sistema
    log "🌐 URLs do Sistema:"
    log "   - Frontend: https://portal.evo-eden.site"
    log "   - API: https://portal.evo-eden.site/api"
    log "   - Logs: https://logs.portal.evo-eden.site"
    log "   - Health Check: https://portal.evo-eden.site/api/health"
    
    # Comandos úteis
    log "🛠️ Comandos Úteis:"
    log "   - Ver serviços: docker stack services portal-evolution"
    log "   - Ver logs backend: docker service logs portal-evolution_portal_backend"
    log "   - Ver logs frontend: docker service logs portal-evolution_portal_frontend"
    log "   - Build novo: ./scripts/deploy/build-production.sh"
    log "   - Deploy novo: ./scripts/deploy/deploy-production.sh"
    
    exit 0
else
    log "⚠️ $((TOTAL_TESTS - PASSED_TESTS)) teste(s) falharam"
    log "⚠️ Sistema pode ter problemas"
    
    log "🔧 Ações Recomendadas:"
    log "   1. Verificar logs dos serviços"
    log "   2. Verificar conectividade de rede"
    log "   3. Verificar configurações"
    log "   4. Executar rebuild se necessário"
    
    exit 1
fi
