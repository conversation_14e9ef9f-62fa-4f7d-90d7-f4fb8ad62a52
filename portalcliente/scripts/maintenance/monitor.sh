#!/bin/bash

# ===================================
# MONITOR PORTAL EVOLUTION
# ===================================
# Script para monitorar a stack do Portal Evolution
# Execute: bash monitor.sh

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Carregar configurações se existir
if [[ -f "configuracao.env" ]]; then
    source configuracao.env
fi

echo "🔍 Monitor do Portal Evolution"
echo "================================"

# Verificar se stack existe
if ! docker stack ls | grep -q "portal-evolution"; then
    error "Stack 'portal-evolution' não encontrada!"
    echo "Execute: bash deploy-traefik.sh"
    exit 1
fi

# Status dos serviços
echo ""
log "📊 Status dos Serviços:"
docker stack services portal-evolution

echo ""
log "🐳 Containers em execução:"
docker stack ps portal-evolution --no-trunc

# Testar conectividade
echo ""
log "🌐 Testando conectividade:"

if [[ -n "$DOMAIN" ]]; then
    # Testar HTTPS
    if curl -f -s -I https://$DOMAIN > /dev/null 2>&1; then
        echo -e "✅ Frontend HTTPS: ${GREEN}https://$DOMAIN${NC}"
    else
        echo -e "❌ Frontend HTTPS: ${RED}https://$DOMAIN${NC}"
    fi
    
    # Testar API
    if curl -f -s https://$DOMAIN/api/health > /dev/null 2>&1; then
        echo -e "✅ Backend API: ${GREEN}https://$DOMAIN/api/health${NC}"
    else
        echo -e "❌ Backend API: ${RED}https://$DOMAIN/api/health${NC}"
    fi
else
    warn "DOMAIN não configurado em configuracao.env"
fi

# Recursos utilizados
echo ""
log "💾 Uso de recursos:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $(docker ps --filter "label=com.docker.stack.namespace=portal-evolution" -q) 2>/dev/null || echo "Nenhum container encontrado"

# Logs recentes
echo ""
log "📝 Logs recentes (últimas 10 linhas):"
echo ""
echo -e "${BLUE}=== Backend ===${NC}"
docker service logs --tail 10 portal-evolution_portal_backend 2>/dev/null || echo "Serviço backend não encontrado"

echo ""
echo -e "${BLUE}=== Frontend ===${NC}"
docker service logs --tail 10 portal-evolution_portal_frontend 2>/dev/null || echo "Serviço frontend não encontrado"

echo ""
echo -e "${BLUE}=== Database ===${NC}"
docker service logs --tail 10 portal-evolution_portal_database 2>/dev/null || echo "Serviço database não encontrado"

# Menu de opções
echo ""
echo "================================"
echo "Opções disponíveis:"
echo "1. Ver logs completos do backend"
echo "2. Ver logs completos do frontend"
echo "3. Ver logs completos do database"
echo "4. Reiniciar serviços"
echo "5. Escalar serviços"
echo "6. Sair"
echo ""

read -p "Escolha uma opção (1-6): " choice

case $choice in
    1)
        echo ""
        log "📝 Logs completos do backend:"
        docker service logs -f portal-evolution_portal_backend
        ;;
    2)
        echo ""
        log "📝 Logs completos do frontend:"
        docker service logs -f portal-evolution_portal_frontend
        ;;
    3)
        echo ""
        log "📝 Logs completos do database:"
        docker service logs -f portal-evolution_portal_database
        ;;
    4)
        echo ""
        log "🔄 Reiniciando serviços..."
        docker service update --force portal-evolution_portal_backend
        docker service update --force portal-evolution_portal_frontend
        log "✅ Serviços reiniciados!"
        ;;
    5)
        echo ""
        echo "Serviços disponíveis:"
        echo "1. Backend"
        echo "2. Frontend"
        read -p "Qual serviço escalar? (1-2): " service_choice
        read -p "Quantas réplicas? (1-5): " replicas
        
        case $service_choice in
            1)
                docker service scale portal-evolution_portal_backend=$replicas
                ;;
            2)
                docker service scale portal-evolution_portal_frontend=$replicas
                ;;
            *)
                error "Opção inválida!"
                ;;
        esac
        ;;
    6)
        log "👋 Saindo do monitor..."
        exit 0
        ;;
    *)
        error "Opção inválida!"
        ;;
esac
