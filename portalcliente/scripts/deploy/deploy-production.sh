#!/bin/bash

# Deploy script para produção
set -e

echo "🚀 Iniciando deploy para produção..."

# Verificar se está no diretório correto
if [ ! -f "deploy/production/docker-compose.prod.yml" ]; then
    echo "❌ Erro: docker-compose.prod.yml não encontrado"
    exit 1
fi

# Parar stack atual
echo "🛑 Parando stack atual..."
docker stack rm portal-evolution || true
sleep 30

# Deploy nova versão
echo "🚀 Fazendo deploy..."
docker stack deploy -c deploy/production/docker-compose.prod.yml portal-evolution

# Aguardar inicialização
echo "⏳ Aguardando inicialização..."
sleep 60

# Verificar status
echo "🔍 Verificando status..."
docker stack services portal-evolution

echo "✅ Deploy concluído!"
