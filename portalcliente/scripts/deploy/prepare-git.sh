#!/bin/bash

# ===================================
# SCRIPT DE PREPARAÇÃO PARA GIT
# ===================================
# Prepara o projeto para versionamento no Git

set -e

echo "🚀 ====================================="
echo "🚀 PREPARAÇÃO PARA GIT"
echo "🚀 Portal Evolution"
echo "🚀 ====================================="

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 1. VERIFICAR SE ESTÁ NO DIRETÓRIO CORRETO
if [ ! -f "package.json" ]; then
    echo "❌ Erro: Execute este script no diretório portalcliente"
    exit 1
fi

# 2. INICIALIZAR GIT SE NECESSÁRIO
if [ ! -d ".git" ]; then
    log "📁 Inicializando repositório Git..."
    git init
    log "✅ Repositório Git inicializado"
else
    log "✅ Repositório Git já existe"
fi

# 3. CONFIGURAR USUÁRIO GIT (se não configurado)
if [ -z "$(git config user.name)" ]; then
    log "👤 Configurando usuário Git..."
    git config user.name "Portal Evolution Team"
    git config user.email "<EMAIL>"
    log "✅ Usuário Git configurado"
fi

# 4. ADICIONAR REMOTE ORIGIN (se não existir)
if ! git remote get-url origin > /dev/null 2>&1; then
    log "🔗 Configurando remote origin..."
    echo "Para configurar o remote origin, execute:"
    echo "git remote add origin https://github.com/SEU_USUARIO/portalevo.git"
    echo "ou"
    echo "git remote <NAME_EMAIL>:SEU_USUARIO/portalevo.git"
else
    log "✅ Remote origin já configurado: $(git remote get-url origin)"
fi

# 5. CRIAR BRANCHES PRINCIPAIS
log "🌿 Criando estrutura de branches..."

# Branch main (se não existir)
if ! git show-ref --verify --quiet refs/heads/main; then
    git checkout -b main 2>/dev/null || git checkout main
    log "✅ Branch main criada/selecionada"
fi

# Branch develop (se não existir)
if ! git show-ref --verify --quiet refs/heads/develop; then
    git checkout -b develop 2>/dev/null || true
    log "✅ Branch develop criada"
fi

# Branch production (se não existir)
if ! git show-ref --verify --quiet refs/heads/production; then
    git checkout -b production 2>/dev/null || true
    log "✅ Branch production criada"
fi

# Voltar para main
git checkout main

# 6. ADICIONAR ARQUIVOS AO STAGING
log "📦 Adicionando arquivos ao staging..."
git add .

# 7. VERIFICAR STATUS
log "📊 Status do repositório:"
git status --short

# 8. CRIAR COMMIT INICIAL (se necessário)
if [ -z "$(git log --oneline 2>/dev/null)" ]; then
    log "💾 Criando commit inicial..."
    git commit -m "feat: estrutura inicial do Portal Evolution

- Sistema de gestão de sepultamentos
- Frontend React + Material-UI
- Backend Node.js + Express
- Banco PostgreSQL
- Docker Swarm para produção
- Sistema de logs integrado
- Funcionalidade de recuperação de senha
- Estrutura organizada para produção"
    log "✅ Commit inicial criado"
else
    log "ℹ️ Repositório já possui commits"
fi

# 9. CRIAR TAGS DE VERSÃO
log "🏷️ Criando tag de versão..."
VERSION="v1.0.0-production-ready"
if ! git tag -l | grep -q "$VERSION"; then
    git tag -a "$VERSION" -m "Versão 1.0.0 - Pronto para produção

- Sistema completamente funcional
- Estrutura reorganizada
- Docker otimizado
- Segurança implementada
- Testes validados"
    log "✅ Tag $VERSION criada"
else
    log "ℹ️ Tag $VERSION já existe"
fi

# 10. CRIAR ARQUIVO DE VERSÃO
log "📝 Criando arquivo de versão..."
cat > version.txt << EOF
Portal Evolution
Versão: 1.0.0
Build: $(date +%Y%m%d-%H%M%S)
Commit: $(git rev-parse --short HEAD 2>/dev/null || echo "inicial")
Data: $(date '+%Y-%m-%d %H:%M:%S')
Status: Production Ready
EOF

# 11. CRIAR CHANGELOG
log "📝 Criando CHANGELOG..."
cat > CHANGELOG.md << 'EOF'
# Changelog

Todas as mudanças notáveis neste projeto serão documentadas neste arquivo.

## [1.0.0] - 2025-06-19

### Adicionado
- Sistema completo de gestão de sepultamentos
- Interface React com Material-UI
- API REST com Node.js e Express
- Integração com PostgreSQL
- Sistema de autenticação JWT
- Recuperação de senha por email
- Dashboard com métricas
- Sistema de relatórios com PDF
- Logs centralizados
- Deploy automatizado com Docker Swarm

### Estrutura
- Frontend organizado em componentes reutilizáveis
- Backend com arquitetura modular
- Banco de dados normalizado
- Docker multi-stage builds
- Scripts de deploy automatizados
- Configuração por ambiente
- Documentação completa

### Segurança
- Autenticação JWT
- Senhas criptografadas
- Variáveis de ambiente protegidas
- Validação de entrada
- Logs de auditoria

### Performance
- Imagens Docker otimizadas
- Cache de dependências
- Compressão de assets
- Health checks
- Monitoramento integrado
EOF

# 12. INSTRUÇÕES FINAIS
log "🎉 ====================================="
log "🎉 PREPARAÇÃO PARA GIT CONCLUÍDA!"
log "🎉 ====================================="

echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo ""
echo "1. 🔗 Configurar remote origin:"
echo "   git remote add origin https://github.com/SEU_USUARIO/portalevo.git"
echo ""
echo "2. 🚀 Fazer push inicial:"
echo "   git push -u origin main"
echo "   git push origin develop"
echo "   git push origin production"
echo "   git push --tags"
echo ""
echo "3. 🌿 Workflow de desenvolvimento:"
echo "   - main: branch principal (produção)"
echo "   - develop: desenvolvimento"
echo "   - production: deploy em produção"
echo ""
echo "4. 📦 Para novos deploys:"
echo "   git checkout develop"
echo "   # fazer mudanças"
echo "   git add ."
echo "   git commit -m 'feat: nova funcionalidade'"
echo "   git checkout main"
echo "   git merge develop"
echo "   ./scripts/deploy/build-production.sh"
echo "   ./scripts/deploy/deploy-production.sh"
echo ""
echo "5. 🏷️ Para criar releases:"
echo "   git tag -a v1.1.0 -m 'Versão 1.1.0'"
echo "   git push --tags"
echo ""
echo "✅ Sistema pronto para versionamento e colaboração!"
