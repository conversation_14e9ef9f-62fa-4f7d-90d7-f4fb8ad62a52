#!/bin/bash

# Build script para produção
set -e

echo "🔨 Iniciando build para produção..."

# Gerar timestamp único
TIMESTAMP=$(date +%s)
echo "🏷️ Timestamp: $TIMESTAMP"

# Build backend
echo "🔨 Building backend..."
docker build -f docker/production/Dockerfile.backend -t portal-evolution-backend:$TIMESTAMP .

# Build frontend  
echo "🔨 Building frontend..."
docker build -f docker/production/Dockerfile.frontend -t portal-evolution-frontend:$TIMESTAMP .

# Build logs
echo "🔨 Building logs..."
docker build -f docker/production/Dockerfile.logs -t portal-evolution-logs:$TIMESTAMP .

# Atualizar docker-compose com novas imagens
echo "📝 Atualizando docker-compose..."
sed -i "s/portal-evolution-backend:[0-9]*/portal-evolution-backend:$TIMESTAMP/g" deploy/production/docker-compose.prod.yml
sed -i "s/portal-evolution-frontend:[0-9]*/portal-evolution-frontend:$TIMESTAMP/g" deploy/production/docker-compose.prod.yml
sed -i "s/portal-evolution-logs:[0-9]*/portal-evolution-logs:$TIMESTAMP/g" deploy/production/docker-compose.prod.yml

echo "✅ Build concluído com timestamp: $TIMESTAMP"
