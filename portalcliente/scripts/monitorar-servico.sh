#!/bin/bash

# ===================================
# SCRIPT DE MONITORAMENTO - PORTAL EVOLUTION
# ===================================
# Execute: bash scripts/monitorar-servico.sh
# Para monitoramento contínuo: watch -n 30 'bash scripts/monitorar-servico.sh'

echo "📊 Monitor Portal Evolution - $(date)"
echo "========================================"

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# ===================================
# STATUS DOS CONTAINERS
# ===================================
echo ""
echo -e "${BLUE}🐳 STATUS DOS CONTAINERS${NC}"
echo "----------------------------------------"

# Verificar se containers estão rodando
# Para verificar manualmente: docker ps | grep portal
if docker ps --filter "name=portal-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q portal; then
    docker ps --filter "name=portal-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
else
    echo -e "${RED}❌ Nenhum container do portal rodando${NC}"
fi

# ===================================
# SAÚDE DOS SERVIÇOS
# ===================================
echo ""
echo -e "${BLUE}🏥 SAÚDE DOS SERVIÇOS${NC}"
echo "----------------------------------------"

# Verificar banco de dados
# Para testar: docker exec portal-database pg_isready -U portal_user
if docker ps | grep -q portal-database; then
    if docker exec portal-database pg_isready -U portal_user > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Banco de dados: ONLINE${NC}"
        
        # Verificar conexões ativas
        # Para verificar: docker exec portal-database psql -U portal_user -d portal_evolution -c "SELECT count(*) FROM pg_stat_activity;"
        CONNECTIONS=$(docker exec portal-database psql -U portal_user -d portal_evolution -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" 2>/dev/null | tr -d ' ' || echo "N/A")
        echo "   Conexões ativas: $CONNECTIONS"
    else
        echo -e "${RED}❌ Banco de dados: ERRO${NC}"
    fi
else
    echo -e "${RED}❌ Container do banco não está rodando${NC}"
fi

# Verificar backend
# Para testar: curl -s http://localhost:3001/api/health
if curl -f -s http://localhost:3001/api/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend: ONLINE${NC}"
    
    # Verificar tempo de resposta
    # Para testar: time curl -s http://localhost:3001/api/health
    RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:3001/api/health 2>/dev/null || echo "N/A")
    echo "   Tempo de resposta: ${RESPONSE_TIME}s"
else
    echo -e "${RED}❌ Backend: OFFLINE${NC}"
fi

# Verificar frontend
# Para testar: curl -s http://localhost/health
if curl -f -s http://localhost/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend: ONLINE${NC}"
    
    # Verificar tempo de resposta
    FRONTEND_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost/health 2>/dev/null || echo "N/A")
    echo "   Tempo de resposta: ${FRONTEND_TIME}s"
else
    echo -e "${RED}❌ Frontend: OFFLINE${NC}"
fi

# ===================================
# RECURSOS DO SISTEMA
# ===================================
echo ""
echo -e "${BLUE}💻 RECURSOS DO SISTEMA${NC}"
echo "----------------------------------------"

# Memória
# Para verificar: free -h
echo "💾 Memória:"
free -h | grep -E "Mem|Swap" | while read line; do
    echo "   $line"
done

# Disco
# Para verificar: df -h
echo ""
echo "💿 Disco:"
df -h / | tail -1 | awk '{print "   Usado: " $3 " / " $2 " (" $5 ")"}'

# CPU Load
# Para verificar: uptime
echo ""
echo "⚡ CPU Load:"
uptime | awk -F'load average:' '{print "   " $2}'

# ===================================
# RECURSOS DOS CONTAINERS
# ===================================
echo ""
echo -e "${BLUE}🐳 RECURSOS DOS CONTAINERS${NC}"
echo "----------------------------------------"

# Verificar se há containers rodando
if docker ps --filter "name=portal-" -q | head -1 > /dev/null; then
    # Para verificar: docker stats --no-stream
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" | grep portal
else
    echo "Nenhum container do portal rodando"
fi

# ===================================
# LOGS RECENTES
# ===================================
echo ""
echo -e "${BLUE}📝 LOGS RECENTES (últimas 5 linhas)${NC}"
echo "----------------------------------------"

# Logs do backend
if docker ps | grep -q portal-backend; then
    echo "🔧 Backend:"
    docker logs portal-backend --tail 5 2>/dev/null | sed 's/^/   /' || echo "   Erro ao obter logs"
fi

# Logs do frontend
if docker ps | grep -q portal-frontend; then
    echo ""
    echo "🖥️  Frontend:"
    docker logs portal-frontend --tail 5 2>/dev/null | sed 's/^/   /' || echo "   Erro ao obter logs"
fi

# ===================================
# CONECTIVIDADE
# ===================================
echo ""
echo -e "${BLUE}🌐 CONECTIVIDADE${NC}"
echo "----------------------------------------"

# Verificar portas
# Para verificar: ss -tlnp | grep -E ":80|:3001|:5432"
echo "🔌 Portas em uso:"
ss -tlnp 2>/dev/null | grep -E ":80|:3001|:5432" | while read line; do
    echo "   $line"
done

# Verificar IP público
# Para verificar: curl ifconfig.me
echo ""
echo "🌍 IP Público:"
PUBLIC_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || echo "Não disponível")
echo "   $PUBLIC_IP"

# ===================================
# ESPAÇO EM DISCO DOS VOLUMES
# ===================================
echo ""
echo -e "${BLUE}📁 VOLUMES DOCKER${NC}"
echo "----------------------------------------"

# Verificar volumes do portal
# Para verificar: docker volume ls | grep portal
if docker volume ls | grep -q portal; then
    echo "📦 Volumes do Portal:"
    docker volume ls | grep portal | while read driver name; do
        SIZE=$(docker system df -v | grep "$name" | awk '{print $3}' 2>/dev/null || echo "N/A")
        echo "   $name: $SIZE"
    done
else
    echo "Nenhum volume do portal encontrado"
fi

# ===================================
# RESUMO E ALERTAS
# ===================================
echo ""
echo -e "${BLUE}⚠️  ALERTAS E RECOMENDAÇÕES${NC}"
echo "----------------------------------------"

# Verificar uso de disco
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [[ $DISK_USAGE -gt 80 ]]; then
    echo -e "${RED}⚠️  Disco com uso alto: ${DISK_USAGE}%${NC}"
fi

# Verificar uso de memória
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [[ $MEM_USAGE -gt 80 ]]; then
    echo -e "${RED}⚠️  Memória com uso alto: ${MEM_USAGE}%${NC}"
fi

# Verificar containers parados
STOPPED_CONTAINERS=$(docker ps -a --filter "name=portal-" --filter "status=exited" -q | wc -l)
if [[ $STOPPED_CONTAINERS -gt 0 ]]; then
    echo -e "${YELLOW}⚠️  $STOPPED_CONTAINERS containers parados${NC}"
fi

# Verificar idade dos backups
if [[ -d "dados/backups" ]]; then
    LAST_BACKUP=$(find dados/backups -name "db_backup_*.sql" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    if [[ -n "$LAST_BACKUP" ]]; then
        BACKUP_AGE=$(find "$LAST_BACKUP" -mtime +1 2>/dev/null)
        if [[ -n "$BACKUP_AGE" ]]; then
            echo -e "${YELLOW}⚠️  Último backup tem mais de 1 dia${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Nenhum backup encontrado${NC}"
    fi
fi

echo ""
echo -e "${GREEN}📊 Monitoramento concluído - $(date)${NC}"
echo ""
echo "🔧 Comandos úteis:"
echo "   Logs em tempo real: docker-compose -f portal-evolution.yaml logs -f"
echo "   Reiniciar serviços: docker-compose -f portal-evolution.yaml restart"
echo "   Backup manual: bash scripts/backup-dados.sh"
echo "   Atualizar: bash scripts/atualizar-containers.sh"
