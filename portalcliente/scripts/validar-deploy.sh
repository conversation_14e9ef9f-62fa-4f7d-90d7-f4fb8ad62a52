#!/bin/bash

# Script para validar se tudo está pronto para deploy
# Execute: bash validar-deploy.sh

echo "🔍 Validando configuração para deploy..."

# Cores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

success=0
warnings=0
errors=0

check_ok() {
    echo -e "${GREEN}✅ $1${NC}"
    ((success++))
}

check_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((warnings++))
}

check_error() {
    echo -e "${RED}❌ $1${NC}"
    ((errors++))
}

echo ""
echo "📋 Verificando arquivos obrigatórios..."

# Verificar Dockerfiles
if [[ -f "Dockerfile.frontend" ]]; then
    check_ok "Dockerfile.frontend encontrado"
else
    check_error "Dockerfile.frontend não encontrado"
fi

if [[ -f "Dockerfile.backend" ]]; then
    check_ok "Dockerfile.backend encontrado"
else
    check_error "Dockerfile.backend não encontrado"
fi

# Verificar docker-compose
if [[ -f "docker-compose.prod.yml" ]]; then
    check_ok "docker-compose.prod.yml encontrado"
else
    check_error "docker-compose.prod.yml não encontrado"
fi

# Verificar nginx.conf
if [[ -f "nginx.conf" ]]; then
    check_ok "nginx.conf encontrado"
else
    check_error "nginx.conf não encontrado"
fi

# Verificar configuração
if [[ -f "configuracao.env" ]]; then
    check_ok "configuracao.env encontrado"
    
    # Verificar se configurações foram alteradas
    if grep -q "SUA_SENHA_SEGURA_AQUI" configuracao.env; then
        check_warn "DB_PASSWORD ainda está com valor padrão"
    else
        check_ok "DB_PASSWORD foi alterado"
    fi
    
    if grep -q "SUA_CHAVE_JWT_MUITO_SEGURA_AQUI" configuracao.env; then
        check_warn "JWT_SECRET ainda está com valor padrão"
    else
        check_ok "JWT_SECRET foi alterado"
    fi
    
    # Verificar VPS_HOST
    if grep -q "VPS_HOST=************" configuracao.env; then
        check_ok "VPS_HOST configurado"
    else
        check_warn "VPS_HOST pode precisar ser ajustado"
    fi
    
else
    check_error "configuracao.env não encontrado"
fi

# Verificar scripts
echo ""
echo "📋 Verificando scripts..."

scripts=("deploy.sh" "atualizar.sh" "configurar-vps.sh")
for script in "${scripts[@]}"; do
    if [[ -f "$script" ]]; then
        check_ok "$script encontrado"
        if [[ -x "$script" ]]; then
            check_ok "$script é executável"
        else
            check_warn "$script não é executável (execute: chmod +x $script)"
        fi
    else
        check_error "$script não encontrado"
    fi
done

# Verificar estrutura do servidor
echo ""
echo "📋 Verificando estrutura do servidor..."

if [[ -d "server" ]]; then
    check_ok "Diretório server/ encontrado"
    
    if [[ -f "server/package.json" ]]; then
        check_ok "server/package.json encontrado"
    else
        check_error "server/package.json não encontrado"
    fi
    
    if [[ -f "server/index.js" ]]; then
        check_ok "server/index.js encontrado"
    else
        check_error "server/index.js não encontrado"
    fi
    
    if [[ -f "server/database/init.sql" ]]; then
        check_ok "server/database/init.sql encontrado"
    else
        check_warn "server/database/init.sql não encontrado"
    fi
else
    check_error "Diretório server/ não encontrado"
fi

# Verificar frontend
echo ""
echo "📋 Verificando frontend..."

if [[ -f "package.json" ]]; then
    check_ok "package.json encontrado"
else
    check_error "package.json não encontrado"
fi

if [[ -f "index.html" ]]; then
    check_ok "index.html encontrado"
else
    check_error "index.html não encontrado"
fi

if [[ -d "src" ]]; then
    check_ok "Diretório src/ encontrado"
else
    check_error "Diretório src/ não encontrado"
fi

# Verificar .dockerignore
if [[ -f ".dockerignore" ]]; then
    check_ok ".dockerignore encontrado"
else
    check_warn ".dockerignore não encontrado (recomendado)"
fi

# Resumo final
echo ""
echo "📊 RESUMO DA VALIDAÇÃO:"
echo -e "${GREEN}✅ Sucessos: $success${NC}"
echo -e "${YELLOW}⚠️  Avisos: $warnings${NC}"
echo -e "${RED}❌ Erros: $errors${NC}"

echo ""
if [[ $errors -eq 0 ]]; then
    echo -e "${GREEN}🎉 VALIDAÇÃO PASSOU! Sistema pronto para deploy.${NC}"
    echo ""
    echo "📋 Próximos passos:"
    echo "1. Configure sua VPS: bash configurar-vps.sh"
    echo "2. Edite configuracao.env com seus dados"
    echo "3. Execute deploy: bash deploy.sh"
    echo ""
    if [[ $warnings -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Há alguns avisos acima. Revise se necessário.${NC}"
    fi
else
    echo -e "${RED}❌ VALIDAÇÃO FALHOU! Corrija os erros antes de fazer deploy.${NC}"
    exit 1
fi
