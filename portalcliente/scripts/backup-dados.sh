#!/bin/bash

# ===================================
# SCRIPT DE BACKUP - PORTAL EVOLUTION
# ===================================
# Execute: bash scripts/backup-dados.sh
# Para verificar backups: ls -la dados/backups/

set -e

echo "💾 Backup Portal Evolution..."

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    exit 1
}

# Configurações
BACKUP_DIR="./dados/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=7

# Criar diretório de backup se não existir
# Para verificar: ls -la dados/
mkdir -p $BACKUP_DIR

log "Iniciando backup..."

# ===================================
# BACKUP DO BANCO DE DADOS
# ===================================
log "Fazendo backup do banco de dados..."

# Verificar se container do banco está rodando
# Para verificar: docker ps | grep portal-database
if ! docker ps | grep -q portal-database; then
    error "Container portal-database não está rodando!"
fi

# Fazer backup do banco
# Para verificar backup: head -20 dados/backups/db_backup_*.sql
DB_BACKUP_FILE="$BACKUP_DIR/db_backup_$DATE.sql"
if docker exec portal-database pg_dump -U portal_user portal_evolution > $DB_BACKUP_FILE; then
    log "✅ Backup do banco criado: $DB_BACKUP_FILE"
    
    # Verificar tamanho do backup
    # Para verificar: ls -lh dados/backups/db_backup_*.sql
    BACKUP_SIZE=$(du -h $DB_BACKUP_FILE | cut -f1)
    log "   Tamanho: $BACKUP_SIZE"
else
    error "❌ Erro ao fazer backup do banco"
fi

# ===================================
# BACKUP DOS UPLOADS
# ===================================
log "Fazendo backup dos uploads..."

if [[ -d "./dados/uploads" ]]; then
    UPLOADS_BACKUP_FILE="$BACKUP_DIR/uploads_backup_$DATE.tar.gz"
    
    # Criar backup compactado dos uploads
    # Para verificar: tar -tzf dados/backups/uploads_backup_*.tar.gz | head -10
    if tar -czf $UPLOADS_BACKUP_FILE -C ./dados uploads/; then
        log "✅ Backup dos uploads criado: $UPLOADS_BACKUP_FILE"
        
        # Verificar tamanho do backup
        UPLOADS_SIZE=$(du -h $UPLOADS_BACKUP_FILE | cut -f1)
        log "   Tamanho: $UPLOADS_SIZE"
    else
        warn "⚠️  Erro ao fazer backup dos uploads"
    fi
else
    warn "⚠️  Diretório de uploads não encontrado"
fi

# ===================================
# BACKUP DA CONFIGURAÇÃO
# ===================================
log "Fazendo backup da configuração..."

CONFIG_BACKUP_FILE="$BACKUP_DIR/config_backup_$DATE.tar.gz"

# Backup dos arquivos de configuração
# Para verificar: tar -tzf dados/backups/config_backup_*.tar.gz
if tar -czf $CONFIG_BACKUP_FILE .env docker/ scripts/; then
    log "✅ Backup da configuração criado: $CONFIG_BACKUP_FILE"
    
    CONFIG_SIZE=$(du -h $CONFIG_BACKUP_FILE | cut -f1)
    log "   Tamanho: $CONFIG_SIZE"
else
    warn "⚠️  Erro ao fazer backup da configuração"
fi

# ===================================
# LIMPEZA DE BACKUPS ANTIGOS
# ===================================
log "Limpando backups antigos (mais de $RETENTION_DAYS dias)..."

# Remover backups antigos
# Para verificar quais serão removidos: find dados/backups/ -name "*backup_*.sql" -mtime +7
REMOVED_COUNT=0

for pattern in "db_backup_*.sql" "uploads_backup_*.tar.gz" "config_backup_*.tar.gz"; do
    while IFS= read -r -d '' file; do
        rm "$file"
        log "   Removido: $(basename "$file")"
        ((REMOVED_COUNT++))
    done < <(find $BACKUP_DIR -name "$pattern" -mtime +$RETENTION_DAYS -print0 2>/dev/null)
done

if [[ $REMOVED_COUNT -gt 0 ]]; then
    log "✅ $REMOVED_COUNT backups antigos removidos"
else
    log "ℹ️  Nenhum backup antigo para remover"
fi

# ===================================
# RESUMO DO BACKUP
# ===================================
log "📊 Resumo do backup:"

# Listar backups atuais
# Para verificar todos: ls -lah dados/backups/
BACKUP_COUNT=$(ls -1 $BACKUP_DIR/*backup_*.* 2>/dev/null | wc -l)
TOTAL_SIZE=$(du -sh $BACKUP_DIR | cut -f1)

log "   📁 Total de backups: $BACKUP_COUNT"
log "   💾 Espaço usado: $TOTAL_SIZE"
log "   📅 Data do backup: $DATE"

# Verificar espaço em disco
# Para verificar: df -h /opt
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}')
log "   💿 Uso do disco: $DISK_USAGE"

log ""
log "🎉 Backup concluído com sucesso!"
log ""
log "📋 Arquivos criados:"
log "   🗄️  Banco: $DB_BACKUP_FILE"
if [[ -f "$UPLOADS_BACKUP_FILE" ]]; then
    log "   📁 Uploads: $UPLOADS_BACKUP_FILE"
fi
log "   ⚙️  Config: $CONFIG_BACKUP_FILE"

log ""
log "🔧 Para restaurar:"
log "   Banco: cat $DB_BACKUP_FILE | docker exec -i portal-database psql -U portal_user -d portal_evolution"
log "   Uploads: tar -xzf $UPLOADS_BACKUP_FILE -C ./dados/"
log "   Config: tar -xzf $CONFIG_BACKUP_FILE"

# Criar log de backup
echo "$(date): Backup concluído - DB: $DB_BACKUP_FILE, Config: $CONFIG_BACKUP_FILE" >> $BACKUP_DIR/backup.log
