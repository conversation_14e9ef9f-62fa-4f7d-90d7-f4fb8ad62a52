#!/bin/bash

# Script de limpeza do projeto Portal Evolution
# Remove arquivos desnecessários, duplicados e obsoletos

set -e

echo "🧹 Iniciando limpeza do projeto Portal Evolution..."

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Verificar se estamos no diretório correto
if [[ ! -f "package.json" ]]; then
    log "❌ Execute este script no diretório raiz do projeto (onde está o package.json)"
    exit 1
fi

log "✅ Diretório correto identificado"

# 1. REMOVER ARQUIVOS DE DEBUG E TESTE DA RAIZ
log "🗑️ Removendo arquivos de debug e teste da raiz..."

# Arquivos de debug HTML
rm -f debug-*.html 2>/dev/null || true
rm -f test*.html 2>/dev/null || true
rm -f teste-*.html 2>/dev/null || true
rm -f validacao-*.html 2>/dev/null || true
rm -f monitor-*.html 2>/dev/null || true
rm -f interceptor-*.html 2>/dev/null || true
rm -f resumo-final-*.html 2>/dev/null || true
rm -f confirmacao-final.html 2>/dev/null || true

# Arquivos de teste JavaScript
rm -f test-*.js 2>/dev/null || true
rm -f test-*.cjs 2>/dev/null || true
rm -f teste-*.cjs 2>/dev/null || true
rm -f debug-*.js 2>/dev/null || true
rm -f debug-*.cjs 2>/dev/null || true
rm -f fix-*.js 2>/dev/null || true
rm -f check-*.js 2>/dev/null || true
rm -f *test*.js 2>/dev/null || true

# Scripts de validação e teste
rm -f validacao-*.sh 2>/dev/null || true
rm -f teste-*.sh 2>/dev/null || true
rm -f test-*.sh 2>/dev/null || true
rm -f update-*.sh 2>/dev/null || true

log "✅ Arquivos de debug e teste removidos"

# 2. REMOVER ARQUIVOS DE MIGRAÇÃO E CORREÇÃO TEMPORÁRIOS
log "🗑️ Removendo arquivos de migração temporários..."

rm -f migrate.sql 2>/dev/null || true
rm -f fix-*.sql 2>/dev/null || true
rm -f rename-*.js 2>/dev/null || true
rm -f executar_*.js 2>/dev/null || true
rm -f verificacao-*.sql 2>/dev/null || true
rm -f diagnostico-*.js 2>/dev/null || true

log "✅ Arquivos de migração temporários removidos"

# 3. REMOVER MÚLTIPLOS SCRIPTS DE DEPLOY OBSOLETOS
log "🗑️ Removendo scripts de deploy obsoletos..."

# Manter apenas deploy-dev-only.sh e deploy.sh
find . -maxdepth 1 -name "deploy-*.sh" ! -name "deploy.sh" ! -name "deploy-dev-only.sh" -delete 2>/dev/null || true

log "✅ Scripts de deploy obsoletos removidos"

# 4. REMOVER ARQUIVOS DE DOCUMENTAÇÃO DUPLICADOS/OBSOLETOS
log "🗑️ Removendo documentação obsoleta..."

# Remover relatórios de execução antigos (manter apenas os essenciais)
rm -f ANALISE_*.md 2>/dev/null || true
rm -f CHANGELOG-*.md 2>/dev/null || true
rm -f CORRECAO_*.md 2>/dev/null || true
rm -f CORRECOES_*.md 2>/dev/null || true
rm -f GUIA-*.md 2>/dev/null || true
rm -f RELATORIO-*.md 2>/dev/null || true
rm -f RELATORIO_*.md 2>/dev/null || true
rm -f RESULTADO_*.md 2>/dev/null || true
rm -f RESUMO-*.md 2>/dev/null || true
rm -f TESTE-*.md 2>/dev/null || true
rm -f TESTE_*.md 2>/dev/null || true
rm -f TOGGLE_*.md 2>/dev/null || true
rm -f VALIDACAO_*.md 2>/dev/null || true

log "✅ Documentação obsoleta removida"

# 5. LIMPAR PASTA PUBLIC DE ARQUIVOS DE TESTE
log "🗑️ Limpando pasta public..."

if [[ -d "public" ]]; then
    rm -f public/debug-*.html 2>/dev/null || true
    rm -f public/test*.html 2>/dev/null || true
    rm -f public/teste-*.html 2>/dev/null || true
    rm -f public/validacao-*.html 2>/dev/null || true
    rm -f public/monitor-*.html 2>/dev/null || true
    rm -f public/interceptor-*.html 2>/dev/null || true
    rm -f public/resumo-final-*.html 2>/dev/null || true
    rm -f public/confirmacao-final.html 2>/dev/null || true
fi

log "✅ Pasta public limpa"

# 6. LIMPAR PASTA DIST (BUILD)
log "🗑️ Limpando pasta dist..."

if [[ -d "dist" ]]; then
    rm -rf dist/* 2>/dev/null || true
fi

log "✅ Pasta dist limpa"

# 7. REMOVER ARQUIVOS TEMPORÁRIOS E LOGS
log "🗑️ Removendo arquivos temporários..."

rm -f *.log 2>/dev/null || true
rm -f *.tmp 2>/dev/null || true
rm -f *.temp 2>/dev/null || true
rm -f *.backup 2>/dev/null || true
rm -f *.bak 2>/dev/null || true
rm -f downloaded-*.png 2>/dev/null || true
rm -f test-logo.png 2>/dev/null || true
rm -f version.txt 2>/dev/null || true

log "✅ Arquivos temporários removidos"

# 8. LIMPAR SCRIPTS DUPLICADOS NO SERVER
log "🗑️ Limpando scripts duplicados no server..."

if [[ -d "server" ]]; then
    rm -f server/test-*.js 2>/dev/null || true
    rm -f server/test-*.cjs 2>/dev/null || true
    rm -f server/debug-*.js 2>/dev/null || true
    rm -f server/debug-*.cjs 2>/dev/null || true
    rm -f server/check-*.js 2>/dev/null || true
    rm -f server/fix-*.js 2>/dev/null || true
    rm -f server/add-*.js 2>/dev/null || true
    rm -f server/update-*.js 2>/dev/null || true
    rm -f server/verify_*.js 2>/dev/null || true
    rm -f server/verificar-*.js 2>/dev/null || true
    rm -f server/*_test.js 2>/dev/null || true
    rm -f server/*_inspector.js 2>/dev/null || true
    rm -f server/migrate_*.js 2>/dev/null || true
    rm -f server/complete_*.js 2>/dev/null || true
    rm -f server/real_*.js 2>/dev/null || true
    rm -f server/simple_*.js 2>/dev/null || true
    rm -f server/success_*.js 2>/dev/null || true
    rm -f server/final_*.js 2>/dev/null || true
    rm -f server/direct_*.js 2>/dev/null || true
    rm -f server/run-*.js 2>/dev/null || true
    rm -f server/add_*.js 2>/dev/null || true
    rm -f server/add_*.sql 2>/dev/null || true
fi

log "✅ Scripts duplicados no server removidos"

# 9. REMOVER PASTA DE BACKUP ANTIGA
log "🗑️ Removendo backups antigos..."

rm -rf backup-reorganizacao-* 2>/dev/null || true

log "✅ Backups antigos removidos"

# 10. LIMPAR ARQUIVOS DE CONFIGURAÇÃO OBSOLETOS
log "🗑️ Removendo configurações obsoletas..."

rm -f reorganizar-para-producao.sh 2>/dev/null || true
rm -f preparar-scripts.sh 2>/dev/null || true
rm -f remove-stack.sh 2>/dev/null || true

log "✅ Configurações obsoletas removidas"

# 11. ESTATÍSTICAS FINAIS
log "📊 Calculando estatísticas..."

TOTAL_FILES=$(find . -type f | wc -l)
TOTAL_DIRS=$(find . -type d | wc -l)

log "✅ Limpeza concluída!"
log "📋 Estatísticas finais:"
log "   📁 Diretórios: $TOTAL_DIRS"
log "   📄 Arquivos: $TOTAL_FILES"

echo ""
echo "🎯 Projeto limpo e organizado!"
echo "✅ Arquivos desnecessários removidos"
echo "✅ Estrutura otimizada para produção"
echo ""
