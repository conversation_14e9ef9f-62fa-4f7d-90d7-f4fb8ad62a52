#!/bin/bash

# Script de deploy simples para VPS
# Execute: bash deploy.sh

set -e

echo "🚀 Iniciando deploy do Portal Evolution..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se arquivo de configuração existe
if [[ ! -f "configuracao.env" ]]; then
    error "Arquivo configuracao.env não encontrado! Configure primeiro."
fi

# Carregar configurações
source configuracao.env

# Validar configurações obrigatórias
if [[ -z "$VPS_HOST" ]]; then
    error "VPS_HOST não configurado em configuracao.env"
fi

if [[ -z "$VPS_USER" ]]; then
    error "VPS_USER não configurado em configuracao.env"
fi

log "Configurações carregadas:"
info "VPS: $VPS_USER@$VPS_HOST:$VPS_PORT"
info "Domínio: ${DOMAIN:-'Não configurado (usando IP)'}"
info "Banco: $DB_NAME"

# Verificar conexão SSH
log "Testando conexão SSH..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes -p $VPS_PORT $VPS_USER@$VPS_HOST exit 2>/dev/null; then
    error "Não foi possível conectar na VPS. Verifique SSH."
fi

log "Conexão SSH OK!"

# Fazer deploy
log "Iniciando deploy na VPS..."

ssh -p $VPS_PORT $VPS_USER@$VPS_HOST << 'ENDSSH'
    # Navegar para diretório do projeto
    cd /opt/portal-evolution || {
        echo "ERRO: Diretório /opt/portal-evolution não existe!"
        echo "Execute primeiro: bash configurar-vps-simples.sh"
        exit 1
    }

    # Verificar se código existe
    if [[ ! -d "portalcliente" ]]; then
        echo "ERRO: Código não encontrado!"
        echo "Execute primeiro: bash configurar-vps-simples.sh"
        exit 1
    fi
    
    # Navegar para diretório do portal
    cd portalcliente
    
    # Parar containers se estiverem rodando
    echo "Parando containers existentes..."
    docker-compose -f docker-compose.prod.yml down || true
    
    # Limpar imagens antigas
    echo "Limpando imagens antigas..."
    docker system prune -f
    
    # Build e iniciar containers
    echo "Construindo e iniciando containers..."
    docker-compose -f docker-compose.prod.yml --env-file configuracao.env up -d --build
    
    # Aguardar containers iniciarem
    echo "Aguardando containers iniciarem..."
    sleep 30
    
    # Verificar status
    echo "Verificando status dos containers..."
    docker-compose -f docker-compose.prod.yml ps
    
    # Testar saúde
    echo "Testando saúde da aplicação..."
    curl -f http://localhost/health || echo "Frontend não respondeu"
    curl -f http://localhost/api/health || echo "Backend não respondeu"
    
    echo "Deploy concluído!"
ENDSSH

log "Deploy finalizado!"

# Testar aplicação
log "Testando aplicação..."
sleep 10

if curl -f -s http://$VPS_HOST/health > /dev/null; then
    log "✅ Frontend funcionando: http://$VPS_HOST"
else
    warn "❌ Frontend não está respondendo"
fi

if curl -f -s http://$VPS_HOST/api/health > /dev/null; then
    log "✅ Backend funcionando: http://$VPS_HOST/api/health"
else
    warn "❌ Backend não está respondendo"
fi

log "🎉 Deploy concluído!"
log ""
log "📋 Acesse a aplicação:"
if [[ -n "$DOMAIN" ]]; then
    log "   🌐 Domínio: http://$DOMAIN"
fi
log "   🖥️  IP: http://$VPS_HOST"
log ""
log "👥 Credenciais padrão:"
log "   Admin: <EMAIL> / adminnbr5410!"
log "   Cliente: <EMAIL> / 54321"
