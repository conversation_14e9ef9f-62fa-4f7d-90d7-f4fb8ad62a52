#!/bin/bash

# ===================================================================
# SCRIPT DE LIMPEZA AVANÇADA - PORTAL EVOLUTION
# Remove arquivos históricos, debug, testes e desnecessários
# ===================================================================

echo "🧹 Iniciando limpeza avançada do Portal Evolution..."
echo "=================================================="

# Contadores
TOTAL_REMOVED=0
TOTAL_SIZE_BEFORE=0
TOTAL_SIZE_AFTER=0

# Função para calcular tamanho do diretório
calculate_size() {
    if [ -d "$1" ]; then
        du -sb "$1" 2>/dev/null | cut -f1
    else
        echo "0"
    fi
}

# Função para remover arquivo/diretório com log
remove_item() {
    local item="$1"
    local description="$2"
    
    if [ -e "$item" ]; then
        local size=$(du -sb "$item" 2>/dev/null | cut -f1 || echo "0")
        rm -rf "$item"
        if [ $? -eq 0 ]; then
            echo "✅ Removido: $description ($item)"
            TOTAL_REMOVED=$((TOTAL_REMOVED + 1))
        else
            echo "❌ Erro ao remover: $description ($item)"
        fi
    else
        echo "⚠️  Não encontrado: $description ($item)"
    fi
}

# Calcular tamanho inicial
TOTAL_SIZE_BEFORE=$(calculate_size ".")

echo ""
echo "📁 1. Removendo arquivos de debug e teste..."
echo "--------------------------------------------"

# Arquivos de debug específicos
remove_item "RELATORIOS_CORRECAO.md" "Arquivo de correção de relatórios (histórico)"
remove_item "debug-relatorios.sql" "Script SQL de debug"
remove_item "test-delete-user.sql" "Script de teste de exclusão"
remove_item "validate-logo-implementation.sh" "Script de validação de logo"
remove_item "public/debug.html" "Página de debug HTML"

echo ""
echo "📂 2. Removendo diretórios desnecessários..."
echo "-------------------------------------------"

# Diretórios vazios ou desnecessários
remove_item "app/" "Diretório app vazio"
remove_item "backup/" "Diretório de backup antigo"
remove_item "dados/logs/" "Diretório de logs antigo"
remove_item "dados/uploads/" "Diretório de uploads antigo"
remove_item "deploy/production/" "Diretório de deploy production"
remove_item "deploy/staging/" "Diretório de deploy staging"
remove_item "docker/development/" "Diretório docker development"
remove_item "docker/production/" "Diretório docker production"

echo ""
echo "📄 3. Removendo arquivos de configuração duplicados..."
echo "----------------------------------------------------"

# Dockerfiles duplicados
remove_item "docker/Dockerfile.backend" "Dockerfile backend duplicado"
remove_item "docker/Dockerfile.frontend" "Dockerfile frontend duplicado"
remove_item "docker/nginx.conf" "Nginx config duplicado"

# Configurações nginx duplicadas
remove_item "nginx-traefik.conf" "Configuração nginx-traefik"
remove_item "nginx.conf" "Configuração nginx raiz"

echo ""
echo "🗂️ 4. Removendo arquivos de documentação obsoletos..."
echo "---------------------------------------------------"

# Documentação obsoleta
remove_item "docs/PLANO_REORGANIZACAO_PRODUCAO.md" "Plano de reorganização (histórico)"
remove_item "docs/RELATORIO_CORRECAO_ESQUECI_SENHA.md" "Relatório de correção (histórico)"
remove_item "docs/deploy-manual.md" "Manual de deploy obsoleto"
remove_item "docs/api/" "Diretório de documentação API vazio"
remove_item "docker/README.md" "README docker obsoleto"

echo ""
echo "🧪 5. Removendo arquivos de teste e scripts obsoletos..."
echo "------------------------------------------------------"

# Scripts de teste específicos do servidor
remove_item "server/check_columns_direct.js" "Script de verificação de colunas"
remove_item "server/test_complete_creation.js" "Script de teste completo"
remove_item "server/test_database_connection.js" "Script de teste de conexão"
remove_item "server/test_final_verification.js" "Script de verificação final"
remove_item "server/test_hierarchical_queries.js" "Script de teste hierárquico"
remove_item "server/update_logs_table.js" "Script de atualização de logs"
remove_item "server/fix-permissions.sh" "Script de correção de permissões"

# Scripts de teste do frontend
remove_item "tests/" "Diretório de testes obsoleto"

echo ""
echo "📊 6. Removendo scripts específicos de clientes..."
echo "------------------------------------------------"

# Scripts específicos do cliente STB001
remove_item "scripts/executar_remocao_direta_stb001.sql" "Script remoção direta STB001"
remove_item "scripts/executar_remocao_gavetas_stb001.js" "Script remoção gavetas STB001"
remove_item "scripts/remocao_simples_stb001.sql" "Script remoção simples STB001"
remove_item "scripts/remover_gavetas_stb001.js" "Script remover gavetas STB001"
remove_item "scripts/verificar_dependencias_stb001.sql" "Script verificar dependências STB001"
remove_item "scripts/verificar_remover_gavetas_stb001.sql" "Script verificar remoção STB001"
remove_item "scripts/teste_endpoint_gavetas.js" "Script teste endpoint gavetas"

echo ""
echo "🔧 7. Removendo scripts de configuração obsoletos..."
echo "--------------------------------------------------"

# Scripts PowerShell (não usados em produção Linux)
remove_item "scripts/preparar-deploy.ps1" "Script PowerShell deploy"
remove_item "scripts/start-backend.ps1" "Script PowerShell backend"
remove_item "scripts/start-frontend.ps1" "Script PowerShell frontend"
remove_item "scripts/start-portal.ps1" "Script PowerShell portal"
remove_item "scripts/stop-portal.ps1" "Script PowerShell stop"
remove_item "scripts/test-api.ps1" "Script PowerShell test API"
remove_item "scripts/test-complete.ps1" "Script PowerShell test completo"

# Scripts de configuração VPS obsoletos
remove_item "scripts/configurar-vps-simples.sh" "Script configuração VPS simples"
remove_item "scripts/configurar-vps.sh" "Script configuração VPS"
remove_item "scripts/setup-manual-vps.sh" "Script setup manual VPS"
remove_item "scripts/teste-rapido-vps.sh" "Script teste rápido VPS"

echo ""
echo "🗄️ 8. Removendo arquivos de banco de dados temporários..."
echo "-------------------------------------------------------"

# Scripts SQL temporários
remove_item "scripts/corrigir_timezone_logs.sql" "Script correção timezone"
remove_item "database/transferencia_gavetas_script.sql" "Script transferência gavetas"

# Diretórios de migração e seeds vazios
remove_item "database/migrations/" "Diretório migrations vazio"
remove_item "database/seeds/" "Diretório seeds vazio"

echo ""
echo "📦 9. Removendo arquivos de build e cache..."
echo "------------------------------------------"

# Diretório dist (será recriado no build)
remove_item "dist/" "Diretório dist de build"

# Logs system (não usado em produção)
remove_item "logs-system/" "Sistema de logs obsoleto"

echo ""
echo "🔄 10. Removendo arquivos de configuração duplicados..."
echo "----------------------------------------------------"

# Dockerfiles obsoletos
remove_item "Dockerfile.logs" "Dockerfile de logs"

# Arquivos de configuração de ambiente vazios
if [ -d "config/environments" ]; then
    if [ -z "$(ls -A config/environments 2>/dev/null)" ]; then
        remove_item "config/environments/" "Diretório environments vazio"
        remove_item "config/" "Diretório config vazio"
    fi
fi

# Deploy directory se vazio
if [ -d "deploy" ]; then
    if [ -z "$(ls -A deploy 2>/dev/null)" ]; then
        remove_item "deploy/" "Diretório deploy vazio"
    fi
fi

# Database directory se vazio
if [ -d "database" ]; then
    if [ -z "$(ls -A database 2>/dev/null)" ]; then
        remove_item "database/" "Diretório database vazio"
    fi
fi

# Dados directory se vazio
if [ -d "dados" ]; then
    if [ -z "$(ls -A dados 2>/dev/null)" ]; then
        remove_item "dados/" "Diretório dados vazio"
    fi
fi

echo ""
echo "🧹 11. Limpeza final..."
echo "---------------------"

# Remover arquivos temporários que possam ter sido criados
find . -name "*.tmp" -type f -delete 2>/dev/null
find . -name "*.bak" -type f -delete 2>/dev/null
find . -name "*.swp" -type f -delete 2>/dev/null
find . -name ".DS_Store" -type f -delete 2>/dev/null

echo "✅ Arquivos temporários removidos"

# Calcular tamanho final
TOTAL_SIZE_AFTER=$(calculate_size ".")

echo ""
echo "=================================================="
echo "📊 RESUMO DA LIMPEZA AVANÇADA"
echo "=================================================="
echo "🗑️  Total de itens removidos: $TOTAL_REMOVED"

if [ "$TOTAL_SIZE_BEFORE" -gt 0 ] && [ "$TOTAL_SIZE_AFTER" -gt 0 ]; then
    SPACE_SAVED=$((TOTAL_SIZE_BEFORE - TOTAL_SIZE_AFTER))
    SPACE_SAVED_MB=$((SPACE_SAVED / 1024 / 1024))
    echo "💾 Espaço liberado: ${SPACE_SAVED_MB}MB"
    echo "📏 Tamanho antes: $((TOTAL_SIZE_BEFORE / 1024 / 1024))MB"
    echo "📏 Tamanho depois: $((TOTAL_SIZE_AFTER / 1024 / 1024))MB"
fi

echo ""
echo "✅ LIMPEZA CONCLUÍDA COM SUCESSO!"
echo ""
echo "📁 Estrutura final otimizada:"
echo "   ├── src/           # Frontend React"
echo "   ├── server/        # Backend Node.js"
echo "   ├── public/        # Assets estáticos"
echo "   ├── scripts/       # Scripts essenciais"
echo "   ├── docs/          # Documentação oficial"
echo "   ├── package.json   # Dependências frontend"
echo "   └── README.md      # Documentação principal"
echo ""
echo "🚀 Projeto pronto para produção!"
