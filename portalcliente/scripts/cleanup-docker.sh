#!/bin/bash

# Script de limpeza Docker para Portal Evolution
# Remove imagens antigas, containers órfãos e volumes não utilizados

set -e

echo "🧹 Iniciando limpeza Docker para Portal Evolution..."

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para verificar se comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Verificar se Docker está instalado
if ! command_exists docker; then
    log "❌ Docker não está instalado ou não está no PATH"
    exit 1
fi

# Verificar se Docker está rodando
if ! docker info >/dev/null 2>&1; then
    log "❌ Docker não está rodando"
    exit 1
fi

log "✅ Docker está rodando"

# 1. Parar e remover stack atual se existir
log "🛑 Parando stack portal-evolution (se existir)..."
docker stack rm portal-evolution 2>/dev/null || log "ℹ️ Stack portal-evolution não estava rodando"

# Aguardar remoção completa
log "⏳ Aguardando remoção completa da stack..."
sleep 15

# 2. Parar todos os containers relacionados ao portal
log "🛑 Parando containers relacionados ao portal..."
docker ps -a --filter "name=portal" --format "{{.ID}}" | xargs -r docker stop 2>/dev/null || true
docker ps -a --filter "name=portal" --format "{{.ID}}" | xargs -r docker rm 2>/dev/null || true

# 3. Remover imagens antigas do portal
log "🗑️ Removendo imagens antigas do portal..."
docker images --filter "reference=portal-evolution-*" --format "{{.ID}}" | xargs -r docker rmi -f 2>/dev/null || true

# 4. Limpar containers órfãos
log "🧹 Removendo containers órfãos..."
docker container prune -f

# 5. Limpar imagens não utilizadas
log "🧹 Removendo imagens não utilizadas..."
docker image prune -f

# 6. Limpar volumes órfãos
log "🧹 Removendo volumes órfãos..."
docker volume prune -f

# 7. Limpar networks órfãs
log "🧹 Removendo networks órfãs..."
docker network prune -f

# 8. Limpar build cache
log "🧹 Limpando build cache..."
docker builder prune -f

# 9. Mostrar estatísticas de espaço liberado
log "📊 Estatísticas após limpeza:"
docker system df

# 10. Listar imagens restantes
log "📋 Imagens Docker restantes:"
docker images

# 11. Listar containers ativos
log "📋 Containers ativos:"
docker ps

# 12. Verificar se há serviços do swarm ainda rodando
log "📋 Serviços Docker Swarm ativos:"
docker service ls 2>/dev/null || log "ℹ️ Nenhum serviço swarm ativo"

log "✅ Limpeza Docker concluída!"
log "🔄 Sistema pronto para rebuild das imagens"

# Opcional: Mostrar espaço em disco disponível
log "💾 Espaço em disco disponível:"
df -h /var/lib/docker 2>/dev/null || df -h /

echo ""
echo "🎯 Próximos passos:"
echo "1. Executar build das novas imagens"
echo "2. Fazer deploy da stack atualizada"
echo "3. Verificar se os serviços estão funcionando"
echo ""
