# ===================================
# PORTAL EVOLUTION - DOCKER COMPOSE
# ===================================
# Arquivo de orquestração seguindo padrão da VPS
# Conecta à rede traefik existente

version: '3.8'

services:
  # ===================================
  # BANCO DE DADOS POSTGRESQL - USANDO EXISTENTE
  # ===================================
  # Conectando ao banco PostgreSQL já existente na VPS

  # ===================================
  # BACKEND NODE.JS
  # ===================================
  portal-backend:
    image: portal-evolution-backend:v2025-08-01-timezone-brasil-fix-dev
    container_name: portal-backend
    restart: always
    environment:
      # Variáveis de ambiente do backend
      NODE_ENV: ${NODE_ENV:-production}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT:-5432}
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      PORT: ${PORT:-3001}
      EMAIL_HOST: ${EMAIL_HOST:-smtp.gmail.com}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER:-<EMAIL>}
      EMAIL_PASS: ${EMAIL_PASS:-jgvhevmyjpuucbhp}
      TZ: ${TZ:-America/Sao_Paulo}
    # volumes:
      # Logs da aplicação
      # - ./dados/logs:/app/logs
      # Uploads de arquivos
      # - ./dados/uploads:/app/uploads
    # depends_on:
      # - portal-database  # Usando banco externo
    networks:
      - traefik
      - portal_internal
    healthcheck:
      # Verificação de saúde do backend
      # Para testar: curl http://localhost:3001/api/health
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: ${BACKEND_MEMORY:-512m}
        reservations:
          memory: 256M
      labels:
        # Configuração Traefik para roteamento da API
        - "traefik.enable=true"
        - "traefik.http.routers.portal-api.rule=Host(`portal.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.portal-api.entrypoints=websecure"
        - "traefik.http.routers.portal-api.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal-api.loadbalancer.server.port=3001"
        - "portal.service=backend"

  # ===================================
  # FRONTEND REACT + NGINX
  # ===================================
  portal-frontend:
    image: portal-evolution-frontend:v2025-08-01-timezone-brasil-fix-dev
    container_name: portal-frontend
    restart: always
    environment:
      TZ: ${TZ:-America/Sao_Paulo}
    # volumes:
      # Configuração customizada do Nginx
      # - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - portal-backend
    networks:
      - traefik
    healthcheck:
      # Verificação de saúde do frontend
      # Para testar: curl http://localhost/health
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: ${FRONTEND_MEMORY:-256m}
        reservations:
          memory: 128M
      labels:
        # Configuração Traefik para roteamento principal
        - "traefik.enable=true"
        - "traefik.http.routers.portal-web.rule=Host(`portal.evo-eden.site`)"
        - "traefik.http.routers.portal-web.entrypoints=websecure"
        - "traefik.http.routers.portal-web.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal-web.loadbalancer.server.port=80"
        - "portal.service=frontend"

# ===================================
# VOLUMES PERSISTENTES
# ===================================
volumes:
  portal_postgres_data:
    driver: local
    # Para verificar: docker volume inspect portal_postgres_data

# ===================================
# REDES
# ===================================
networks:
  # Rede externa Traefik (já deve existir na VPS)
  # Para verificar: docker network ls | grep traefik
  traefik:
    external: true
    name: redeinterna
  
  # Rede interna para comunicação entre serviços
  portal_internal:
    driver: overlay
    internal: false

# ===================================
# INSTRUÇÕES DE USO
# ===================================

# Para iniciar todos os serviços:
# docker-compose -f portal-evolution.yaml up -d

# Para verificar status:
# docker-compose -f portal-evolution.yaml ps

# Para ver logs:
# docker-compose -f portal-evolution.yaml logs -f

# Para parar todos os serviços:
# docker-compose -f portal-evolution.yaml down

# Para atualizar (rebuild):
# docker-compose -f portal-evolution.yaml up -d --build

# Para verificar saúde dos serviços:
# docker-compose -f portal-evolution.yaml exec portal-backend curl http://localhost:3001/api/health
# docker-compose -f portal-evolution.yaml exec portal-frontend curl http://localhost/health
