#!/bin/bash

# ===================================
# DEPLOY DESENVOLVIMENTO APENAS
# ===================================
# Script para deploy apenas do ambiente de desenvolvimento
# Não afeta a produção

set -e

echo "🚀 Iniciando deploy do desenvolvimento..."

# Definir variáveis
BACKEND_IMAGE="portal-evolution-backend"
FRONTEND_IMAGE="portal-evolution-frontend"
TAG="v2025-08-01-espacamento-ajustado-dev"

# ===================================
# 1. LIMPEZA DE IMAGENS ANTIGAS
# ===================================
echo "🧹 Removendo imagens antigas de desenvolvimento..."

# Remover imagens antigas do backend (apenas dev)
docker images | grep "$BACKEND_IMAGE" | grep "dev" | awk '{print $3}' | xargs -r docker rmi -f || true

# Remover imagens antigas do frontend (apenas dev)
docker images | grep "$FRONTEND_IMAGE" | grep "dev" | awk '{print $3}' | xargs -r docker rmi -f || true

# Limpeza geral
docker system prune -f

echo "✅ Limpeza concluída"

# ===================================
# 2. BUILD DO REACT FRONTEND
# ===================================
echo "⚛️ Fazendo build do React..."
npm run build
echo "✅ Build do React concluído"

# ===================================
# 3. CONSTRUÇÃO DAS NOVAS IMAGENS
# ===================================
echo "🔨 Construindo novas imagens para desenvolvimento..."

# Construir imagem do backend
echo "📦 Construindo backend (sem cache)..."
docker build --no-cache -f Dockerfile.backend -t "$BACKEND_IMAGE:$TAG" .

# Construir imagem do frontend
echo "📦 Construindo frontend (sem cache)..."
docker build --no-cache -f Dockerfile.frontend -t "$FRONTEND_IMAGE:$TAG" .

echo "✅ Imagens construídas com sucesso"

# ===================================
# 4. ATUALIZAR ARQUIVO DE DESENVOLVIMENTO
# ===================================
echo "📝 Atualizando arquivo de configuração de desenvolvimento..."

# Atualizar tags no arquivo de desenvolvimento
sed -i "s/portal-evolution-backend:v[^[:space:]]*/portal-evolution-backend:$TAG/g" portal-evolution-dev.yaml
sed -i "s/portal-evolution-frontend:v[^[:space:]]*/portal-evolution-frontend:$TAG/g" portal-evolution-dev.yaml

echo "✅ Arquivo de configuração atualizado"

# ===================================
# 4. PARAR SERVIÇOS DE DESENVOLVIMENTO
# ===================================
echo "⏹️ Parando serviços de desenvolvimento..."

# Parar stack de desenvolvimento
docker stack rm portal-evolution-dev || true

# Aguardar remoção completa
echo "⏳ Aguardando remoção completa dos serviços..."
sleep 30

echo "✅ Serviços de desenvolvimento parados"

# ===================================
# 5. DEPLOY DESENVOLVIMENTO
# ===================================
echo "🚀 Fazendo deploy do desenvolvimento..."

# Deploy do stack de desenvolvimento
docker stack deploy -c portal-evolution-dev.yaml portal-evolution-dev

echo "✅ Deploy de desenvolvimento concluído"

# ===================================
# 6. VERIFICAÇÃO DOS SERVIÇOS
# ===================================
echo "🔍 Verificando status dos serviços de desenvolvimento..."

# Aguardar inicialização
sleep 60

echo "📊 Status dos serviços de desenvolvimento:"
docker stack services portal-evolution-dev

# ===================================
# 7. TESTE DE CONECTIVIDADE
# ===================================
echo "🧪 Testando conectividade do desenvolvimento..."

# Testar desenvolvimento
echo "🌐 Testando desenvolvimento (portaldev.evo-eden.site)..."
curl -k -s -o /dev/null -w "%{http_code}" https://portaldev.evo-eden.site/ || echo "❌ Falha na conectividade de desenvolvimento"

# ===================================
# 8. RELATÓRIO FINAL
# ===================================
echo ""
echo "🎉 DEPLOY DE DESENVOLVIMENTO CONCLUÍDO COM SUCESSO!"
echo ""
echo "📋 RESUMO:"
echo "   ✅ Imagens antigas de desenvolvimento removidas"
echo "   ✅ Novas imagens construídas: $TAG"
echo "   ✅ Deploy de desenvolvimento: portaldev.evo-eden.site"
echo "   ✅ Produção não foi afetada"
echo ""
echo "🔧 MELHORIAS IMPLEMENTADAS:"
echo "   ✅ Correção de filtros de data nos relatórios"
echo "   ✅ Remoção de offset duplo nas datas"
echo "   ✅ Inclusão correta do primeiro dia do período"
echo ""
echo "🌐 ACESSO:"
echo "   🧪 Desenvolvimento: https://portaldev.evo-eden.site"
echo ""
echo "📝 Para verificar logs:"
echo "   docker service logs portal-evolution-dev_portal-backend-dev -f"
echo "   docker service logs portal-evolution-dev_portal-frontend-dev -f"
echo ""

exit 0
