# Dockerfile para Portal Cliente - Backend Node.js
FROM node:18-alpine

# Labels para identificação da imagem
LABEL maintainer="Portal Evolution Team"
LABEL description="Portal Evolution - Backend Node.js API"
LABEL version="2.0"
LABEL component="backend"

# Definir diretório de trabalho
WORKDIR /app

# Copiar package.json e package-lock.json do servidor
COPY server/package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código do servidor
COPY server/ .

# Criar diretório de uploads
RUN mkdir -p uploads

# Expor porta 3001
EXPOSE 3001

# Comando para iniciar o servidor
CMD ["node", "index.js"]
