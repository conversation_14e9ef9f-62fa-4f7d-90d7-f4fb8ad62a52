# ===================================
# PORTAL EVOLUTION - CONFIGURAÇÃO
# ===================================

# Ambiente
NODE_ENV=production

# Aplicação
PORT=3001
JWT_SECRET=your_jwt_secret_here

# Banco de Dados
DB_HOST=postgres_postgres
DB_PORT=5432
DB_NAME=dbetens
DB_USER=postgres
DB_PASSWORD=your_db_password_here

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here

# Docker
BACKEND_MEMORY=512m
FRONTEND_MEMORY=256m

# Domínio
DOMAIN=portal.evo-eden.site
