const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST || 'postgres_postgres',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'dbetens',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// Teste de conexão
pool.on('connect', () => {
  console.log('✅ Conectado ao banco de dados PostgreSQL');
});

pool.on('error', (err) => {
  console.error('❌ Erro na conexão com o banco de dados:', err);
});

// Função para executar queries
const query = async (text, params) => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('📊 Query executada:', { text, duration, rows: res.rowCount });
    return res;
  } catch (error) {
    console.error('❌ Erro na query:', error);
    throw error;
  }
};

// Função para inicializar o banco de dados
const initializeDatabase = async () => {
  try {
    console.log('🔄 Inicializando banco de dados...');
    
    // Verificar se as tabelas existem
    const checkTables = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('usuarios', 'clientes', 'produtos', 'blocos', 'sub_blocos', 'gavetas', 'sepultamentos', 'logs_auditoria')
    `);
    
    if (checkTables.rows.length === 0) {
      console.log('📋 Criando estrutura do banco de dados...');
      // Aqui você pode executar o schema.sql se necessário
      console.log('⚠️  Execute o arquivo schema.sql manualmente para criar as tabelas');
    } else {
      console.log('✅ Estrutura do banco de dados já existe');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao inicializar banco de dados:', error);
    return false;
  }
};

module.exports = {
  pool,
  query,
  initializeDatabase
};
