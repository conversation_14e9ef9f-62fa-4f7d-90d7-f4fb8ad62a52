const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || '************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'dbetens',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function runIncrementalMigration() {
  try {
    console.log('🔄 Iniciando migração incremental...');
    
    // Verificar se a coluna cnpj existe na tabela clientes
    const checkCnpjColumn = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'clientes' AND column_name = 'cnpj'
    `);
    
    if (checkCnpjColumn.rows.length === 0) {
      console.log('📝 Adicionando novas colunas à tabela clientes...');
      
      await pool.query(`
        ALTER TABLE clientes 
        ADD COLUMN IF NOT EXISTS cnpj VARCHAR(18),
        ADD COLUMN IF NOT EXISTS nome_fantasia VARCHAR(255),
        ADD COLUMN IF NOT EXISTS razao_social VARCHAR(255),
        ADD COLUMN IF NOT EXISTS cep VARCHAR(9),
        ADD COLUMN IF NOT EXISTS logradouro VARCHAR(255),
        ADD COLUMN IF NOT EXISTS numero VARCHAR(20),
        ADD COLUMN IF NOT EXISTS complemento VARCHAR(100),
        ADD COLUMN IF NOT EXISTS bairro VARCHAR(100),
        ADD COLUMN IF NOT EXISTS cidade VARCHAR(100),
        ADD COLUMN IF NOT EXISTS estado VARCHAR(2)
      `);
      
      // Atualizar dados existentes
      await pool.query(`
        UPDATE clientes 
        SET 
          cnpj = '18.384.274/0001-78',
          nome_fantasia = COALESCE(nome, 'Cliente Padrão'),
          razao_social = COALESCE(nome, 'Cliente Padrão') || ' Ltda',
          cep = '01310-100',
          logradouro = 'Av. Paulista',
          numero = '1000',
          bairro = 'Bela Vista',
          cidade = 'São Paulo',
          estado = 'SP'
        WHERE cnpj IS NULL
      `);
      
      console.log('✅ Tabela clientes atualizada!');
    }
    
    // Verificar se a coluna codigo_estacao existe na tabela produtos
    const checkCodigoEstacao = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'produtos' AND column_name = 'codigo_estacao'
    `);
    
    if (checkCodigoEstacao.rows.length === 0) {
      console.log('📝 Atualizando tabela produtos...');
      
      await pool.query(`
        ALTER TABLE produtos 
        ADD COLUMN IF NOT EXISTS codigo_estacao VARCHAR(50),
        ADD COLUMN IF NOT EXISTS meses_para_exumar INTEGER DEFAULT 24,
        ADD COLUMN IF NOT EXISTS denominacao VARCHAR(255),
        ADD COLUMN IF NOT EXISTS observacao TEXT
      `);
      
      // Atualizar dados existentes
      await pool.query(`
        UPDATE produtos 
        SET 
          codigo_estacao = CASE 
            WHEN tipo = 'ETEN' THEN 'ETEN_001'
            ELSE 'OSS_001'
          END,
          denominacao = COALESCE(nome, 'Produto Padrão'),
          observacao = COALESCE(descricao, 'Produto migrado automaticamente')
        WHERE codigo_estacao IS NULL
      `);
      
      console.log('✅ Tabela produtos atualizada!');
    }
    
    // Verificar se a tabela numeracoes_gavetas existe
    const checkNumeracoesTable = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'numeracoes_gavetas'
    `);
    
    if (checkNumeracoesTable.rows.length === 0) {
      console.log('📝 Criando tabela numeracoes_gavetas...');
      
      await pool.query(`
        CREATE TABLE numeracoes_gavetas (
          id SERIAL PRIMARY KEY,
          sub_bloco_id INTEGER NOT NULL,
          numero_inicio INTEGER NOT NULL,
          numero_fim INTEGER NOT NULL,
          ativo BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sub_bloco_id) REFERENCES sub_blocos(id)
        )
      `);
      
      console.log('✅ Tabela numeracoes_gavetas criada!');
    }
    
    // Verificar se há dados de exemplo
    const clienteCount = await pool.query('SELECT COUNT(*) FROM clientes');
    if (parseInt(clienteCount.rows[0].count) === 0) {
      console.log('📝 Inserindo dados de exemplo...');
      
      // Inserir cliente de exemplo
      await pool.query(`
        INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, cep, logradouro, numero, bairro, cidade, estado, nome) 
        VALUES ('SAF_001', '18.384.274/0001-78', 'Safra Cemitérios', 'Safra Cemitérios Ltda', '01310-100', 'Av. Paulista', '1000', 'Bela Vista', 'São Paulo', 'SP', 'Safra Cemitérios')
        ON CONFLICT (codigo_cliente) DO NOTHING
      `);
      
      console.log('✅ Dados de exemplo inseridos!');
    }
    
    console.log('🎉 Migração incremental concluída com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro na migração incremental:', error);
  } finally {
    await pool.end();
  }
}

runIncrementalMigration();
