-- ===================================
-- CRIAÇÃO: TABELA DE TOKENS DE RESET DE SENHA
-- ===================================
-- Permite reset de senha via link temporário com expiração de 10 minutos
-- Data: 19/06/2025

BEGIN;

-- Criar tabela de tokens de reset de senha
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT false,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP,
    
    -- Foreign key para usuários
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE
);

-- Comentários explicativos
COMMENT ON TABLE password_reset_tokens IS 'Tokens temporários para reset de senha com expiração de 10 minutos';
COMMENT ON COLUMN password_reset_tokens.usuario_id IS 'ID do usuário que solicitou o reset';
COMMENT ON COLUMN password_reset_tokens.token IS 'Token único gerado para o reset (UUID)';
COMMENT ON COLUMN password_reset_tokens.expires_at IS 'Data/hora de expiração do token (10 minutos após criação)';
COMMENT ON COLUMN password_reset_tokens.used IS 'Indica se o token já foi utilizado';
COMMENT ON COLUMN password_reset_tokens.ip_address IS 'IP de onde foi solicitado o reset';
COMMENT ON COLUMN password_reset_tokens.user_agent IS 'User agent do navegador que solicitou';
COMMENT ON COLUMN password_reset_tokens.used_at IS 'Data/hora em que o token foi utilizado';

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON password_reset_tokens(token);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_usuario_id ON password_reset_tokens(usuario_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_used ON password_reset_tokens(used);

-- Função para limpar tokens expirados automaticamente
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS void AS $$
BEGIN
    DELETE FROM password_reset_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP 
    OR (used = true AND used_at < CURRENT_TIMESTAMP - INTERVAL '24 hours');
    
    -- Log da limpeza
    INSERT INTO logs_auditoria (
        usuario_id, 
        acao, 
        tabela_afetada, 
        descricao, 
        created_at
    ) VALUES (
        NULL,
        'SYSTEM_CLEANUP',
        'password_reset_tokens',
        'Limpeza automática de tokens expirados executada',
        CURRENT_TIMESTAMP
    );
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar used_at quando token é marcado como usado
CREATE OR REPLACE FUNCTION update_token_used_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.used = true AND OLD.used = false THEN
        NEW.used_at = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_token_used_at
    BEFORE UPDATE ON password_reset_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_token_used_at();

-- Função para validar token
CREATE OR REPLACE FUNCTION validate_reset_token(token_input VARCHAR(255))
RETURNS TABLE(
    valid BOOLEAN,
    usuario_id INTEGER,
    email VARCHAR(255),
    nome VARCHAR(255),
    expires_at TIMESTAMP,
    minutes_remaining INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN prt.token IS NOT NULL 
            AND prt.expires_at > CURRENT_TIMESTAMP 
            AND prt.used = false 
            THEN true 
            ELSE false 
        END as valid,
        u.id as usuario_id,
        u.email,
        u.nome,
        prt.expires_at,
        CASE 
            WHEN prt.expires_at > CURRENT_TIMESTAMP 
            THEN EXTRACT(EPOCH FROM (prt.expires_at - CURRENT_TIMESTAMP))::INTEGER / 60
            ELSE 0
        END as minutes_remaining
    FROM password_reset_tokens prt
    JOIN usuarios u ON prt.usuario_id = u.id
    WHERE prt.token = token_input;
END;
$$ LANGUAGE plpgsql;

-- Log da criação
INSERT INTO logs_auditoria (
    usuario_id, 
    acao, 
    tabela_afetada, 
    descricao, 
    created_at
) VALUES (
    NULL,
    'SYSTEM_CREATE',
    'password_reset_tokens',
    'Tabela de tokens de reset de senha criada com sucesso',
    CURRENT_TIMESTAMP
);

COMMIT;

-- Verificar se a tabela foi criada
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'password_reset_tokens' 
ORDER BY ordinal_position;
