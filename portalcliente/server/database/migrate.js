const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  host: process.env.DB_HOST || '************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'dbetens',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function runMigration() {
  try {
    console.log('🔄 Iniciando migração do banco de dados...');
    
    // Ler o arquivo schema.sql
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Executar o schema
    await pool.query(schema);
    
    console.log('✅ Migração concluída com sucesso!');
    
    // Verificar se os dados de exemplo existem
    const clienteResult = await pool.query('SELECT COUNT(*) FROM clientes');
    const clienteCount = parseInt(clienteResult.rows[0].count);
    
    if (clienteCount === 0) {
      console.log('📝 Inserindo dados de exemplo...');
      
      // Inserir cliente de exemplo
      await pool.query(`
        INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, cep, logradouro, numero, bairro, cidade, estado) 
        VALUES ('SAF_001', '18.384.274/0001-78', 'Safra Cemitérios', 'Safra Cemitérios Ltda', '01310-100', 'Av. Paulista', '1000', 'Bela Vista', 'São Paulo', 'SP')
      `);
      
      // Inserir produto de exemplo
      await pool.query(`
        INSERT INTO produtos (codigo_cliente, codigo_estacao, meses_para_exumar, denominacao, observacao) 
        VALUES ('SAF_001', 'ETEN_001', 24, 'ESTAÇÃO DE TRATAMENTO 001', 'Estação principal para tratamento de restos mortais')
      `);
      
      // Buscar o ID do produto
      const produtoResult = await pool.query('SELECT id FROM produtos WHERE codigo_estacao = $1', ['ETEN_001']);
      const produtoId = produtoResult.rows[0].id;
      
      // Inserir bloco de exemplo
      await pool.query(`
        INSERT INTO blocos (produto_id, codigo_cliente, codigo_estacao, codigo_bloco, denominacao) 
        VALUES ($1, 'SAF_001', 'ETEN_001', 'BL_001', 'Bloco 1')
      `, [produtoId]);
      
      // Buscar o ID do bloco
      const blocoResult = await pool.query('SELECT id FROM blocos WHERE codigo_bloco = $1', ['BL_001']);
      const blocoId = blocoResult.rows[0].id;
      
      // Inserir sub-bloco de exemplo
      await pool.query(`
        INSERT INTO sub_blocos (bloco_id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao) 
        VALUES ($1, 'SAF_001', 'ETEN_001', 'BL_001', 'SUB_001', 'Sub Bloco 1')
      `, [blocoId]);
      
      // Buscar o ID do sub-bloco
      const subBlocoResult = await pool.query('SELECT id FROM sub_blocos WHERE codigo_sub_bloco = $1', ['SUB_001']);
      const subBlocoId = subBlocoResult.rows[0].id;
      
      // Inserir numerações de gavetas
      await pool.query(`
        INSERT INTO numeracoes_gavetas (sub_bloco_id, numero_inicio, numero_fim) 
        VALUES ($1, 1, 20), ($1, 50, 70)
      `, [subBlocoId]);
      
      // Gerar gavetas automaticamente
      for (let i = 1; i <= 20; i++) {
        await pool.query(`
          INSERT INTO gavetas (sub_bloco_id, numero_gaveta) 
          VALUES ($1, $2)
        `, [subBlocoId, i]);
      }
      
      for (let i = 50; i <= 70; i++) {
        await pool.query(`
          INSERT INTO gavetas (sub_bloco_id, numero_gaveta) 
          VALUES ($1, $2)
        `, [subBlocoId, i]);
      }
      
      console.log('✅ Dados de exemplo inseridos!');
    }
    
    console.log('🎉 Banco de dados atualizado com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro na migração:', error);
  } finally {
    await pool.end();
  }
}

runMigration();
