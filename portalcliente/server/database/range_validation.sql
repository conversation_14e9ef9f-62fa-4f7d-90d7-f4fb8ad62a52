-- =====================================================
-- SISTEMA DE VALIDAÇÃO DE RANGES DE GAVETAS
-- =====================================================
-- Implementa todas as regras de validação definidas em definindo_range.md

-- =====================================================
-- FUNÇÃO: Validar sobreposição de ranges
-- =====================================================
CREATE OR REPLACE FUNCTION validar_range_gavetas(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50), 
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50),
    p_numero_inicio INTEGER,
    p_numero_fim INTEGER,
    p_excluir_id INTEGER DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    conflito_count INTEGER;
BEGIN
    -- Verificar se há sobreposição com outros ranges do mesmo bloco
    SELECT COUNT(*) INTO conflito_count
    FROM numeracoes_gavetas ng
    WHERE ng.codigo_cliente = p_codigo_cliente
      AND ng.codigo_estacao = p_codigo_estacao
      AND ng.codigo_bloco = p_codigo_bloco
      AND ng.ativo = true
      AND (p_excluir_id IS NULL OR ng.id != p_excluir_id)
      AND (
          -- Verifica se há sobreposição de ranges
          (p_numero_inicio BETWEEN ng.numero_inicio AND ng.numero_fim) OR
          (p_numero_fim BETWEEN ng.numero_inicio AND ng.numero_fim) OR
          (ng.numero_inicio BETWEEN p_numero_inicio AND p_numero_fim) OR
          (ng.numero_fim BETWEEN p_numero_inicio AND p_numero_fim)
      );
    
    -- Retorna TRUE se não há conflito, FALSE se há conflito
    RETURN conflito_count = 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Verificar gavetas ocupadas
-- =====================================================
CREATE OR REPLACE FUNCTION verificar_gavetas_ocupadas(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50),
    p_numero_inicio INTEGER,
    p_numero_fim INTEGER
) RETURNS INTEGER AS $$
DECLARE
    gavetas_ocupadas INTEGER;
BEGIN
    -- Contar gavetas ocupadas no range
    SELECT COUNT(*) INTO gavetas_ocupadas
    FROM gavetas g
    WHERE g.codigo_cliente = p_codigo_cliente
      AND g.codigo_estacao = p_codigo_estacao
      AND g.codigo_bloco = p_codigo_bloco
      AND g.codigo_sub_bloco = p_codigo_sub_bloco
      AND g.numero_gaveta BETWEEN p_numero_inicio AND p_numero_fim
      AND g.disponivel = false
      AND g.ativo = true;
    
    RETURN gavetas_ocupadas;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Sincronizar gavetas com numerações
-- =====================================================
CREATE OR REPLACE FUNCTION sincronizar_gavetas(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50)
) RETURNS VOID AS $$
DECLARE
    range_record RECORD;
    gaveta_numero INTEGER;
BEGIN
    -- Deletar gavetas que não estão mais em nenhum range ativo
    DELETE FROM gavetas g
    WHERE g.codigo_cliente = p_codigo_cliente
      AND g.codigo_estacao = p_codigo_estacao
      AND g.codigo_bloco = p_codigo_bloco
      AND g.codigo_sub_bloco = p_codigo_sub_bloco
      AND g.disponivel = true -- Só deletar gavetas disponíveis
      AND NOT EXISTS (
          SELECT 1 FROM numeracoes_gavetas ng
          WHERE ng.codigo_cliente = g.codigo_cliente
            AND ng.codigo_estacao = g.codigo_estacao
            AND ng.codigo_bloco = g.codigo_bloco
            AND ng.codigo_sub_bloco = g.codigo_sub_bloco
            AND g.numero_gaveta BETWEEN ng.numero_inicio AND ng.numero_fim
            AND ng.ativo = true
      );
    
    -- Criar gavetas para todos os ranges ativos
    FOR range_record IN 
        SELECT numero_inicio, numero_fim 
        FROM numeracoes_gavetas 
        WHERE codigo_cliente = p_codigo_cliente
          AND codigo_estacao = p_codigo_estacao
          AND codigo_bloco = p_codigo_bloco
          AND codigo_sub_bloco = p_codigo_sub_bloco
          AND ativo = true
    LOOP
        -- Criar gavetas para cada número no range
        FOR gaveta_numero IN range_record.numero_inicio..range_record.numero_fim LOOP
            INSERT INTO gavetas (
                codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
                numero_gaveta, posicao_x, posicao_y, altura_especial,
                disponivel, ativo, created_at, updated_at
            ) VALUES (
                p_codigo_cliente, p_codigo_estacao, p_codigo_bloco, p_codigo_sub_bloco,
                gaveta_numero, 1, 1, 1.0,
                true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            ) ON CONFLICT (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta) 
            DO NOTHING; -- Não sobrescrever gavetas existentes
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGER: Validar antes de inserir/atualizar numeração
-- =====================================================
CREATE OR REPLACE FUNCTION trigger_validar_numeracao_gavetas() RETURNS TRIGGER AS $$
BEGIN
    -- Validar se o range é válido
    IF NEW.numero_inicio > NEW.numero_fim THEN
        RAISE EXCEPTION 'Número de início (%) não pode ser maior que número de fim (%)', 
            NEW.numero_inicio, NEW.numero_fim;
    END IF;
    
    -- Validar se não há sobreposição com outros ranges
    IF NOT validar_range_gavetas(
        NEW.codigo_cliente, NEW.codigo_estacao, NEW.codigo_bloco, NEW.codigo_sub_bloco,
        NEW.numero_inicio, NEW.numero_fim, 
        CASE WHEN TG_OP = 'UPDATE' THEN NEW.id ELSE NULL END
    ) THEN
        RAISE EXCEPTION 'Range de gavetas (% a %) conflita com ranges existentes no bloco %/%/%', 
            NEW.numero_inicio, NEW.numero_fim, NEW.codigo_cliente, NEW.codigo_estacao, NEW.codigo_bloco;
    END IF;
    
    -- Se for UPDATE, verificar se há gavetas ocupadas no range que será removido
    IF TG_OP = 'UPDATE' AND OLD.ativo = true AND NEW.ativo = false THEN
        IF verificar_gavetas_ocupadas(
            OLD.codigo_cliente, OLD.codigo_estacao, OLD.codigo_bloco, OLD.codigo_sub_bloco,
            OLD.numero_inicio, OLD.numero_fim
        ) > 0 THEN
            RAISE EXCEPTION 'Não é possível desativar range pois há gavetas ocupadas (indisponíveis) no range % a %', 
                OLD.numero_inicio, OLD.numero_fim;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGER: Sincronizar gavetas após mudanças
-- =====================================================
CREATE OR REPLACE FUNCTION trigger_sincronizar_gavetas() RETURNS TRIGGER AS $$
BEGIN
    -- Sincronizar gavetas após inserção/atualização
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        PERFORM sincronizar_gavetas(
            NEW.codigo_cliente, NEW.codigo_estacao, NEW.codigo_bloco, NEW.codigo_sub_bloco
        );
        RETURN NEW;
    END IF;
    
    -- Sincronizar gavetas após deleção
    IF TG_OP = 'DELETE' THEN
        PERFORM sincronizar_gavetas(
            OLD.codigo_cliente, OLD.codigo_estacao, OLD.codigo_bloco, OLD.codigo_sub_bloco
        );
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- APLICAR TRIGGERS
-- =====================================================
DROP TRIGGER IF EXISTS tr_validar_numeracao_gavetas ON numeracoes_gavetas;
CREATE TRIGGER tr_validar_numeracao_gavetas
    BEFORE INSERT OR UPDATE ON numeracoes_gavetas
    FOR EACH ROW EXECUTE FUNCTION trigger_validar_numeracao_gavetas();

DROP TRIGGER IF EXISTS tr_sincronizar_gavetas ON numeracoes_gavetas;
CREATE TRIGGER tr_sincronizar_gavetas
    AFTER INSERT OR UPDATE OR DELETE ON numeracoes_gavetas
    FOR EACH ROW EXECUTE FUNCTION trigger_sincronizar_gavetas();

-- =====================================================
-- FUNÇÃO: Validar integridade antes de deletar
-- =====================================================
CREATE OR REPLACE FUNCTION validar_delecao_hierarquica(
    p_tipo VARCHAR(20),
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50) DEFAULT NULL,
    p_codigo_bloco VARCHAR(50) DEFAULT NULL,
    p_codigo_sub_bloco VARCHAR(50) DEFAULT NULL,
    p_numero_gaveta INTEGER DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
    count_dependentes INTEGER;
    mensagem_erro TEXT;
BEGIN
    CASE p_tipo
        WHEN 'produto' THEN
            SELECT COUNT(*) INTO count_dependentes
            FROM blocos WHERE codigo_cliente = p_codigo_cliente AND codigo_estacao = p_codigo_estacao AND ativo = true;
            IF count_dependentes > 0 THEN
                RETURN 'Não é possível deletar produto pois há ' || count_dependentes || ' bloco(s) associado(s)';
            END IF;
            
        WHEN 'bloco' THEN
            SELECT COUNT(*) INTO count_dependentes
            FROM sub_blocos WHERE codigo_cliente = p_codigo_cliente AND codigo_estacao = p_codigo_estacao 
                AND codigo_bloco = p_codigo_bloco AND ativo = true;
            IF count_dependentes > 0 THEN
                RETURN 'Não é possível deletar bloco pois há ' || count_dependentes || ' sub-bloco(s) associado(s)';
            END IF;
            
        WHEN 'sub_bloco' THEN
            -- Verificar se há sepultamentos ativos (não exumados) no sub-bloco
            SELECT COUNT(*) INTO count_dependentes
            FROM sepultamentos WHERE codigo_cliente = p_codigo_cliente AND codigo_estacao = p_codigo_estacao
                AND codigo_bloco = p_codigo_bloco AND codigo_sub_bloco = p_codigo_sub_bloco
                AND ativo = true AND data_exumacao IS NULL;
            IF count_dependentes > 0 THEN
                RETURN 'Não é possível deletar este sub-bloco pois existem sepultamentos associados a ele.';
            END IF;
            
        WHEN 'gaveta' THEN
            SELECT COUNT(*) INTO count_dependentes
            FROM sepultamentos WHERE codigo_cliente = p_codigo_cliente AND codigo_estacao = p_codigo_estacao 
                AND codigo_bloco = p_codigo_bloco AND codigo_sub_bloco = p_codigo_sub_bloco 
                AND numero_gaveta = p_numero_gaveta AND ativo = true AND data_exumacao IS NULL;
            IF count_dependentes > 0 THEN
                RETURN 'Não é possível deletar gaveta pois há ' || count_dependentes || ' sepultamento(s) ativo(s) associado(s)';
            END IF;
    END CASE;
    
    RETURN NULL; -- Sem problemas, pode deletar
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Contar gavetas baseado nos ranges (CONFORME INSTRUCAO.MD)
-- =====================================================
CREATE OR REPLACE FUNCTION contar_gavetas_por_ranges(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50)
) RETURNS INTEGER AS $$
DECLARE
    total_gavetas INTEGER := 0;
    range_record RECORD;
BEGIN
    -- Buscar todos os ranges ativos para o sub-bloco
    FOR range_record IN
        SELECT numero_inicio, numero_fim
        FROM numeracoes_gavetas
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND codigo_bloco = p_codigo_bloco
        AND codigo_sub_bloco = p_codigo_sub_bloco
        AND ativo = true
    LOOP
        -- Contar gavetas que estão dentro do range (CONFORME INSTRUCAO.MD)
        SELECT total_gavetas + COUNT(*) INTO total_gavetas
        FROM gavetas
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND codigo_bloco = p_codigo_bloco
        AND codigo_sub_bloco = p_codigo_sub_bloco
        AND numero_gaveta BETWEEN range_record.numero_inicio AND range_record.numero_fim
        AND ativo = true;
    END LOOP;

    RETURN total_gavetas;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Verificar se um range pode ser deletado (CONFORME INSTRUCAO.MD)
-- =====================================================
CREATE OR REPLACE FUNCTION pode_deletar_range(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50),
    p_numero_inicio INTEGER,
    p_numero_fim INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
    gavetas_ocupadas INTEGER := 0;
BEGIN
    -- Verificar se há gavetas ocupadas no range (disponivel = false)
    SELECT COUNT(*) INTO gavetas_ocupadas
    FROM gavetas
    WHERE codigo_cliente = p_codigo_cliente
    AND codigo_estacao = p_codigo_estacao
    AND codigo_bloco = p_codigo_bloco
    AND codigo_sub_bloco = p_codigo_sub_bloco
    AND numero_gaveta BETWEEN p_numero_inicio AND p_numero_fim
    AND disponivel = false
    AND ativo = true;

    -- Retorna true se não há gavetas ocupadas
    RETURN gavetas_ocupadas = 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Validar conflitos de ranges (CONFORME INSTRUCAO.MD)
-- =====================================================
CREATE OR REPLACE FUNCTION validar_conflito_ranges(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50),
    p_numero_inicio INTEGER,
    p_numero_fim INTEGER,
    p_range_id INTEGER DEFAULT NULL
) RETURNS TEXT AS $$
DECLARE
    range_conflitante RECORD;
BEGIN
    -- REGRA 1: Verificar conflitos dentro do mesmo sub-bloco
    -- Exemplo instrucao.md: SUB_001 não pode ter 1-10 e 5-20 (conflito 5,6,7,8,9,10)
    FOR range_conflitante IN
        SELECT id, numero_inicio, numero_fim
        FROM numeracoes_gavetas
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND codigo_bloco = p_codigo_bloco
        AND codigo_sub_bloco = p_codigo_sub_bloco
        AND ativo = true
        AND (p_range_id IS NULL OR id != p_range_id)
        AND (
            (p_numero_inicio BETWEEN numero_inicio AND numero_fim) OR
            (p_numero_fim BETWEEN numero_inicio AND numero_fim) OR
            (numero_inicio BETWEEN p_numero_inicio AND p_numero_fim) OR
            (numero_fim BETWEEN p_numero_inicio AND p_numero_fim)
        )
    LOOP
        RETURN 'Conflito no sub-bloco ' || p_codigo_sub_bloco ||
               ' com range existente: ' ||
               range_conflitante.numero_inicio || '-' || range_conflitante.numero_fim ||
               ' (números ' || GREATEST(p_numero_inicio, range_conflitante.numero_inicio) ||
               ' a ' || LEAST(p_numero_fim, range_conflitante.numero_fim) || ' já ocupados)';
    END LOOP;

    -- REGRA 2: Verificar duplicidade entre sub-blocos do mesmo bloco
    -- Conforme instrucao.md linha 12: verificar duplicidade com outros sub-blocos do mesmo bloco
    FOR range_conflitante IN
        SELECT ng.id, ng.numero_inicio, ng.numero_fim, ng.codigo_sub_bloco
        FROM numeracoes_gavetas ng
        WHERE ng.codigo_cliente = p_codigo_cliente
        AND ng.codigo_estacao = p_codigo_estacao
        AND ng.codigo_bloco = p_codigo_bloco
        AND ng.codigo_sub_bloco != p_codigo_sub_bloco  -- Diferentes sub-blocos
        AND ng.ativo = true
        AND (p_range_id IS NULL OR ng.id != p_range_id)
        AND (
            (p_numero_inicio BETWEEN ng.numero_inicio AND ng.numero_fim) OR
            (p_numero_fim BETWEEN ng.numero_inicio AND ng.numero_fim) OR
            (ng.numero_inicio BETWEEN p_numero_inicio AND p_numero_fim) OR
            (ng.numero_fim BETWEEN p_numero_inicio AND p_numero_fim)
        )
    LOOP
        RETURN 'Conflito entre sub-blocos: ' || p_codigo_sub_bloco ||
               ' e ' || range_conflitante.codigo_sub_bloco ||
               ' no range ' || range_conflitante.numero_inicio || '-' || range_conflitante.numero_fim ||
               ' (números ' || GREATEST(p_numero_inicio, range_conflitante.numero_inicio) ||
               ' a ' || LEAST(p_numero_fim, range_conflitante.numero_fim) || ' duplicados)';
    END LOOP;

    RETURN 'OK';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Criar gavetas automaticamente (CONFORME INSTRUCAO.MD)
-- =====================================================
CREATE OR REPLACE FUNCTION criar_gavetas_automaticamente(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50),
    p_numero_inicio INTEGER,
    p_numero_fim INTEGER
) RETURNS TEXT AS $$
DECLARE
    v_numero_gaveta INTEGER;
    gavetas_criadas INTEGER := 0;
    gavetas_existentes INTEGER := 0;
BEGIN
    -- Criar gavetas de 1 em 1 conforme instrucao.md linha 23
    -- Exemplo: range 1-10 cria gavetas 1, 2, 3, 4, 5, 6, 7, 8, 9, 10
    FOR v_numero_gaveta IN p_numero_inicio..p_numero_fim LOOP
        -- Verificar se a gaveta já existe
        IF EXISTS (
            SELECT 1 FROM gavetas g
            WHERE g.codigo_cliente = p_codigo_cliente
            AND g.codigo_estacao = p_codigo_estacao
            AND g.codigo_bloco = p_codigo_bloco
            AND g.codigo_sub_bloco = p_codigo_sub_bloco
            AND g.numero_gaveta = v_numero_gaveta
            AND g.ativo = true
        ) THEN
            gavetas_existentes := gavetas_existentes + 1;
        ELSE
            -- Criar gaveta individual
            INSERT INTO gavetas (
                codigo_cliente,
                codigo_estacao,
                codigo_bloco,
                codigo_sub_bloco,
                numero_gaveta,
                posicao_x,
                posicao_y,
                disponivel,
                ativo,
                created_at,
                updated_at
            ) VALUES (
                p_codigo_cliente,
                p_codigo_estacao,
                p_codigo_bloco,
                p_codigo_sub_bloco,
                v_numero_gaveta,
                1, -- posicao_x default conforme instrucao.md
                1, -- posicao_y default conforme instrucao.md
                true, -- disponivel = true conforme instrucao.md linha 21
                true, -- ativo = true conforme instrucao.md linha 20
                NOW(),
                NOW()
            );
            gavetas_criadas := gavetas_criadas + 1;
        END IF;
    END LOOP;

    RETURN 'Gavetas criadas: ' || gavetas_criadas || ', já existentes: ' || gavetas_existentes;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Deletar gavetas de um range (CONFORME INSTRUCAO.MD)
-- =====================================================
CREATE OR REPLACE FUNCTION deletar_gavetas_range(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50),
    p_numero_inicio INTEGER,
    p_numero_fim INTEGER
) RETURNS TEXT AS $$
DECLARE
    gavetas_deletadas INTEGER := 0;
    gavetas_ocupadas INTEGER := 0;
BEGIN
    -- Verificar gavetas ocupadas primeiro
    SELECT COUNT(*) INTO gavetas_ocupadas
    FROM gavetas g
    WHERE g.codigo_cliente = p_codigo_cliente
    AND g.codigo_estacao = p_codigo_estacao
    AND g.codigo_bloco = p_codigo_bloco
    AND g.codigo_sub_bloco = p_codigo_sub_bloco
    AND g.numero_gaveta BETWEEN p_numero_inicio AND p_numero_fim
    AND g.disponivel = false
    AND g.ativo = true;

    IF gavetas_ocupadas > 0 THEN
        RETURN 'Erro: ' || gavetas_ocupadas || ' gaveta(s) ocupada(s) no range ' || p_numero_inicio || '-' || p_numero_fim;
    END IF;

    -- CORREÇÃO: Deletar gavetas disponíveis FISICAMENTE (DELETE real)
    -- Isso resolve o problema de conflito de chave única ao recriar ranges
    DELETE FROM gavetas
    WHERE codigo_cliente = p_codigo_cliente
    AND codigo_estacao = p_codigo_estacao
    AND codigo_bloco = p_codigo_bloco
    AND codigo_sub_bloco = p_codigo_sub_bloco
    AND numero_gaveta BETWEEN p_numero_inicio AND p_numero_fim
    AND disponivel = true
    AND ativo = true;

    GET DIAGNOSTICS gavetas_deletadas = ROW_COUNT;

    RETURN 'Gavetas deletadas: ' || gavetas_deletadas || ' (range ' || p_numero_inicio || '-' || p_numero_fim || ')';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Validar associações hierárquicas (CONFORME INSTRUCAO.MD)
-- =====================================================
CREATE OR REPLACE FUNCTION validar_associacoes_hierarquicas(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50)
) RETURNS TEXT AS $$
DECLARE
    cliente_existe BOOLEAN := false;
    produto_existe BOOLEAN := false;
    bloco_existe BOOLEAN := false;
    sub_bloco_existe BOOLEAN := false;
BEGIN
    -- Verificar se cliente existe
    SELECT EXISTS(
        SELECT 1 FROM clientes
        WHERE codigo_cliente = p_codigo_cliente AND ativo = true
    ) INTO cliente_existe;

    IF NOT cliente_existe THEN
        RETURN 'Cliente ' || p_codigo_cliente || ' não encontrado ou inativo';
    END IF;

    -- Verificar se produto/estação existe
    SELECT EXISTS(
        SELECT 1 FROM produtos
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND ativo = true
    ) INTO produto_existe;

    IF NOT produto_existe THEN
        RETURN 'Produto/Estação ' || p_codigo_estacao || ' não encontrado para cliente ' || p_codigo_cliente;
    END IF;

    -- Verificar se bloco existe
    SELECT EXISTS(
        SELECT 1 FROM blocos
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND codigo_bloco = p_codigo_bloco
        AND ativo = true
    ) INTO bloco_existe;

    IF NOT bloco_existe THEN
        RETURN 'Bloco ' || p_codigo_bloco || ' não encontrado para produto ' || p_codigo_estacao;
    END IF;

    -- Verificar se sub-bloco existe
    SELECT EXISTS(
        SELECT 1 FROM sub_blocos
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND codigo_bloco = p_codigo_bloco
        AND codigo_sub_bloco = p_codigo_sub_bloco
        AND ativo = true
    ) INTO sub_bloco_existe;

    IF NOT sub_bloco_existe THEN
        RETURN 'Sub-bloco ' || p_codigo_sub_bloco || ' não encontrado para bloco ' || p_codigo_bloco;
    END IF;

    RETURN 'OK';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Calcular total de gavetas por ranges (NOVA ARQUITETURA)
-- =====================================================
CREATE OR REPLACE FUNCTION calcular_total_gavetas_ranges(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50)
) RETURNS INTEGER AS $$
DECLARE
    total_gavetas INTEGER := 0;
    range_record RECORD;
BEGIN
    -- Somar todas as gavetas dos ranges ativos
    -- Exemplo: Range 1 (21-30) = 10 gavetas, Range 2 (51-80) = 30 gavetas = Total: 40
    FOR range_record IN
        SELECT numero_inicio, numero_fim
        FROM numeracoes_gavetas
        WHERE codigo_cliente = p_codigo_cliente
        AND codigo_estacao = p_codigo_estacao
        AND codigo_bloco = p_codigo_bloco
        AND codigo_sub_bloco = p_codigo_sub_bloco
        AND ativo = true
    LOOP
        total_gavetas := total_gavetas + (range_record.numero_fim - range_record.numero_inicio + 1);
    END LOOP;

    RETURN total_gavetas;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNÇÃO: Listar ranges com detalhes (NOVA ARQUITETURA)
-- =====================================================
CREATE OR REPLACE FUNCTION listar_ranges_detalhados(
    p_codigo_cliente VARCHAR(50),
    p_codigo_estacao VARCHAR(50),
    p_codigo_bloco VARCHAR(50),
    p_codigo_sub_bloco VARCHAR(50)
) RETURNS TABLE (
    id INTEGER,
    numero_inicio INTEGER,
    numero_fim INTEGER,
    total_gavetas_range INTEGER,
    gavetas_disponiveis INTEGER,
    gavetas_ocupadas INTEGER,
    ativo BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ng.id,
        ng.numero_inicio,
        ng.numero_fim,
        (ng.numero_fim - ng.numero_inicio + 1) as total_gavetas_range,
        COALESCE(COUNT(g.numero_gaveta) FILTER (WHERE g.disponivel = true), 0)::INTEGER as gavetas_disponiveis,
        COALESCE(COUNT(g.numero_gaveta) FILTER (WHERE g.disponivel = false), 0)::INTEGER as gavetas_ocupadas,
        ng.ativo,
        ng.created_at,
        ng.updated_at
    FROM numeracoes_gavetas ng
    LEFT JOIN gavetas g ON ng.codigo_cliente = g.codigo_cliente
        AND ng.codigo_estacao = g.codigo_estacao
        AND ng.codigo_bloco = g.codigo_bloco
        AND ng.codigo_sub_bloco = g.codigo_sub_bloco
        AND g.numero_gaveta BETWEEN ng.numero_inicio AND ng.numero_fim
        AND g.ativo = true
    WHERE ng.codigo_cliente = p_codigo_cliente
      AND ng.codigo_estacao = p_codigo_estacao
      AND ng.codigo_bloco = p_codigo_bloco
      AND ng.codigo_sub_bloco = p_codigo_sub_bloco
      AND ng.ativo = true
    GROUP BY ng.id, ng.numero_inicio, ng.numero_fim, ng.ativo, ng.created_at, ng.updated_at
    ORDER BY ng.numero_inicio;
END;
$$ LANGUAGE plpgsql;
