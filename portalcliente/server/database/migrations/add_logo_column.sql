-- Migration: Ad<PERSON><PERSON><PERSON> coluna logo_path na tabela clientes
-- Data: 2025-01-20
-- Descrição: Adiciona suporte para upload de logos dos clientes

-- Verificar se a coluna já existe antes de adicionar
DO $$ 
BEGIN
    -- Verificar se a coluna logo_path já existe
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'clientes' 
        AND column_name = 'logo_path'
    ) THEN
        -- Adicionar coluna logo_path
        ALTER TABLE clientes 
        ADD COLUMN logo_path VARCHAR(255) NULL;
        
        RAISE NOTICE 'Coluna logo_path adicionada à tabela clientes';
    ELSE
        RAISE NOTICE 'Coluna logo_path já existe na tabela clientes';
    END IF;
    
    -- Adicionar comentário na coluna
    COMMENT ON COLUMN clientes.logo_path IS 'Caminho relativo para o arquivo de logo do cliente (ex: /api/uploads/logos/SAF_001.png)';
    
    -- Atualizar timestamp de updated_at para refletir a mudança na estrutura
    UPDATE clientes SET updated_at = CURRENT_TIMESTAMP WHERE logo_path IS NULL;
    
END $$;

-- <PERSON><PERSON>r índice para otimizar consultas por logo_path (opcional)
CREATE INDEX IF NOT EXISTS idx_clientes_logo_path 
ON clientes(logo_path) 
WHERE logo_path IS NOT NULL;

-- Verificar a estrutura final da tabela
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 'clientes' 
ORDER BY ordinal_position;
