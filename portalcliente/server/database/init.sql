-- Script de inicialização do banco de dados
-- <PERSON>ste script é executado automaticamente quando o container PostgreSQL é criado

-- <PERSON>riar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON><PERSON> tabela de usuários
CREATE TABLE IF NOT EXISTS usuarios (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    tipo_usuario VARCHAR(20) NOT NULL CHECK (tipo_usuario IN ('admin', 'cliente')),
    codigo_cliente VARCHAR(50),
    nome VARCHAR(255) NOT NULL,
    produtos_acesso TEXT[],
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON>r tabela de clientes
CREATE TABLE IF NOT EXISTS clientes (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) UNIQUE NOT NULL,
    razao_social VARCHAR(255) NOT NULL,
    nome_fantasia VARCHAR(255),
    cnpj VARCHAR(18),
    endereco TEXT,
    telefone VARCHAR(20),
    email VARCHAR(255),
    responsavel VARCHAR(255),
    observacoes TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Criar tabela de produtos
CREATE TABLE IF NOT EXISTS produtos (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255) NOT NULL,
    tipo VARCHAR(20) NOT NULL CHECK (tipo IN ('ETEN', 'OSSUARIO')),
    meses_para_exumar INTEGER DEFAULT 36,
    observacao TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao)
);

-- Criar tabela de blocos
CREATE TABLE IF NOT EXISTS blocos (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255),
    observacao TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco)
);

-- Criar tabela de sub_blocos
CREATE TABLE IF NOT EXISTS sub_blocos (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    denominacao VARCHAR(255),
    observacao TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco)
);

-- Criar tabela de numeracoes_gavetas
CREATE TABLE IF NOT EXISTS numeracoes_gavetas (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_inicio INTEGER NOT NULL,
    numero_fim INTEGER NOT NULL,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim)
);

-- Criar tabela de gavetas
CREATE TABLE IF NOT EXISTS gavetas (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    disponivel BOOLEAN DEFAULT true,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
);

-- Criar tabela de sepultamentos
CREATE TABLE IF NOT EXISTS sepultamentos (
    id SERIAL PRIMARY KEY,
    codigo_cliente VARCHAR(50) NOT NULL,
    codigo_estacao VARCHAR(50) NOT NULL,
    codigo_bloco VARCHAR(50) NOT NULL,
    codigo_sub_bloco VARCHAR(50) NOT NULL,
    numero_gaveta INTEGER NOT NULL,
    nome_sepultado VARCHAR(255) NOT NULL,
    data_sepultamento DATE NOT NULL,
    horario_sepultamento TIME,
    data_exumacao DATE,
    observacoes TEXT,
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
);

-- Criar tabela de logs de auditoria
CREATE TABLE IF NOT EXISTS logs_auditoria (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER,
    codigo_cliente VARCHAR(50),
    codigo_estacao VARCHAR(50),
    codigo_bloco VARCHAR(50),
    codigo_sub_bloco VARCHAR(50),
    acao VARCHAR(50) NOT NULL,
    tabela_afetada VARCHAR(100) NOT NULL,
    registro_id VARCHAR(100),
    dados_anteriores JSONB,
    dados_novos JSONB,
    ip_address INET,
    user_agent TEXT,
    descricao TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Adicionar chaves estrangeiras
ALTER TABLE usuarios 
ADD CONSTRAINT fk_usuarios_cliente 
FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente) 
ON DELETE SET NULL;

ALTER TABLE produtos 
ADD CONSTRAINT fk_produtos_cliente 
FOREIGN KEY (codigo_cliente) REFERENCES clientes(codigo_cliente) 
ON DELETE CASCADE;

ALTER TABLE blocos 
ADD CONSTRAINT fk_blocos_produto 
FOREIGN KEY (codigo_cliente, codigo_estacao) REFERENCES produtos(codigo_cliente, codigo_estacao) 
ON DELETE CASCADE;

ALTER TABLE sub_blocos 
ADD CONSTRAINT fk_sub_blocos_bloco 
FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco) REFERENCES blocos(codigo_cliente, codigo_estacao, codigo_bloco) 
ON DELETE CASCADE;

ALTER TABLE numeracoes_gavetas 
ADD CONSTRAINT fk_numeracoes_sub_bloco 
FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) REFERENCES sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) 
ON DELETE CASCADE;

ALTER TABLE gavetas 
ADD CONSTRAINT fk_gavetas_sub_bloco 
FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) REFERENCES sub_blocos(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco) 
ON DELETE CASCADE;

ALTER TABLE sepultamentos 
ADD CONSTRAINT fk_sepultamentos_gaveta 
FOREIGN KEY (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta) REFERENCES gavetas(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta) 
ON DELETE CASCADE;

ALTER TABLE logs_auditoria 
ADD CONSTRAINT fk_logs_usuario 
FOREIGN KEY (usuario_id) REFERENCES usuarios(id) 
ON DELETE SET NULL;

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_usuarios_email ON usuarios(email);
CREATE INDEX IF NOT EXISTS idx_usuarios_tipo ON usuarios(tipo_usuario);
CREATE INDEX IF NOT EXISTS idx_usuarios_ativo ON usuarios(ativo);
CREATE INDEX IF NOT EXISTS idx_clientes_codigo ON clientes(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_produtos_cliente ON produtos(codigo_cliente);
CREATE INDEX IF NOT EXISTS idx_produtos_tipo ON produtos(tipo);
CREATE INDEX IF NOT EXISTS idx_gavetas_disponivel ON gavetas(disponivel);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_ativo ON sepultamentos(ativo);
CREATE INDEX IF NOT EXISTS idx_sepultamentos_data ON sepultamentos(data_sepultamento);
CREATE INDEX IF NOT EXISTS idx_logs_created_at ON logs_auditoria(created_at);
CREATE INDEX IF NOT EXISTS idx_logs_usuario ON logs_auditoria(usuario_id);
CREATE INDEX IF NOT EXISTS idx_logs_tabela ON logs_auditoria(tabela_afetada);

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_usuarios_updated_at BEFORE UPDATE ON usuarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clientes_updated_at BEFORE UPDATE ON clientes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_produtos_updated_at BEFORE UPDATE ON produtos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_blocos_updated_at BEFORE UPDATE ON blocos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sub_blocos_updated_at BEFORE UPDATE ON sub_blocos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_numeracoes_gavetas_updated_at BEFORE UPDATE ON numeracoes_gavetas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_gavetas_updated_at BEFORE UPDATE ON gavetas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sepultamentos_updated_at BEFORE UPDATE ON sepultamentos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Inserir usuário administrador padrão (senha: adminnbr5410!)
INSERT INTO usuarios (email, senha, tipo_usuario, nome, ativo) 
VALUES (
    '<EMAIL>', 
    '$2b$12$8K8.QQxQxQxQxQxQxQxQxOeKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK', -- Hash da senha adminnbr5410!
    'admin', 
    'Administrador Sistema', 
    true
) ON CONFLICT (email) DO NOTHING;

-- Log de inicialização
INSERT INTO logs_auditoria (acao, tabela_afetada, descricao, created_at)
VALUES ('INIT', 'database', 'Banco de dados inicializado com sucesso', CURRENT_TIMESTAMP);
