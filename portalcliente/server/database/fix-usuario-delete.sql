-- ===================================
-- CORREÇÃO: DELETAR USUÁRIOS
-- ===================================
-- Permite deletar usuários mantendo logs de auditoria
-- Data: 19/06/2025

BEGIN;

-- 1. Remover constraint existente
ALTER TABLE logs_auditoria 
DROP CONSTRAINT IF EXISTS logs_auditoria_usuario_id_fkey;

-- 2. Modificar coluna usuario_id para aceitar NULL
ALTER TABLE logs_auditoria 
ALTER COLUMN usuario_id DROP NOT NULL;

-- 3. Adicionar nova constraint com ON DELETE SET NULL
ALTER TABLE logs_auditoria 
ADD CONSTRAINT logs_auditoria_usuario_id_fkey 
FOREIGN KEY (usuario_id) REFERENCES usuarios(id) 
ON DELETE SET NULL;

-- 4. Adicionar coment<PERSON>rio explicativo
COMMENT ON COLUMN logs_auditoria.usuario_id IS 
'ID do usuário que executou a ação. NULL quando usuário foi deletado.';

-- 5. Criar índice para logs órfãos (opcional, para performance)
CREATE INDEX IF NOT EXISTS idx_logs_usuario_null 
ON logs_auditoria(usuario_id) 
WHERE usuario_id IS NULL;

-- 6. Log da correção
INSERT INTO logs_auditoria (
    usuario_id, 
    acao, 
    tabela_afetada, 
    descricao, 
    created_at
) VALUES (
    NULL,
    'SYSTEM_FIX',
    'logs_auditoria',
    'Correção aplicada: Permitir exclusão de usuários mantendo logs de auditoria',
    CURRENT_TIMESTAMP
);

COMMIT;

-- Verificar se a correção foi aplicada
SELECT 
    conname as constraint_name,
    CASE confdeltype 
        WHEN 'a' THEN 'NO ACTION'
        WHEN 'r' THEN 'RESTRICT'
        WHEN 'c' THEN 'CASCADE'
        WHEN 'n' THEN 'SET NULL'
        WHEN 'd' THEN 'SET DEFAULT'
    END as delete_action
FROM pg_constraint 
WHERE conname = 'logs_auditoria_usuario_id_fkey';

-- Verificar se a coluna aceita NULL
SELECT 
    column_name,
    is_nullable,
    data_type
FROM information_schema.columns 
WHERE table_name = 'logs_auditoria' 
AND column_name = 'usuario_id';
