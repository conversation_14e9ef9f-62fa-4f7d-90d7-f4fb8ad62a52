const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Criar diretório de uploads se não existir
const uploadsDir = path.join(__dirname, '../uploads/logos');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('📁 Diretório de uploads criado:', uploadsDir);
}

console.log('🔧 Middleware de upload inicializado');
console.log('📁 Diretório de uploads:', uploadsDir);
console.log('📁 Diretório existe:', fs.existsSync(uploadsDir));

// Verificar permissões do diretório
try {
  const stats = fs.statSync(uploadsDir);
  console.log('📋 Permissões do diretório:', stats.mode.toString(8));
  console.log('👤 UID do processo:', process.getuid());
  console.log('👥 GID do processo:', process.getgid());

  // Testar escrita
  const testFile = path.join(uploadsDir, 'test-write.tmp');
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  console.log('✅ Teste de escrita: OK');
} catch (error) {
  console.error('❌ Erro ao verificar permissões:', error.message);
}

// Configuração do storage do multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Usar o código do cliente como nome do arquivo
    const codigoCliente = req.params.codigo || req.body.codigo_cliente;
    if (!codigoCliente) {
      return cb(new Error('Código do cliente é obrigatório para upload'));
    }
    
    const ext = path.extname(file.originalname).toLowerCase();
    const filename = `${codigoCliente}${ext}`;
    cb(null, filename);
  }
});

// Filtro para validar tipos de arquivo
const fileFilter = (req, file, cb) => {
  // Verificar tipo MIME
  const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (!allowedMimes.includes(file.mimetype)) {
    return cb(new Error('Apenas arquivos PNG e JPG são permitidos'), false);
  }
  
  // Verificar extensão
  const ext = path.extname(file.originalname).toLowerCase();
  const allowedExts = ['.png', '.jpg', '.jpeg'];
  if (!allowedExts.includes(ext)) {
    return cb(new Error('Apenas arquivos com extensão .png, .jpg ou .jpeg são permitidos'), false);
  }
  
  cb(null, true);
};

// Configuração do multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB em bytes
    files: 1 // Apenas um arquivo por vez
  }
});

// Função para remover logo antiga
const removeOldLogo = (codigoCliente) => {
  const extensions = ['.png', '.jpg', '.jpeg'];

  extensions.forEach(ext => {
    const oldFilePath = path.join(uploadsDir, `${codigoCliente}${ext}`);
    if (fs.existsSync(oldFilePath)) {
      try {
        // Verificar permissões antes de remover
        const stats = fs.statSync(oldFilePath);
        console.log(`📋 Permissões do arquivo: ${stats.mode.toString(8)}`);

        fs.unlinkSync(oldFilePath);
        console.log(`🗑️ Logo antiga removida: ${oldFilePath}`);
      } catch (error) {
        console.error('❌ Erro ao remover logo antiga:', error.message);
        console.error('❌ Código do erro:', error.code);
        console.error('❌ Caminho:', error.path);
      }
    }
  });
};

// Middleware para upload de logo
const uploadLogo = upload.single('logo');

// Wrapper para tratar erros do multer
const handleUpload = (req, res, next) => {
  console.log('🔄 Processando upload...');
  console.log('📁 Diretório de destino:', uploadsDir);

  uploadLogo(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      console.error('❌ Erro Multer:', err);
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          error: 'Arquivo muito grande. Tamanho máximo permitido: 2MB'
        });
      }
      if (err.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({
          error: 'Apenas um arquivo é permitido por vez'
        });
      }
      return res.status(400).json({
        error: `Erro no upload: ${err.message}`
      });
    } else if (err) {
      console.error('❌ Erro geral:', err);
      return res.status(400).json({
        error: err.message
      });
    }

    if (req.file) {
      console.log('✅ Arquivo recebido:', req.file.filename);
      console.log('📁 Salvo em:', req.file.path);
    } else {
      console.log('ℹ️ Nenhum arquivo enviado');
    }

    next();
  });
};

// Função para obter URL da logo
const getLogoUrl = (codigoCliente) => {
  if (!codigoCliente) return null;

  const extensions = ['.png', '.jpg', '.jpeg'];

  for (const ext of extensions) {
    const filePath = path.join(uploadsDir, `${codigoCliente}${ext}`);
    if (fs.existsSync(filePath)) {
      // Adicionar timestamp para evitar cache
      const timestamp = Date.now();
      return `/api/uploads/logos/${codigoCliente}${ext}?t=${timestamp}`;
    }
  }

  return null;
};

// Função para verificar se logo existe
const logoExists = (codigoCliente) => {
  if (!codigoCliente) return false;
  
  const extensions = ['.png', '.jpg', '.jpeg'];
  
  return extensions.some(ext => {
    const filePath = path.join(uploadsDir, `${codigoCliente}${ext}`);
    return fs.existsSync(filePath);
  });
};

module.exports = {
  handleUpload,
  removeOldLogo,
  getLogoUrl,
  logoExists,
  uploadsDir
};
