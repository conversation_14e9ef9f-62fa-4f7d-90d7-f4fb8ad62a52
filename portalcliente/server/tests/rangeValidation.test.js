/**
 * =====================================================
 * TESTES PARA VALIDAÇÃO DE RANGES DE GAVETAS
 * =====================================================
 * Testa todas as regras de negócio definidas em definindo_range.md
 */

const request = require('supertest');
const app = require('../index');
const { query } = require('../database/connection');

describe('Range Validation Tests', () => {
    let authToken;
    let testData = {
        codigo_cliente: 'TEST_001',
        codigo_estacao: 'ETEN_001',
        codigo_bloco: 'BL_001',
        codigo_sub_bloco_1: 'SUB_001',
        codigo_sub_bloco_2: 'SUB_002'
    };

    beforeAll(async () => {
        // Fazer login para obter token
        const loginResponse = await request(app)
            .post('/api/auth/login')
            .send({
                email: '<EMAIL>',
                senha: 'secret'
            });
        
        authToken = loginResponse.body.token;

        // Limpar dados de teste
        await cleanTestData();
        
        // Criar dados de teste
        await createTestData();
    });

    afterAll(async () => {
        // Limpar dados de teste
        await cleanTestData();
    });

    const cleanTestData = async () => {
        try {
            await query('DELETE FROM sepultamentos WHERE codigo_cliente = $1', [testData.codigo_cliente]);
            await query('DELETE FROM gavetas WHERE codigo_cliente = $1', [testData.codigo_cliente]);
            await query('DELETE FROM numeracoes_gavetas WHERE codigo_cliente = $1', [testData.codigo_cliente]);
            await query('DELETE FROM sub_blocos WHERE codigo_cliente = $1', [testData.codigo_cliente]);
            await query('DELETE FROM blocos WHERE codigo_cliente = $1', [testData.codigo_cliente]);
            await query('DELETE FROM produtos WHERE codigo_cliente = $1', [testData.codigo_cliente]);
            await query('DELETE FROM clientes WHERE codigo_cliente = $1', [testData.codigo_cliente]);
        } catch (error) {
            console.log('Erro na limpeza (esperado):', error.message);
        }
    };

    const createTestData = async () => {
        // Criar cliente
        await query(`
            INSERT INTO clientes (codigo_cliente, cnpj, nome_fantasia, razao_social, ativo)
            VALUES ($1, '12345678000199', 'Teste Cliente', 'Teste Cliente LTDA', true)
        `, [testData.codigo_cliente]);

        // Criar produto
        await query(`
            INSERT INTO produtos (codigo_cliente, codigo_estacao, denominacao, ativo)
            VALUES ($1, $2, 'Produto Teste', true)
        `, [testData.codigo_cliente, testData.codigo_estacao]);

        // Criar bloco
        await query(`
            INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao, ativo)
            VALUES ($1, $2, $3, 'Bloco Teste', true)
        `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco]);

        // Criar sub-blocos
        await query(`
            INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao, ativo)
            VALUES ($1, $2, $3, $4, 'Sub-bloco 1', true)
        `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco_1]);

        await query(`
            INSERT INTO sub_blocos (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, denominacao, ativo)
            VALUES ($1, $2, $3, $4, 'Sub-bloco 2', true)
        `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco_2]);
    };

    describe('Validação de Ranges', () => {
        test('Deve criar range válido sem conflitos', async () => {
            const response = await request(app)
                .post(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/ranges`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    numero_inicio: 1,
                    numero_fim: 10
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.range).toBeDefined();
        });

        test('Deve rejeitar range com sobreposição', async () => {
            // Primeiro range: 1-10 (já criado no teste anterior)
            // Segundo range: 5-15 (deve conflitar)
            const response = await request(app)
                .post(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/ranges`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    numero_inicio: 5,
                    numero_fim: 15
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.error).toContain('conflita');
        });

        test('Deve permitir ranges não sobrepostos no mesmo sub-bloco', async () => {
            const response = await request(app)
                .post(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/ranges`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    numero_inicio: 20,
                    numero_fim: 30
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
        });

        test('Deve permitir ranges sobrepostos em sub-blocos diferentes', async () => {
            const response = await request(app)
                .post(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_2}/ranges`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    numero_inicio: 1,
                    numero_fim: 10
                });

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
        });

        test('Deve rejeitar range inválido (início > fim)', async () => {
            const response = await request(app)
                .post(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_2}/ranges`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    numero_inicio: 15,
                    numero_fim: 10
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
        });
    });

    describe('Sincronização de Gavetas', () => {
        test('Deve sincronizar gavetas após criar range', async () => {
            // Verificar se gavetas foram criadas automaticamente
            const gavetas = await query(`
                SELECT COUNT(*) as total FROM gavetas 
                WHERE codigo_cliente = $1 AND codigo_estacao = $2 
                  AND codigo_bloco = $3 AND codigo_sub_bloco = $4
            `, [testData.codigo_cliente, testData.codigo_estacao, testData.codigo_bloco, testData.codigo_sub_bloco_1]);

            // Deve ter 20 gavetas (1-10 + 20-30)
            expect(parseInt(gavetas.rows[0].total)).toBe(20);
        });

        test('Deve sincronizar gavetas manualmente', async () => {
            const response = await request(app)
                .post(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/sync-gavetas`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
        });
    });

    describe('Validação de Deleção', () => {
        test('Deve validar deleção de produto', async () => {
            const response = await request(app)
                .post('/api/ranges/validate-deletion')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    tipo: 'produto',
                    codigo_cliente: testData.codigo_cliente,
                    codigo_estacao: testData.codigo_estacao
                });

            expect(response.status).toBe(200);
            expect(response.body.canDelete).toBe(false); // Deve ter blocos associados
        });

        test('Deve validar deleção de bloco', async () => {
            const response = await request(app)
                .post('/api/ranges/validate-deletion')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    tipo: 'bloco',
                    codigo_cliente: testData.codigo_cliente,
                    codigo_estacao: testData.codigo_estacao,
                    codigo_bloco: testData.codigo_bloco
                });

            expect(response.status).toBe(200);
            expect(response.body.canDelete).toBe(false); // Deve ter sub-blocos associados
        });

        test('Deve validar deleção de sub-bloco', async () => {
            const response = await request(app)
                .post('/api/ranges/validate-deletion')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    tipo: 'sub_bloco',
                    codigo_cliente: testData.codigo_cliente,
                    codigo_estacao: testData.codigo_estacao,
                    codigo_bloco: testData.codigo_bloco,
                    codigo_sub_bloco: testData.codigo_sub_bloco_1
                });

            expect(response.status).toBe(200);
            expect(response.body.canDelete).toBe(false); // Deve ter numerações associadas
        });
    });

    describe('Listagem de Ranges', () => {
        test('Deve listar ranges de um sub-bloco', async () => {
            const response = await request(app)
                .get(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/ranges`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.ranges).toHaveLength(2); // 1-10 e 20-30
        });

        test('Deve incluir estatísticas de gavetas nos ranges', async () => {
            const response = await request(app)
                .get(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/ranges`)
                .set('Authorization', `Bearer ${authToken}`);

            const ranges = response.body.ranges;
            expect(ranges[0]).toHaveProperty('total_gavetas');
            expect(ranges[0]).toHaveProperty('gavetas_disponiveis');
            expect(ranges[0]).toHaveProperty('gavetas_ocupadas');
        });
    });

    describe('Atualização de Ranges', () => {
        test('Deve atualizar range sem conflitos', async () => {
            // Buscar um range existente
            const listResponse = await request(app)
                .get(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_1}/ranges`)
                .set('Authorization', `Bearer ${authToken}`);

            const rangeId = listResponse.body.ranges[0].id;

            const response = await request(app)
                .put(`/api/ranges/ranges/${rangeId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    codigo_cliente: testData.codigo_cliente,
                    codigo_estacao: testData.codigo_estacao,
                    codigo_bloco: testData.codigo_bloco,
                    codigo_sub_bloco: testData.codigo_sub_bloco_1,
                    numero_inicio: 1,
                    numero_fim: 5 // Reduzir o range
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
        });
    });

    describe('Deleção de Ranges', () => {
        test('Deve deletar range sem gavetas ocupadas', async () => {
            // Buscar um range existente
            const listResponse = await request(app)
                .get(`/api/ranges/sub-blocos/${testData.codigo_cliente}/${testData.codigo_estacao}/${testData.codigo_bloco}/${testData.codigo_sub_bloco_2}/ranges`)
                .set('Authorization', `Bearer ${authToken}`);

            const rangeId = listResponse.body.ranges[0].id;

            const response = await request(app)
                .delete(`/api/ranges/ranges/${rangeId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    codigo_cliente: testData.codigo_cliente,
                    codigo_estacao: testData.codigo_estacao,
                    codigo_bloco: testData.codigo_bloco,
                    codigo_sub_bloco: testData.codigo_sub_bloco_2
                });

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
        });
    });
});
