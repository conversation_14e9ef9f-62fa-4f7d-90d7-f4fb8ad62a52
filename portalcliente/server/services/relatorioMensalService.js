/**
 * SERVIÇO: RELATÓRIO MENSAL
 * Funções para cálculos e consultas SQL do relatório mensal
 * Consumo elétrico baseado na tabela dadosc conforme solicitação do usuário
 * Outros dados baseados na tabela daily_report conforme instrucao.md
 * Autor: Sistema Multi-Agente
 * Data: 2025-01-21
 * Atualização: 2025-07-29 - Consumo elétrico da tabela dadosc
 */

const { query } = require('../database/connection');

/**
 * Calcula dados universais do relatório mensal
 * @param {Array} dadosConsumo - Dados da tabela dadosc
 * @param {number} custoKwh - Custo médio do kWh
 * @returns {Object} Dados universais calculados
 */
function calcularDadosUniversais(dadosConsumo, custoKwh) {
  if (!dadosConsumo || dadosConsumo.length === 0) {
    return {
      consumoEletricoTotal: 0,
      custoOperacao: 0
    };
  }

  // Somar todos os consumos elétricos da tabela dadosc
  const consumoEletricoTotal = dadosConsumo.reduce((total, registro) => {
    const consumo = parseFloat(registro.consumo_eletrico) || 0;
    return total + consumo;
  }, 0);

  // Calcular custo de operação
  const custoOperacao = consumoEletricoTotal * custoKwh;

  console.log(`📊 Consumo elétrico calculado: ${dadosConsumo.length} registros, total: ${consumoEletricoTotal.toFixed(2)} kWh`);

  return {
    consumoEletricoTotal: consumoEletricoTotal.toFixed(2),
    custoOperacao: custoOperacao.toFixed(2)
  };
}

/**
 * Calcula valores mistos (soma ou média de todos os blocos)
 * @param {Array} dadosOperacionais - Dados da daily_report
 * @returns {Object} Valores mistos calculados
 */
function calcularValoresMistos(dadosOperacionais) {
  if (!dadosOperacionais || dadosOperacionais.length === 0) {
    return {
      volumeArTotal: 0,
      tempoTotalTrocasGasosas: 0,
      fluxoMedioAr: 0,
      potenciaMediaPercentual: 0,
      temperaturaMediaSistema: 0,
      umidadeRelativaMediaSistema: 0,
      pressaoAlvoEstanqueidade: 0,
      pressaoMediaObtidaEstanqueidade: 0
    };
  }

  // Somar volume de ar total
  const volumeArTotal = dadosOperacionais.reduce((total, dia) => {
    return total + (parseFloat(dia.volume_de_ar_total) || 0);
  }, 0);

  // Somar tempo total de trocas gasosas (duração total em segundos)
  const tempoTotalTrocasGasosas = dadosOperacionais.reduce((total, dia) => {
    return total + (parseInt(dia.duracao_total) || 0);
  }, 0);

  // Calcular fluxo médio de ar (m³/h)
  // volume_de_ar_total (m³) / duracao_total (segundos) * 3600 (para converter para horas)
  let fluxoMedioAr = 0;
  if (tempoTotalTrocasGasosas > 0) {
    fluxoMedioAr = (volumeArTotal / tempoTotalTrocasGasosas) * 3600;
  }

  // Calcular médias
  const totalDias = dadosOperacionais.length;
  
  const potenciaMediaPercentual = dadosOperacionais.reduce((total, dia) => {
    return total + (parseFloat(dia.potencia_media_percentual) || 0);
  }, 0) / totalDias;

  const temperaturaMediaSistema = dadosOperacionais.reduce((total, dia) => {
    const tempManha = parseFloat(dia.temperatura_manha) || 0;
    const tempTarde = parseFloat(dia.temperatura_tarde) || 0;
    const tempNoite = parseFloat(dia.temperatura_noite) || 0;
    return total + ((tempManha + tempTarde + tempNoite) / 3);
  }, 0) / totalDias;

  const umidadeRelativaMediaSistema = dadosOperacionais.reduce((total, dia) => {
    const umidManha = parseFloat(dia.umidade_manha) || 0;
    const umidTarde = parseFloat(dia.umidade_tarde) || 0;
    const umidNoite = parseFloat(dia.umidade_noite) || 0;
    return total + ((umidManha + umidTarde + umidNoite) / 3);
  }, 0) / totalDias;

  const pressaoAlvoEstanqueidade = dadosOperacionais.reduce((total, dia) => {
    return total + (parseFloat(dia.pressao_ideal_estanque || dia.pressao_ideal_maxima) || 0);
  }, 0) / totalDias;

  const pressaoMediaObtidaEstanqueidade = dadosOperacionais.reduce((total, dia) => {
    return total + (parseFloat(dia.pressao_obtida_estanque || dia.pressao_obtida_maxima) || 0);
  }, 0) / totalDias;

  return {
    volumeArTotal: volumeArTotal.toFixed(2),
    tempoTotalTrocasGasosas: tempoTotalTrocasGasosas,
    fluxoMedioAr: fluxoMedioAr.toFixed(2),
    potenciaMediaPercentual: potenciaMediaPercentual.toFixed(2),
    temperaturaMediaSistema: temperaturaMediaSistema.toFixed(2),
    umidadeRelativaMediaSistema: umidadeRelativaMediaSistema.toFixed(2),
    pressaoAlvoEstanqueidade: pressaoAlvoEstanqueidade.toFixed(2),
    pressaoMediaObtidaEstanqueidade: pressaoMediaObtidaEstanqueidade.toFixed(2)
  };
}

/**
 * Agrupa dados operacionais por bloco
 * @param {Array} dadosOperacionais - Dados da daily_report
 * @returns {Object} Dados agrupados por código do bloco
 */
function agruparDadosPorBloco(dadosOperacionais) {
  const dadosPorBloco = {};

  dadosOperacionais.forEach(dia => {
    const codigoBloco = dia.codigo_bloco;
    
    if (!dadosPorBloco[codigoBloco]) {
      dadosPorBloco[codigoBloco] = [];
    }
    
    dadosPorBloco[codigoBloco].push(dia);
  });

  return dadosPorBloco;
}

/**
 * Calcula indicadores diários por bloco com dados sequenciais para gráficos
 * @param {Array} dadosBloco - Dados de um bloco específico
 * @returns {Object} Indicadores calculados para o bloco
 */
function calcularIndicadoresPorBloco(dadosBloco) {
  if (!dadosBloco || dadosBloco.length === 0) {
    return {
      temperaturaMedia: [],
      umidadeRelativaMedia: [],
      pressaoEstanqueidade: [],
      volumeArPotencia: [],
      dadosGraficos: {
        temperatura: [],
        umidade: [],
        labels: []
      },
      tabelaDados: [],
      dadosTrocasGasosas: [],
      dadosEstanqueidade: []
    };
  }

  // Dados originais (médias diárias)
  const temperaturaMedia = dadosBloco.map(dia => ({
    data: dia.data_leitura_formatada,
    temperatura: ((parseFloat(dia.temperatura_manha) || 0) +
                  (parseFloat(dia.temperatura_tarde) || 0) +
                  (parseFloat(dia.temperatura_noite) || 0)) / 3
  }));

  const umidadeRelativaMedia = dadosBloco.map(dia => ({
    data: dia.data_leitura_formatada,
    umidade: ((parseFloat(dia.umidade_manha) || 0) +
              (parseFloat(dia.umidade_tarde) || 0) +
              (parseFloat(dia.umidade_noite) || 0)) / 3
  }));

  const pressaoEstanqueidade = dadosBloco.map(dia => ({
    data: dia.data_leitura_formatada,
    pressaoAlvo: parseFloat(dia.pressao_ideal_estanque || dia.pressao_ideal_maxima) || 0,
    pressaoObtida: parseFloat(dia.pressao_obtida_estanque || dia.pressao_obtida_maxima) || 0
  }));

  const volumeArPotencia = dadosBloco.map(dia => ({
    data: dia.data_leitura_formatada,
    volumeAr: parseFloat(dia.volume_de_ar_total) || 0,
    potenciaPercentual: parseFloat(dia.potencia_media_percentual) || 0
  }));

  // Novos dados para Trocas Gasosas e Estanqueidade do Sistema
  const dadosTrocasGasosas = dadosBloco.map(dia => ({
    data: dia.data_leitura_formatada,
    volumeArTotal: parseFloat(dia.volume_de_ar_total) || 0,
    pressaoMedia: parseFloat(dia.pressao_media) || 0,
    potenciaMediaPercentual: parseFloat(dia.potencia_media_percentual) || 0
  }));

  const dadosEstanqueidade = dadosBloco.map(dia => ({
    data: dia.data_leitura_formatada,
    duracaoMaxima: parseFloat(dia.duracao_maxima) || 0, // Duração em segundos
    pressaoObtidaMaxima: parseFloat(dia.pressao_obtida_estanque || dia.pressao_obtida_maxima) || 0,
    valorAnalogoPercentual: parseFloat(dia.valor_analogo_percentual) || 0
  }));

  // Dados sequenciais para gráficos (manhã, tarde, noite)
  const dadosGraficos = {
    temperatura: [],
    umidade: [],
    labels: []
  };

  const tabelaDados = [];

  // Ordenar dados por data
  const dadosOrdenados = dadosBloco.sort((a, b) => new Date(a.data_leitura_formatada) - new Date(b.data_leitura_formatada));

  dadosOrdenados.forEach(dia => {
    // CORREÇÃO: Usar UTC para evitar problemas de timezone
    const dataFormatada = new Date(dia.data_leitura_formatada + 'T00:00:00.000Z').toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo'
    });

    // Manhã
    dadosGraficos.temperatura.push(parseFloat(dia.temperatura_manha) || 0);
    dadosGraficos.umidade.push(parseFloat(dia.umidade_manha) || 0);
    dadosGraficos.labels.push(`${dataFormatada} - Manhã`);

    // Tarde
    dadosGraficos.temperatura.push(parseFloat(dia.temperatura_tarde) || 0);
    dadosGraficos.umidade.push(parseFloat(dia.umidade_tarde) || 0);
    dadosGraficos.labels.push(`${dataFormatada} - Tarde`);

    // Noite
    dadosGraficos.temperatura.push(parseFloat(dia.temperatura_noite) || 0);
    dadosGraficos.umidade.push(parseFloat(dia.umidade_noite) || 0);
    dadosGraficos.labels.push(`${dataFormatada} - Noite`);

    // Dados para tabela
    tabelaDados.push({
      data: dataFormatada,
      tempManha: (parseFloat(dia.temperatura_manha) || 0).toFixed(1),
      tempTarde: (parseFloat(dia.temperatura_tarde) || 0).toFixed(1),
      tempNoite: (parseFloat(dia.temperatura_noite) || 0).toFixed(1),
      umidManha: (parseFloat(dia.umidade_manha) || 0).toFixed(1),
      umidTarde: (parseFloat(dia.umidade_tarde) || 0).toFixed(1),
      umidNoite: (parseFloat(dia.umidade_noite) || 0).toFixed(1)
    });
  });

  return {
    temperaturaMedia,
    umidadeRelativaMedia,
    pressaoEstanqueidade,
    volumeArPotencia,
    dadosGraficos,
    tabelaDados,
    dadosTrocasGasosas,
    dadosEstanqueidade
  };
}

/**
 * Busca dados de consumo elétrico da tabela dadosc
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação
 * @param {string} dataInicio - Data de início (YYYY-MM-DD)
 * @param {string} dataFim - Data de fim (YYYY-MM-DD)
 * @returns {Array} Dados de consumo elétrico
 */
async function buscarDadosConsumo(codigoCliente, codigoEstacao, dataInicio, dataFim) {
  try {
    console.log('⚡ Buscando dados de consumo elétrico da tabela dadosc:', {
      codigoCliente,
      codigoEstacao,
      dataInicio,
      dataFim
    });

    const consumoQuery = `
      SELECT
        codigo_cliente,
        codigo_estacao,
        data_leitura,
        consumo_eletrico,
        valor_consumo
      FROM dadosc
      WHERE codigo_cliente = $1
        AND codigo_estacao = $2
        AND DATE(data_leitura) >= $3::date
        AND DATE(data_leitura) <= $4::date
        AND consumo_eletrico IS NOT NULL
      ORDER BY data_leitura ASC
    `;

    const result = await query(consumoQuery, [codigoCliente, codigoEstacao, dataInicio, dataFim]);
    console.log(`⚡ Encontrados ${result.rows.length} registros de consumo`);

    return result.rows;

  } catch (error) {
    console.error('❌ Erro ao buscar dados de consumo:', error);
    throw error;
  }
}

/**
 * Busca total de gavetas para um produto específico
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação/produto
 * @returns {number} Total de gavetas cadastradas
 */
async function buscarTotalGavetas(codigoCliente, codigoEstacao) {
  try {
    console.log('🔢 Buscando total de gavetas para produto:', { codigoCliente, codigoEstacao });

    const gavetasQuery = `
      SELECT COUNT(*) as total_gavetas
      FROM gavetas g
      WHERE g.codigo_cliente = $1
        AND g.codigo_estacao = $2
        AND g.ativo = true
    `;

    const result = await query(gavetasQuery, [codigoCliente, codigoEstacao]);
    const totalGavetas = parseInt(result.rows[0].total_gavetas) || 0;

    console.log(`🔢 Total de gavetas encontradas: ${totalGavetas}`);
    return totalGavetas;

  } catch (error) {
    console.error('❌ Erro ao buscar total de gavetas:', error);
    throw error;
  }
}

/**
 * Busca total de sepultamentos realizados no período específico
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação/produto
 * @param {string} dataInicio - Data de início (YYYY-MM-DD)
 * @param {string} dataFim - Data de fim (YYYY-MM-DD)
 * @returns {number} Total de sepultamentos no período
 */
async function buscarTotalSepultamentosMes(codigoCliente, codigoEstacao, dataInicio, dataFim) {
  try {
    console.log('⚰️ Buscando total de sepultamentos no período:', { codigoCliente, codigoEstacao, dataInicio, dataFim });

    // CORREÇÃO: Usar datas originais sem offset adicional
    // O período já está correto (01/07 a 31/07), não aplicar offset
    const dataInicioFormatada = dataInicio;
    const dataFimFormatada = dataFim;

    console.log('⚰️ Usando datas originais (sem offset):', {
      dataInicio: dataInicioFormatada,
      dataFim: dataFimFormatada,
      observacao: 'Removido offset desnecessário que causava perda do primeiro dia'
    });

    // Query para debug - buscar registros individuais primeiro
    const debugQuery = `
      SELECT s.data_sepultamento, s.nome_sepultado, s.status_exumacao, s.exumado_em
      FROM sepultamentos s
      WHERE s.codigo_cliente = $1
        AND s.codigo_estacao = $2
        AND DATE(s.data_sepultamento) >= $3::date
        AND DATE(s.data_sepultamento) <= $4::date
        AND s.ativo = true
      ORDER BY s.data_sepultamento
    `;

    const debugResult = await query(debugQuery, [codigoCliente, codigoEstacao, dataInicioFormatada, dataFimFormatada]);
    console.log(`🔍 DEBUG: Registros encontrados no período ${dataInicio} a ${dataFim}:`, debugResult.rows.length);

    if (debugResult.rows.length > 0) {
      console.log('🔍 DEBUG: Primeiros 5 registros:', debugResult.rows.slice(0, 5).map(r => ({
        data: r.data_sepultamento,
        nome: r.nome_sepultado,
        status_exumacao: r.status_exumacao,
        exumado_em: r.exumado_em
      })));
      console.log('🔍 DEBUG: Últimos 5 registros:', debugResult.rows.slice(-5).map(r => ({
        data: r.data_sepultamento,
        nome: r.nome_sepultado,
        status_exumacao: r.status_exumacao,
        exumado_em: r.exumado_em
      })));

      // CORREÇÃO: Contar quantos estão exumados vs não exumados
      const exumados = debugResult.rows.filter(r => r.status_exumacao === true).length;
      const naoExumados = debugResult.rows.filter(r => r.status_exumacao === false || r.status_exumacao === null).length;
      console.log('🔍 DEBUG: Status dos sepultamentos:', { total: debugResult.rows.length, exumados, naoExumados });
    }

    // CORREÇÃO: Usar a mesma lógica da aba Relatórios - buscar TODOS os sepultamentos do período
    const sepultamentosQuery = `
      SELECT COUNT(*) as total_sepultamentos
      FROM sepultamentos s
      JOIN produtos p ON p.codigo_estacao = s.codigo_estacao AND p.codigo_cliente = s.codigo_cliente
      WHERE s.codigo_cliente = $1
        AND s.codigo_estacao = $2
        AND s.data_sepultamento >= $3
        AND s.data_sepultamento <= $4
    `;

    const result = await query(sepultamentosQuery, [codigoCliente, codigoEstacao, dataInicioFormatada, dataFimFormatada]);
    const totalSepultamentos = parseInt(result.rows[0].total_sepultamentos) || 0;

    console.log(`⚰️ Total de sepultamentos no período: ${totalSepultamentos}`);
    return totalSepultamentos;

  } catch (error) {
    console.error('❌ Erro ao buscar total de sepultamentos:', error);
    throw error;
  }
}

/**
 * Busca total de exumações realizadas no período específico
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação/produto
 * @param {string} dataInicio - Data de início (YYYY-MM-DD)
 * @param {string} dataFim - Data de fim (YYYY-MM-DD)
 * @returns {number} Total de exumações no período
 */
async function buscarTotalExumacoesMes(codigoCliente, codigoEstacao, dataInicio, dataFim) {
  try {
    console.log('🏺 Buscando total de exumações no período:', { codigoCliente, codigoEstacao, dataInicio, dataFim });

    // CORREÇÃO: Usar datas originais sem offset adicional
    // O período já está correto (01/07 a 31/07), não aplicar offset
    const dataInicioFormatada = dataInicio;
    const dataFimFormatada = dataFim;

    console.log('🏺 Usando datas originais (sem offset):', {
      dataInicio: dataInicioFormatada,
      dataFim: dataFimFormatada,
      observacao: 'Removido offset desnecessário que causava perda do primeiro dia'
    });

    // CORREÇÃO: Usar a mesma lógica da aba Relatórios para exumações com offset
    const exumacoesQuery = `
      SELECT COUNT(*) as total_exumacoes
      FROM sepultamentos s
      JOIN produtos p ON p.codigo_estacao = s.codigo_estacao AND p.codigo_cliente = s.codigo_cliente
      WHERE s.codigo_cliente = $1
        AND s.codigo_estacao = $2
        AND s.exumado_em >= $3
        AND s.exumado_em <= $4
        AND s.status_exumacao = true
    `;

    const result = await query(exumacoesQuery, [codigoCliente, codigoEstacao, dataInicioFormatada, dataFimFormatada]);
    const totalExumacoes = parseInt(result.rows[0].total_exumacoes) || 0;

    console.log(`🏺 Total de exumações no período (com offset): ${totalExumacoes}`);
    return totalExumacoes;

  } catch (error) {
    console.error('❌ Erro ao buscar total de exumações:', error);
    throw error;
  }
}

/**
 * Busca o total de atendimentos únicos no período
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação
 * @param {string} dataInicio - Data de início (YYYY-MM-DD)
 * @param {string} dataFim - Data de fim (YYYY-MM-DD)
 * @returns {Promise<number>} Total de atendimentos únicos
 */
async function buscarTotalAtendimentosPeriodo(codigoCliente, codigoEstacao, dataInicio, dataFim) {
  try {
    console.log(`🎫 Buscando total de atendimentos no período para ${codigoCliente}/${codigoEstacao} de ${dataInicio} a ${dataFim}`);

    const atendimentosQuery = `
      SELECT COUNT(DISTINCT a.numero_chamado) as total_atendimentos
      FROM atendimentos_eten a
      WHERE a.codigo_cliente = $1
        AND a.codigo_estacao = $2
        AND a.data_fechamento IS NOT NULL
        AND DATE(a.data_fechamento) >= $3::date
        AND DATE(a.data_fechamento) <= $4::date
    `;

    const result = await query(atendimentosQuery, [codigoCliente, codigoEstacao, dataInicio, dataFim]);
    const totalAtendimentos = parseInt(result.rows[0].total_atendimentos) || 0;

    console.log(`🎫 Total de atendimentos encontrados: ${totalAtendimentos}`);
    return totalAtendimentos;

  } catch (error) {
    console.error('❌ Erro ao buscar total de atendimentos no período:', error);
    throw error;
  }
}

/**
 * Busca o plano de cobertura do sistema
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação
 * @returns {Promise<string>} Plano de cobertura do sistema
 */
async function buscarPlanoCobertura(codigoCliente, codigoEstacao) {
  try {
    console.log(`📋 Buscando plano de cobertura para ${codigoCliente}/${codigoEstacao}`);

    const planoQuery = `
      SELECT p.situacao
      FROM produtos p
      WHERE p.codigo_cliente = $1
        AND p.codigo_estacao = $2
        AND p.ativo = true
    `;

    const result = await query(planoQuery, [codigoCliente, codigoEstacao]);
    const planoCobertura = result.rows[0]?.situacao || 'Não informado';

    console.log(`📋 Plano de cobertura encontrado: ${planoCobertura}`);
    return planoCobertura;

  } catch (error) {
    console.error('❌ Erro ao buscar plano de cobertura:', error);
    throw error;
  }
}

/**
 * Busca dados de manutenção do produto
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação
 * @returns {Promise<Object>} Dados de manutenção (última e próxima)
 */
async function buscarDadosManutencao(codigoCliente, codigoEstacao) {
  try {
    console.log(`🔧 Buscando dados de manutenção para ${codigoCliente}/${codigoEstacao}`);

    const manutencaoQuery = `
      SELECT
        p.ultima_manutencao_feita,
        p.proxima_manutencao_prevista
      FROM produtos p
      WHERE p.codigo_cliente = $1
        AND p.codigo_estacao = $2
        AND p.ativo = true
    `;

    const result = await query(manutencaoQuery, [codigoCliente, codigoEstacao]);
    const dadosManutencao = {
      ultimaManutencao: result.rows[0]?.ultima_manutencao_feita || null,
      proximaManutencao: result.rows[0]?.proxima_manutencao_prevista || null
    };

    console.log(`🔧 Dados de manutenção encontrados:`, dadosManutencao);

    // CORREÇÃO: Log detalhado para debug das datas de manutenção
    console.log('🔍 DEBUG: Dados brutos de manutenção:', {
      ultima_raw: result.rows[0]?.ultima_manutencao_feita,
      proxima_raw: result.rows[0]?.proxima_manutencao_prevista,
      ultima_tipo: typeof result.rows[0]?.ultima_manutencao_feita,
      proxima_tipo: typeof result.rows[0]?.proxima_manutencao_prevista,
      row_completa: result.rows[0]
    });

    return dadosManutencao;

  } catch (error) {
    console.error('❌ Erro ao buscar dados de manutenção:', error);
    throw error;
  }
}

/**
 * Busca lista de atendimentos do período
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação
 * @param {string} dataInicio - Data de início (YYYY-MM-DD)
 * @param {string} dataFim - Data de fim (YYYY-MM-DD)
 * @returns {Promise<Array>} Lista de atendimentos do período
 */
async function buscarAtendimentosPeriodo(codigoCliente, codigoEstacao, dataInicio, dataFim) {
  try {
    console.log(`📋 Buscando lista de atendimentos no período para ${codigoCliente}/${codigoEstacao} de ${dataInicio} a ${dataFim}`);

    const atendimentosQuery = `
      SELECT
        a.numero_chamado,
        a.data_abertura,
        a.tecnico_responsavel,
        a.descricao_atendimento,
        a.observacao_atendimento,
        a.status_atendimento,
        a.data_fechamento,
        a.solucao
      FROM atendimentos_eten a
      WHERE a.codigo_cliente = $1
        AND a.codigo_estacao = $2
        AND a.data_fechamento IS NOT NULL
        AND DATE(a.data_fechamento) >= $3::date
        AND DATE(a.data_fechamento) <= $4::date
      ORDER BY a.data_fechamento DESC, a.numero_chamado
    `;

    const result = await query(atendimentosQuery, [codigoCliente, codigoEstacao, dataInicio, dataFim]);
    const listaAtendimentos = result.rows || [];

    console.log(`📋 Total de atendimentos listados: ${listaAtendimentos.length}`);

    // CORREÇÃO: Log detalhado dos atendimentos para debug
    if (listaAtendimentos.length > 0) {
      console.log('🔍 DEBUG: Primeiros atendimentos encontrados:', listaAtendimentos.slice(0, 3).map(a => ({
        numero_chamado: a.numero_chamado,
        data_abertura: a.data_abertura,
        data_fechamento: a.data_fechamento,
        tecnico: a.tecnico_responsavel,
        tipo_abertura: typeof a.data_abertura,
        tipo_fechamento: typeof a.data_fechamento
      })));
    }

    return listaAtendimentos;

  } catch (error) {
    console.error('❌ Erro ao buscar lista de atendimentos:', error);
    throw error;
  }
}

/**
 * Busca dados completos para o relatório mensal
 * @param {string} codigoCliente - Código do cliente
 * @param {string} codigoEstacao - Código da estação/produto
 * @param {number} mes - Mês (1-12)
 * @param {number} ano - Ano
 * @param {number} custoKwh - Custo do kWh
 * @returns {Object} Dados completos do relatório
 */
async function buscarDadosRelatorioCompleto(codigoCliente, codigoEstacao, mes, ano, custoKwh) {
  try {
    console.log('📊 Buscando dados completos do relatório mensal...');

    // Buscar dados do cliente
    const clienteQuery = `
      SELECT 
        codigo_cliente,
        cnpj,
        razao_social,
        nome_fantasia,
        logradouro,
        numero,
        complemento,
        bairro,
        cidade,
        estado,
        cep
      FROM clientes 
      WHERE codigo_cliente = $1
    `;

    const clienteResult = await query(clienteQuery, [codigoCliente]);
    const cliente = clienteResult.rows[0];

    // Buscar dados do produto
    const produtoQuery = `
      SELECT 
        codigo_estacao,
        denominacao
      FROM produtos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2
    `;

    const produtoResult = await query(produtoQuery, [codigoCliente, codigoEstacao]);
    const produto = produtoResult.rows[0];

    // Buscar dados operacionais com informações correlacionadas
    const dadosOperacionaisQuery = `
      SELECT
        dr.*,
        c.razao_social,
        c.nome_fantasia,
        p.denominacao as produto_denominacao,
        b.denominacao as bloco_denominacao
      FROM daily_report dr
      JOIN clientes c ON dr.codigo_cliente = c.codigo_cliente
      JOIN produtos p ON dr.codigo_cliente = p.codigo_cliente
                     AND dr.codigo_estacao = p.codigo_estacao
      JOIN blocos b ON dr.codigo_cliente = b.codigo_cliente
                   AND dr.codigo_estacao = b.codigo_estacao
                   AND dr.codigo_bloco = b.codigo_bloco
      WHERE dr.codigo_cliente = $1
        AND dr.codigo_estacao = $2
        AND DATE(dr.data_leitura_formatada) >= $3::date
        AND DATE(dr.data_leitura_formatada) <= $4::date
        AND c.ativo = true
        AND p.ativo = true
        AND b.ativo = true
      ORDER BY dr.data_leitura_formatada ASC, dr.codigo_bloco ASC
    `;

    // Calcular período específico baseado no mês/ano fornecido
    // CORREÇÃO: Usar UTC para evitar problemas de timezone
    const dataInicio = `${ano}-${mes.toString().padStart(2, '0')}-01`;

    // Calcular último dia do mês usando UTC para evitar problemas de timezone
    const ultimoDiaDoMes = new Date(Date.UTC(ano, mes, 0)).getUTCDate();
    const dataFim = `${ano}-${mes.toString().padStart(2, '0')}-${ultimoDiaDoMes.toString().padStart(2, '0')}`;

    console.log('📅 Período calculado para dados operacionais (UTC):', {
      dataInicio,
      dataFim,
      mes,
      ano,
      ultimoDiaDoMes
    });

    // CORREÇÃO TIMEZONE: Log detalhado para validação
    console.log('🔍 TIMEZONE DEBUG - Período calculado:', {
      entrada: { mes, ano },
      calculado: { dataInicio, dataFim },
      ultimoDiaCalculado: ultimoDiaDoMes,
      observacao: 'Usando UTC para evitar problemas de timezone'
    });

    const dadosResult = await query(dadosOperacionaisQuery, [codigoCliente, codigoEstacao, dataInicio, dataFim]);
    const dadosOperacionais = dadosResult.rows;

    // Buscar blocos ativos que têm dados na daily_report
    const blocosQuery = `
      SELECT DISTINCT
        b.codigo_bloco,
        b.denominacao as nome_bloco
      FROM blocos b
      JOIN daily_report dr ON b.codigo_cliente = dr.codigo_cliente
                          AND b.codigo_estacao = dr.codigo_estacao
                          AND b.codigo_bloco = dr.codigo_bloco
      WHERE b.codigo_cliente = $1
        AND b.codigo_estacao = $2
        AND b.ativo = true
      ORDER BY b.codigo_bloco ASC
    `;

    const blocosResult = await query(blocosQuery, [codigoCliente, codigoEstacao]);
    const blocos = blocosResult.rows;

    // Buscar dados de consumo elétrico da tabela dadosc
    const dadosConsumo = await buscarDadosConsumo(codigoCliente, codigoEstacao, dataInicio, dataFim);

    // Calcular dados universais e mistos
    const dadosUniversais = calcularDadosUniversais(dadosConsumo, custoKwh);
    const valoresMistos = calcularValoresMistos(dadosOperacionais);

    // Agrupar dados por bloco e calcular indicadores
    const dadosPorBloco = agruparDadosPorBloco(dadosOperacionais);
    const indicadoresPorBloco = {};

    Object.keys(dadosPorBloco).forEach(codigoBloco => {
      indicadoresPorBloco[codigoBloco] = calcularIndicadoresPorBloco(dadosPorBloco[codigoBloco]);
    });

    // Buscar dados adicionais para o Resumo Executivo
    console.log('📊 Buscando dados adicionais para Resumo Executivo...');

    const totalGavetas = await buscarTotalGavetas(codigoCliente, codigoEstacao);
    const totalSepultamentosMes = await buscarTotalSepultamentosMes(codigoCliente, codigoEstacao, dataInicio, dataFim);
    const totalExumacoesMes = await buscarTotalExumacoesMes(codigoCliente, codigoEstacao, dataInicio, dataFim);

    // Buscar dados adicionais para atendimentos e manutenção
    console.log('🎫 Buscando dados de atendimentos e manutenção...');

    const totalAtendimentos = await buscarTotalAtendimentosPeriodo(codigoCliente, codigoEstacao, dataInicio, dataFim);
    const planoCobertura = await buscarPlanoCobertura(codigoCliente, codigoEstacao);
    const dadosManutencao = await buscarDadosManutencao(codigoCliente, codigoEstacao);
    const listaAtendimentos = await buscarAtendimentosPeriodo(codigoCliente, codigoEstacao, dataInicio, dataFim);

    // Dados adicionais para o Resumo Executivo
    const dadosAdicionais = {
      totalGavetas,
      totalSepultamentosMes,
      totalExumacoesMes,
      totalAtendimentos,
      planoCobertura,
      dadosManutencao,
      listaAtendimentos
    };

    console.log('📊 Dados adicionais coletados:', dadosAdicionais);

    return {
      cliente,
      produto,
      dadosOperacionais,
      blocos,
      dadosUniversais,
      valoresMistos,
      dadosPorBloco,
      indicadoresPorBloco,
      dadosAdicionais,
      parametros: {
        mes,
        ano,
        custoKwh
      }
    };

  } catch (error) {
    console.error('❌ Erro ao buscar dados do relatório:', error);
    throw error;
  }
}

module.exports = {
  calcularDadosUniversais,
  calcularValoresMistos,
  agruparDadosPorBloco,
  calcularIndicadoresPorBloco,
  buscarDadosConsumo,
  buscarTotalGavetas,
  buscarTotalSepultamentosMes,
  buscarTotalExumacoesMes,
  buscarTotalAtendimentosPeriodo,
  buscarPlanoCobertura,
  buscarDadosManutencao,
  buscarAtendimentosPeriodo,
  buscarDadosRelatorioCompleto
};
