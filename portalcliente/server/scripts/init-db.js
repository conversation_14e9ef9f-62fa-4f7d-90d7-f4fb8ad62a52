const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Configuração do banco de dados
const pool = new Pool({
  host: process.env.DB_HOST || '************',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'dbetens',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'ab3780bd73ee4e2804d566ce6fd96209',
  ssl: false,
});

async function initializeDatabase() {
  try {
    console.log('🔄 Conectando ao banco de dados...');
    
    // Ler o arquivo schema.sql
    const schemaPath = path.join(__dirname, '../database/schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('📋 Executando schema SQL...');
    
    // Executar o schema
    await pool.query(schema);
    
    console.log('✅ Banco de dados inicializado com sucesso!');
    
    // Verificar se as tabelas foram criadas
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('📊 Tabelas criadas:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    // Verificar se o usuário admin foi criado
    const adminCheck = await pool.query(
      "SELECT email FROM usuarios WHERE email = 'admin'"
    );
    
    if (adminCheck.rows.length > 0) {
      console.log('👤 Usuário admin criado com sucesso');
      console.log('📧 Email: admin');
      console.log('🔑 Senha: adminnbr5410!');
    } else {
      console.log('⚠️  Usuário admin não foi criado automaticamente');
    }
    
  } catch (error) {
    console.error('❌ Erro ao inicializar banco de dados:', error);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('🔌 Conexão com banco de dados encerrada');
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  initializeDatabase();
}

module.exports = { initializeDatabase };
