const { query } = require('../database/connection');
const fs = require('fs');
const path = require('path');

async function migrateLogo() {
  console.log('🔄 Iniciando migração para adicionar coluna logo_path...');
  
  try {
    // Ler o arquivo SQL de migração
    const migrationPath = path.join(__dirname, '../database/migrations/add_logo_column.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Executar a migração
    await query(migrationSQL);
    
    console.log('✅ Migração executada com sucesso!');
    
    // Verificar se a coluna foi criada
    const result = await query(`
      SELECT column_name, data_type, is_nullable, character_maximum_length
      FROM information_schema.columns 
      WHERE table_name = 'clientes' AND column_name = 'logo_path'
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ Coluna logo_path confirmada na tabela clientes:');
      console.log('   Tipo:', result.rows[0].data_type);
      console.log('   Tamanho máximo:', result.rows[0].character_maximum_length);
      console.log('   Permite NULL:', result.rows[0].is_nullable);
    } else {
      console.log('❌ Coluna logo_path não foi encontrada após migração');
    }
    
    // Listar todas as colunas da tabela clientes para verificação
    const allColumns = await query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'clientes' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📋 Estrutura atual da tabela clientes:');
    allColumns.rows.forEach(col => {
      console.log(`   ${col.column_name} (${col.data_type}) - NULL: ${col.is_nullable}`);
    });
    
  } catch (error) {
    console.error('❌ Erro durante a migração:', error);
    throw error;
  }
}

// Executar migração se chamado diretamente
if (require.main === module) {
  migrateLogo()
    .then(() => {
      console.log('🎉 Migração concluída com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha na migração:', error);
      process.exit(1);
    });
}

module.exports = { migrateLogo };
