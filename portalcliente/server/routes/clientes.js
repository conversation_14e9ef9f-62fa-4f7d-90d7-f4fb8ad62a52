const express = require('express');
const { query } = require('../database/connection');
const { logAction } = require('../utils/logger');
const { handleUpload, removeOldLogo, getLogoUrl } = require('../middleware/upload');

const router = express.Router();

// Middleware para verificar se é admin
const requireAdmin = (req, res, next) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ error: 'Acesso negado. Apenas administradores.' });
  }
  next();
};

// Listar clientes (apenas admin) - TODOS os clientes (ativos e inativos)
router.get('/', requireAdmin, async (req, res) => {
  try {
    console.log('📋 Listando TODOS os clientes (ativos e inativos)...');

    const result = await query(`
      SELECT
        id,
        codigo_cliente,
        cnpj,
        nome_fantasia,
        razao_social,
        cep,
        logradouro,
        numero,
        complemento,
        bairro,
        cidade,
        estado,
        ativo,
        created_at,
        updated_at
      FROM clientes
      ORDER BY ativo DESC, nome_fantasia ASC
    `);

    console.log(`📊 Total de clientes encontrados: ${result.rows.length}`);
    console.log(`✅ Ativos: ${result.rows.filter(c => c.ativo).length}`);
    console.log(`❌ Inativos: ${result.rows.filter(c => !c.ativo).length}`);

    // Adicionar URL da logo para cada cliente
    const clientesComLogo = result.rows.map(cliente => ({
      ...cliente,
      logo_url: getLogoUrl(cliente.codigo_cliente)
    }));

    res.json(clientesComLogo);
  } catch (error) {
    console.error('❌ Erro ao listar clientes:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar cliente por código
router.get('/:codigo', requireAdmin, async (req, res) => {
  try {
    const { codigo } = req.params;
    
    const result = await query(`
      SELECT * FROM clientes
      WHERE codigo_cliente = $1 AND ativo = true
    `, [codigo]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Cliente não encontrado' });
    }

    const cliente = result.rows[0];

    // Adicionar URL da logo se existir
    cliente.logo_url = getLogoUrl(cliente.codigo_cliente);

    res.json(cliente);
  } catch (error) {
    console.error('Erro ao buscar cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo cliente (apenas admin)
router.post('/', requireAdmin, handleUpload, async (req, res) => {
  try {
    const {
      codigo_cliente,
      cnpj,
      nome_fantasia,
      razao_social,
      cep,
      logradouro,
      numero,
      complemento,
      bairro,
      cidade,
      estado
    } = req.body;

    if (!codigo_cliente || !cnpj || !nome_fantasia || !razao_social) {
      return res.status(400).json({
        error: 'Código do cliente, CNPJ, nome fantasia e razão social são obrigatórios'
      });
    }

    // Verificar se o código já existe
    const existingClient = await query(
      'SELECT id FROM clientes WHERE codigo_cliente = $1',
      [codigo_cliente]
    );

    if (existingClient.rows.length > 0) {
      return res.status(400).json({ error: 'Código do cliente já existe' });
    }

    // Verificar se o CNPJ já existe
    const existingCnpj = await query(
      'SELECT id FROM clientes WHERE cnpj = $1',
      [cnpj]
    );

    if (existingCnpj.rows.length > 0) {
      return res.status(400).json({ error: 'CNPJ já cadastrado' });
    }

    // Processar upload de logo se fornecido
    let logoPath = null;
    if (req.file) {
      logoPath = `/api/uploads/logos/${req.file.filename}`;
      console.log('📷 Logo uploaded:', logoPath);
    }

    const result = await query(`
      INSERT INTO clientes (
        codigo_cliente, cnpj, nome_fantasia, razao_social,
        cep, logradouro, numero, complemento, bairro, cidade, estado, logo_path
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [
      codigo_cliente, cnpj, nome_fantasia, razao_social,
      cep, logradouro, numero, complemento, bairro, cidade, estado, logoPath
    ]);

    const novoCliente = result.rows[0];

    // Adicionar URL da logo no retorno
    novoCliente.logo_url = getLogoUrl(novoCliente.codigo_cliente);

    // Registrar log
    await logAction(
      req.user.id,
      'CREATE',
      'clientes',
      novoCliente.id,
      null,
      novoCliente,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Cliente cadastrado com sucesso',
      cliente: novoCliente
    });

  } catch (error) {
    console.error('Erro ao criar cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar cliente (apenas admin)
router.put('/:codigo', requireAdmin, handleUpload, async (req, res) => {
  try {
    const { codigo } = req.params;
    const {
      cnpj,
      nome_fantasia,
      razao_social,
      cep,
      logradouro,
      numero,
      complemento,
      bairro,
      cidade,
      estado,
      ativo
    } = req.body;

    // Buscar dados anteriores
    const dadosAnteriores = await query(
      'SELECT * FROM clientes WHERE codigo_cliente = $1',
      [codigo]
    );

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Cliente não encontrado' });
    }

    // Verificar se o CNPJ já existe em outro cliente
    if (cnpj && cnpj !== dadosAnteriores.rows[0].cnpj) {
      const existingCnpj = await query(
        'SELECT id FROM clientes WHERE cnpj = $1 AND codigo_cliente != $2',
        [cnpj, codigo]
      );

      if (existingCnpj.rows.length > 0) {
        return res.status(400).json({ error: 'CNPJ já cadastrado para outro cliente' });
      }
    }

    // Processar upload de logo se fornecido
    let logoPath = dadosAnteriores.rows[0].logo_path; // Manter logo atual por padrão
    let logoAntiga = logoPath; // Guardar referência da logo antiga

    if (req.file) {
      // Definir novo caminho da logo PRIMEIRO
      logoPath = `/api/uploads/logos/${req.file.filename}`;
      console.log('📷 Nova logo uploaded:', logoPath);

      // Remover logo antiga DEPOIS de confirmar o novo upload
      if (logoAntiga && logoAntiga !== logoPath) {
        removeOldLogo(codigo);
        console.log('🗑️ Logo antiga removida após upload da nova');
      }
    }

    const result = await query(`
      UPDATE clientes
      SET
        cnpj = $1,
        nome_fantasia = $2,
        razao_social = $3,
        cep = $4,
        logradouro = $5,
        numero = $6,
        complemento = $7,
        bairro = $8,
        cidade = $9,
        estado = $10,
        ativo = $11,
        logo_path = $12,
        updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $13
      RETURNING *
    `, [
      cnpj, nome_fantasia, razao_social, cep, logradouro,
      numero, complemento, bairro, cidade, estado, ativo, logoPath, codigo
    ]);

    const clienteAtualizado = result.rows[0];

    // Adicionar URL da logo no retorno
    clienteAtualizado.logo_url = getLogoUrl(clienteAtualizado.codigo_cliente);

    // Registrar log
    await logAction(
      req.user.id,
      'UPDATE',
      'clientes',
      clienteAtualizado.id,
      dadosAnteriores.rows[0],
      clienteAtualizado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Cliente atualizado com sucesso',
      cliente: clienteAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Ativar/Inativar cliente (apenas admin)
router.patch('/:codigo/toggle-status', requireAdmin, async (req, res) => {
  try {
    const { codigo } = req.params;

    console.log(`🔄 Alterando status do cliente: ${codigo}`);

    // Buscar dados atuais do cliente
    const clienteAtual = await query(
      'SELECT * FROM clientes WHERE codigo_cliente = $1',
      [codigo]
    );

    if (clienteAtual.rows.length === 0) {
      return res.status(404).json({ error: 'Cliente não encontrado' });
    }

    const cliente = clienteAtual.rows[0];
    const novoStatus = !cliente.ativo;

    console.log(`📊 Cliente ${codigo}: ${cliente.ativo ? 'ATIVO' : 'INATIVO'} → ${novoStatus ? 'ATIVO' : 'INATIVO'}`);

    // Verificar dados relacionados para relatório de integridade
    const dadosRelacionados = await verificarDadosRelacionadosCliente(codigo);

    console.log('📋 Verificação de integridade dos dados relacionados:');
    console.log(`   - Produtos: ${dadosRelacionados.produtos}`);
    console.log(`   - Blocos: ${dadosRelacionados.blocos}`);
    console.log(`   - Gavetas: ${dadosRelacionados.gavetas}`);
    console.log(`   - Sepultamentos: ${dadosRelacionados.sepultamentos}`);

    // IMPORTANTE: Não alteramos nenhum dado relacionado, apenas o status do cliente
    console.log('⚠️ GARANTIA DE INTEGRIDADE: Todos os dados relacionados serão mantidos intactos');

    // Atualizar status do cliente
    const result = await query(`
      UPDATE clientes
      SET ativo = $1, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $2
      RETURNING *
    `, [novoStatus, codigo]);

    const clienteAtualizado = result.rows[0];

    // Log da ação com detalhes de integridade
    await logAction(
      req.user.id,
      'UPDATE',
      'clientes',
      clienteAtualizado.id,
      {
        ativo: cliente.ativo,
        dados_relacionados: dadosRelacionados
      },
      {
        ativo: novoStatus,
        dados_relacionados_mantidos: dadosRelacionados
      },
      req.ip,
      req.get('User-Agent'),
      { codigo_cliente: codigo },
      `Cliente ${cliente.nome_fantasia} ${novoStatus ? 'ativado' : 'inativado'}. INTEGRIDADE GARANTIDA: ${dadosRelacionados.produtos} produtos, ${dadosRelacionados.blocos} blocos, ${dadosRelacionados.gavetas} gavetas, ${dadosRelacionados.sepultamentos} sepultamentos mantidos intactos.`
    );

    console.log(`✅ Status do cliente ${codigo} alterado com sucesso para: ${novoStatus ? 'ATIVO' : 'INATIVO'}`);

    res.json({
      message: `Cliente ${novoStatus ? 'ativado' : 'inativado'} com sucesso`,
      cliente: clienteAtualizado
    });

  } catch (error) {
    console.error('❌ Erro ao alterar status do cliente:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função auxiliar para verificar dados relacionados do cliente
async function verificarDadosRelacionadosCliente(codigoCliente) {
  try {
    // Contar produtos
    const produtosResult = await query(`
      SELECT COUNT(*) as total
      FROM produtos
      WHERE codigo_cliente = $1
    `, [codigoCliente]);

    // Contar blocos
    const blocosResult = await query(`
      SELECT COUNT(*) as total
      FROM blocos
      WHERE codigo_cliente = $1
    `, [codigoCliente]);

    // Contar gavetas
    const gavetasResult = await query(`
      SELECT COUNT(*) as total
      FROM gavetas
      WHERE codigo_cliente = $1
    `, [codigoCliente]);

    // Contar sepultamentos
    const sepultamentosResult = await query(`
      SELECT COUNT(*) as total
      FROM sepultamentos
      WHERE codigo_cliente = $1
    `, [codigoCliente]);

    return {
      produtos: parseInt(produtosResult.rows[0].total) || 0,
      blocos: parseInt(blocosResult.rows[0].total) || 0,
      gavetas: parseInt(gavetasResult.rows[0].total) || 0,
      sepultamentos: parseInt(sepultamentosResult.rows[0].total) || 0
    };
  } catch (error) {
    console.error('❌ Erro ao verificar dados relacionados do cliente:', error);
    return {
      produtos: 0,
      blocos: 0,
      gavetas: 0,
      sepultamentos: 0
    };
  }
}

// Remover logo do cliente (apenas admin)
router.delete('/:codigo/logo', requireAdmin, async (req, res) => {
  try {
    const { codigo } = req.params;

    // Verificar se cliente existe
    const clienteResult = await query(
      'SELECT * FROM clientes WHERE codigo_cliente = $1',
      [codigo]
    );

    if (clienteResult.rows.length === 0) {
      return res.status(404).json({ error: 'Cliente não encontrado' });
    }

    const cliente = clienteResult.rows[0];

    // Remover arquivo físico da logo
    removeOldLogo(codigo);

    // Atualizar banco de dados removendo logo_path
    await query(`
      UPDATE clientes
      SET logo_path = NULL, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $1
    `, [codigo]);

    // Registrar log
    await logAction(
      req.user.id,
      'UPDATE',
      'clientes',
      cliente.id,
      { logo_path: cliente.logo_path },
      { logo_path: null },
      req.ip,
      req.get('User-Agent')
    );

    console.log(`🗑️ Logo removida do cliente: ${codigo}`);

    res.json({
      message: 'Logo removida com sucesso',
      codigo_cliente: codigo
    });

  } catch (error) {
    console.error('❌ Erro ao remover logo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
