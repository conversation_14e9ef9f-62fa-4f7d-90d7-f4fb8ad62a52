/**
 * ROTA: RELATÓRIO MENSAL
 * Endpoint para geração de relatórios mensais baseados na tabela daily_report
 * Acesso: Apenas administradores
 * Autor: Sistema Multi-Agente
 * Data: 2025-01-21
 */

const express = require('express');
const { query } = require('../database/connection');
const { buscarDadosRelatorioCompleto } = require('../services/relatorioMensalService');

const router = express.Router();





/**
 * GET /api/relatorio-mensal/anos
 * Busca anos disponíveis na tabela daily_report
 */
router.get('/anos', async (req, res) => {
  try {
    const userType = req.user.tipo_usuario;
    
    // Verificar se é administrador
    if (userType !== 'admin') {
      return res.status(403).json({
        error: 'Acesso negado. Apenas administradores podem acessar relatórios mensais.'
      });
    }

    console.log('📊 Buscando anos disponíveis...');

    const anosQuery = `
      SELECT DISTINCT
        EXTRACT(YEAR FROM data_leitura_formatada) as ano
      FROM daily_report
      WHERE data_leitura_formatada IS NOT NULL
      ORDER BY ano DESC
    `;

    const result = await query(anosQuery);

    res.json({
      success: true,
      anos: result.rows.map(row => parseInt(row.ano))
    });

  } catch (error) {
    console.error('❌ Erro ao buscar anos:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

/**
 * POST /api/relatorio-mensal/gerar
 * Gera relatório mensal com base nos filtros fornecidos
 */
router.post('/gerar', async (req, res) => {
  try {
    const userType = req.user.tipo_usuario;
    const { produto_id, mes, ano, custo_kwh } = req.body;

    // Verificar se é administrador
    if (userType !== 'admin') {
      return res.status(403).json({
        error: 'Acesso negado. Apenas administradores podem acessar relatórios mensais.'
      });
    }

    // Validar parâmetros obrigatórios
    if (!produto_id || !mes || !ano || !custo_kwh) {
      return res.status(400).json({
        error: 'Parâmetros obrigatórios: produto_id, mes, ano, custo_kwh'
      });
    }

    // Buscar dados do produto para obter codigo_cliente e codigo_estacao
    const produtoQuery = `
      SELECT codigo_cliente, codigo_estacao, denominacao
      FROM produtos
      WHERE id = $1 AND ativo = true
    `;

    const produtoResult = await query(produtoQuery, [produto_id]);

    if (produtoResult.rows.length === 0) {
      return res.status(404).json({
        error: 'Produto não encontrado ou inativo'
      });
    }

    const { codigo_cliente, codigo_estacao } = produtoResult.rows[0];
    console.log(`📊 Produto encontrado: ${produtoResult.rows[0].denominacao} (${codigo_cliente}/${codigo_estacao})`);


    // Validar tipos e valores
    const mesNum = parseInt(mes);
    const anoNum = parseInt(ano);
    const custoNum = parseFloat(custo_kwh);

    if (isNaN(mesNum) || mesNum < 1 || mesNum > 12) {
      return res.status(400).json({
        error: 'Mês deve ser um número entre 1 e 12'
      });
    }

    if (isNaN(anoNum) || anoNum < 2020 || anoNum > new Date().getFullYear()) {
      return res.status(400).json({
        error: 'Ano deve ser um número válido entre 2020 e o ano atual'
      });
    }

    if (isNaN(custoNum) || custoNum <= 0) {
      return res.status(400).json({
        error: 'Custo do kWh deve ser um número maior que zero'
      });
    }

    console.log('📊 Gerando relatório mensal:', {
      codigo_cliente,
      codigo_estacao,
      mes,
      ano,
      custo_kwh
    });

    // Usar serviço para buscar dados completos
    const relatorio = await buscarDadosRelatorioCompleto(
      codigo_cliente,
      codigo_estacao,
      mesNum,
      anoNum,
      custoNum
    );

    if (!relatorio.cliente) {
      return res.status(404).json({
        error: 'Cliente não encontrado'
      });
    }

    if (!relatorio.produto) {
      return res.status(404).json({
        error: 'Produto/Estação não encontrado'
      });
    }

    // Verificar se há dados operacionais
    if (!relatorio.dadosOperacionais || relatorio.dadosOperacionais.length === 0) {
      return res.status(404).json({
        error: 'Nenhum dado operacional encontrado para o período selecionado'
      });
    }

    res.json({
      success: true,
      relatorio,
      message: `Relatório gerado com sucesso. ${relatorio.dadosOperacionais.length} registros encontrados.`
    });

  } catch (error) {
    console.error('❌ Erro ao gerar relatório mensal:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      details: error.message
    });
  }
});

module.exports = router;
