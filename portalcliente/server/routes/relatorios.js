const express = require('express');
const { query } = require('../database/connection');

const router = express.Router();

// Rota para buscar dados de relatórios de sepultamentos
router.get('/sepultamentos', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { produto_id, data_inicio, data_fim } = req.query;

    console.log('📊 Buscando dados de relatório:', {
      userId,
      userType,
      produto_id,
      data_inicio,
      data_fim
    });

    // Validar formato das datas
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data_inicio) || !dateRegex.test(data_fim)) {
      return res.status(400).json({
        error: 'Formato de data inválido. Use YYYY-MM-DD'
      });
    }

    if (!produto_id || !data_inicio || !data_fim) {
      return res.status(400).json({
        error: 'Parâmetros obrigatórios: produto_id, data_inicio, data_fim'
      });
    }

    // CORREÇÃO: Aceitar tanto ID quanto codigo_estacao para compatibilidade
    let produtoQuery, produtoParams;

    // Verificar se produto_id é numérico (ID) ou string (codigo_estacao)
    const isNumericId = !isNaN(produto_id) && !isNaN(parseFloat(produto_id));

    console.log('🔍 DEBUG - Tipo de produto_id:', {
      produto_id,
      isNumericId,
      type: typeof produto_id
    });

    if (userType === 'cliente') {
      if (isNumericId) {
        // Buscar por ID numérico
        produtoQuery = `
          SELECT * FROM produtos
          WHERE id = $1
          AND codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $2)
        `;
        produtoParams = [parseInt(produto_id), userId];
      } else {
        // Buscar por codigo_estacao (compatibilidade)
        produtoQuery = `
          SELECT * FROM produtos
          WHERE codigo_estacao = $1
          AND codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $2)
        `;
        produtoParams = [produto_id, userId];
      }
    } else {
      if (isNumericId) {
        // Buscar por ID numérico
        produtoQuery = `
          SELECT * FROM produtos
          WHERE id = $1
        `;
        produtoParams = [parseInt(produto_id)];
      } else {
        // Buscar por codigo_estacao (compatibilidade)
        produtoQuery = `
          SELECT * FROM produtos
          WHERE codigo_estacao = $1
        `;
        produtoParams = [produto_id];
      }
    }

    console.log('🔍 DEBUG - Buscando produto:', {
      produto_id: produto_id,
      userType: userType,
      query: produtoQuery,
      params: produtoParams
    });

    let produtoResult;
    try {
      produtoResult = await query(produtoQuery, produtoParams);
    } catch (error) {
      console.log('❌ ERRO - Falha na busca do produto:', {
        produto_id,
        error: error.message,
        code: error.code
      });
      return res.status(400).json({
        error: 'Parâmetro produto_id inválido',
        details: error.message
      });
    }

    console.log('🔍 DEBUG - Resultado busca produto:', {
      totalEncontrados: produtoResult.rows.length,
      produtos: produtoResult.rows.map(p => ({
        id: p.id,
        codigo_cliente: p.codigo_cliente,
        codigo_estacao: p.codigo_estacao,
        denominacao: p.denominacao
      }))
    });

    if (produtoResult.rows.length === 0) {
      console.log('❌ ERRO - Produto não encontrado:', {
        produto_id,
        isNumericId,
        userType
      });
      return res.status(404).json({
        error: 'Produto não encontrado',
        produto_id: produto_id,
        tipo_busca: isNumericId ? 'ID numérico' : 'Código da estação'
      });
    }

    // Usar o produto encontrado diretamente
    const produto = produtoResult.rows[0];
    console.log('🎯 Produto encontrado:', {
      id: produto.id,
      codigo_cliente: produto.codigo_cliente,
      codigo_estacao: produto.codigo_estacao,
      denominacao: produto.denominacao
    });

    let whereClause = '';
    let params = [produto.codigo_estacao, data_inicio, data_fim];

    // Filtrar por cliente específico do produto
    if (userType === 'cliente') {
      whereClause = 'AND s.codigo_cliente = $4';
      params.push(produto.codigo_cliente);
    } else {
      // Para admin, filtrar pelo cliente do produto selecionado
      whereClause = 'AND s.codigo_cliente = $4';
      params.push(produto.codigo_cliente);
    }

    // CORREÇÃO: O frontend já envia data_fim com +1 dia, não aplicar offset adicional
    // Converter data_fim de volta para o dia correto (remover o +1 dia do frontend)
    const dataFimCorrigida = new Date(data_fim);
    dataFimCorrigida.setDate(dataFimCorrigida.getDate() - 1);
    const dataFimCorrigidaStr = dataFimCorrigida.toISOString().split('T')[0];

    console.log('🔧 CORREÇÃO DE DATAS:', {
      data_inicio_recebida: data_inicio,
      data_fim_recebida: data_fim,
      data_fim_corrigida: dataFimCorrigidaStr,
      observacao: 'Frontend envia data_fim+1, corrigindo para período real'
    });

    // Buscar TODOS os sepultamentos do período (independente do status ativo/inativo)
    const sepultamentosQuery = `
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        p.nome as produto_nome,
        b.denominacao as bloco_denominacao,
        CASE
          WHEN s.status_exumacao = true THEN 'Exumado'
          ELSE 'Sepultado'
        END as situacao
      FROM sepultamentos s
      JOIN produtos p ON p.codigo_estacao = s.codigo_estacao AND p.codigo_cliente = s.codigo_cliente
      LEFT JOIN blocos b ON b.codigo_bloco = s.codigo_bloco AND b.codigo_estacao = s.codigo_estacao AND b.codigo_cliente = s.codigo_cliente
      WHERE s.codigo_estacao = $1
        AND s.data_sepultamento >= $2
        AND s.data_sepultamento <= $3
        ${whereClause}
      ORDER BY s.data_sepultamento DESC
    `;

    // Atualizar parâmetros para usar data_fim corrigida
    const paramsCorrigidos = [produto.codigo_estacao, data_inicio, dataFimCorrigidaStr];
    if (whereClause) {
      paramsCorrigidos.push(produto.codigo_cliente);
    }

    console.log('🔍 DEBUG - Query de sepultamentos:', {
      query: sepultamentosQuery,
      params: paramsCorrigidos,
      whereClause: whereClause,
      produto: { codigo_cliente: produto.codigo_cliente, codigo_estacao: produto.codigo_estacao }
    });

    const sepultamentosResult = await query(sepultamentosQuery, paramsCorrigidos);

    console.log('📊 DEBUG - Resultado sepultamentos:', {
      totalEncontrados: sepultamentosResult.rows.length,
      primeiros3: sepultamentosResult.rows.slice(0, 3).map(s => ({
        id: s.id,
        nome: s.nome_sepultado,
        data: s.data_sepultamento,
        codigo_cliente: s.codigo_cliente,
        codigo_estacao: s.codigo_estacao
      }))
    });

    // Buscar gavetas do produto específico
    const gavetasQuery = `
      SELECT COUNT(*) as total
      FROM gavetas g
      WHERE g.codigo_estacao = $1 AND g.codigo_cliente = $2
    `;

    const gavetasResult = await query(gavetasQuery, [produto.codigo_estacao, produto.codigo_cliente]);

    // Buscar exumações do período para o resumo por dia - CORRIGIDO
    const exumacoesQuery = `
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        p.nome as produto_nome,
        b.denominacao as bloco_denominacao
      FROM sepultamentos s
      JOIN produtos p ON p.codigo_estacao = s.codigo_estacao AND p.codigo_cliente = s.codigo_cliente
      LEFT JOIN blocos b ON b.codigo_bloco = s.codigo_bloco AND b.codigo_estacao = s.codigo_estacao AND b.codigo_cliente = s.codigo_cliente
      WHERE s.codigo_estacao = $1
        AND s.exumado_em >= $2
        AND s.exumado_em <= $3
        AND s.status_exumacao = true
        ${whereClause}
      ORDER BY s.exumado_em DESC
    `;

    const exumacoesResult = await query(exumacoesQuery, paramsCorrigidos);

    // Contar total de exumações no período - CORRIGIDO
    const totalExumacoesQuery = `
      SELECT COUNT(*) as total
      FROM sepultamentos s
      JOIN produtos p ON p.codigo_estacao = s.codigo_estacao AND p.codigo_cliente = s.codigo_cliente
      WHERE s.codigo_estacao = $1
        AND s.exumado_em >= $2
        AND s.exumado_em <= $3
        AND s.status_exumacao = true
        ${whereClause}
    `;

    const totalExumacoesResult = await query(totalExumacoesQuery, params);

    res.json({
      sepultamentos: sepultamentosResult.rows,
      exumacoes: exumacoesResult.rows,
      produto: produto,
      totalGavetas: parseInt(gavetasResult.rows[0]?.total || 0),
      totalExumacoesPeriodo: parseInt(totalExumacoesResult.rows[0]?.total || 0),
      periodo: {
        inicio: data_inicio,
        fim: data_fim
      }
    });

  } catch (error) {
    console.error('❌ Erro ao buscar dados de relatório:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao buscar dados de relatório'
    });
  }
});

module.exports = router;
