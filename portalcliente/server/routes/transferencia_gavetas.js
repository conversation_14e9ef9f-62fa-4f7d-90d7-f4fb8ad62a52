const express = require('express');
const { query } = require('../database/connection');
const fs = require('fs').promises;
const path = require('path');

const router = express.Router();

// Endpoint para executar transferência de gavetas específica - ITV_001
router.post('/executar-transferencia-itv001', async (req, res) => {
  // Apenas administradores podem executar transferências
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({
      error: 'Acesso negado. Apenas administradores podem executar transferências de gavetas.'
    });
  }

  try {
    console.log('🔄 Iniciando transferência de gavetas ITV_001...');

    // Parâmetros da transferência - DADOS CORRETOS DO ARQUIVO
    const transferParams = {
      codigo_cliente: 'ITV_001',
      codigo_estacao: 'ETEN_002',
      codigo_bloco: 'BL_001',
      sub_origem: 'SUB_002',
      sub_destino: 'SUB_003',
      gavetas: [
        1279, 1280, 1281, 1282, 1283, 1284, 1285,
        1303, 1304, 1305, 1306, 1307, 1308, 1309,
        1327, 1328, 1329, 1330, 1331, 1332,
        1350, 1351, 1352, 1353, 1354, 1355, 1356,
        1374, 1375, 1376, 1377, 1378, 1379, 1380,
        1398, 1399, 1400, 1401, 1402, 1403,
        1421, 1422, 1423, 1424, 1425, 1426, 1427
      ],
      range_remover: { inicio: 1279, fim: 1444 }
    };

    // ETAPA 1: Verificações de segurança
    console.log('🔍 Verificando sub-blocos...');
    
    const subBlocoOrigem = await query(`
      SELECT * FROM sub_blocos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, transferParams.sub_origem]);

    const subBlocoDestino = await query(`
      SELECT * FROM sub_blocos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, transferParams.sub_destino]);

    if (subBlocoOrigem.rows.length === 0) {
      return res.status(400).json({ error: 'Sub-bloco origem SUB_002 não encontrado!' });
    }

    if (subBlocoDestino.rows.length === 0) {
      return res.status(400).json({ error: 'Sub-bloco destino SUB_003 não encontrado!' });
    }

    // ETAPA 2: Verificar sepultamentos existentes
    console.log('🔍 Verificando sepultamentos nas gavetas...');
    
    const sepultamentosExistentes = await query(`
      SELECT numero_gaveta, nome_sepultado, data_sepultamento, data_exumacao,
             CASE WHEN data_exumacao IS NULL THEN 'ATIVO' ELSE 'EXUMADO' END as status
      FROM sepultamentos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
      AND numero_gaveta = ANY($5) AND ativo = true
      ORDER BY numero_gaveta
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
        transferParams.sub_origem, transferParams.gavetas]);

    console.log(`📊 Encontrados ${sepultamentosExistentes.rows.length} sepultamentos nas gavetas`);

    // ETAPA 3: Executar transferência em transação
    console.log('🔄 Executando transferência...');

    await query('BEGIN');

    try {
      // 3.1: Transferir sepultamentos
      const resultSepultamentos = await query(`
        UPDATE sepultamentos
        SET codigo_sub_bloco = $1, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4
        AND codigo_sub_bloco = $5 AND numero_gaveta = ANY($6)
      `, [transferParams.sub_destino, transferParams.codigo_cliente, transferParams.codigo_estacao,
          transferParams.codigo_bloco, transferParams.sub_origem, transferParams.gavetas]);

      console.log(`✅ ${resultSepultamentos.rowCount} sepultamentos transferidos`);

      // 3.2: Transferir gavetas
      const resultGavetas = await query(`
        UPDATE gavetas
        SET codigo_sub_bloco = $1, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4
        AND codigo_sub_bloco = $5 AND numero_gaveta = ANY($6)
      `, [transferParams.sub_destino, transferParams.codigo_cliente, transferParams.codigo_estacao,
          transferParams.codigo_bloco, transferParams.sub_origem, transferParams.gavetas]);

      console.log(`✅ ${resultGavetas.rowCount} gavetas transferidas`);

      // 3.3: Remover range 1279-1444 do SUB_002
      const resultRange = await query(`
        DELETE FROM numeracoes_gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $4 AND numero_inicio = $5 AND numero_fim = $6
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco,
          transferParams.sub_origem, transferParams.range_remover.inicio, transferParams.range_remover.fim]);

      console.log(`✅ ${resultRange.rowCount} ranges removidos do SUB_002`);

      // 3.4: Verificar se precisa criar range no SUB_003
      const rangeExistente = await query(`
        SELECT * FROM numeracoes_gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $4 AND numero_inicio <= $5 AND numero_fim >= $6
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
          transferParams.sub_destino, Math.min(...transferParams.gavetas), Math.max(...transferParams.gavetas)]);

      if (rangeExistente.rows.length === 0) {
        // Criar novo range no SUB_003
        await query(`
          INSERT INTO numeracoes_gavetas (
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio, numero_fim, ativo, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
            transferParams.sub_destino, Math.min(...transferParams.gavetas), Math.max(...transferParams.gavetas)]);
        
        console.log(`✅ Novo range criado no SUB_003: ${Math.min(...transferParams.gavetas)} - ${Math.max(...transferParams.gavetas)}`);
      } else {
        console.log('✅ Range já existe no SUB_003 que cobre as gavetas transferidas');
      }

      await query('COMMIT');
      console.log('✅ Transferência concluída com sucesso!');

      // ETAPA 4: Verificações finais
      const verificacaoFinal = await query(`
        SELECT 
          'Gavetas no SUB_003' as tipo,
          COUNT(*) as quantidade
        FROM gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5)
        
        UNION ALL
        
        SELECT 
          'Gavetas restantes no SUB_002' as tipo,
          COUNT(*) as quantidade
        FROM gavetas 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $6 AND numero_gaveta = ANY($5)
        
        UNION ALL
        
        SELECT 
          'Sepultamentos no SUB_003' as tipo,
          COUNT(*) as quantidade
        FROM sepultamentos 
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
        AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5) AND ativo = true
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, 
          transferParams.sub_destino, transferParams.gavetas, transferParams.sub_origem]);

      res.json({
        success: true,
        message: 'Transferência de gavetas executada com sucesso!',
        detalhes: {
          sepultamentos_transferidos: resultSepultamentos.rowCount,
          gavetas_transferidas: resultGavetas.rowCount,
          ranges_removidos: resultRange.rowCount,
          sepultamentos_existentes: sepultamentosExistentes.rows,
          verificacao_final: verificacaoFinal.rows
        }
      });

    } catch (error) {
      await query('ROLLBACK');
      console.error('❌ Erro na transferência - Rollback executado:', error);
      console.log('🔄 Dados restaurados ao estado anterior');

      // Log detalhado do erro
      console.error('Detalhes do erro:', {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });

      throw new Error(`Falha na transferência de gavetas: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Erro na transferência de gavetas:', error);
    res.status(500).json({ 
      error: 'Erro ao executar transferência de gavetas',
      details: error.message 
    });
  }
});

// Endpoint para verificar status antes da transferência - ITV_001
router.get('/verificar-status-itv001', async (req, res) => {
  try {
    const params = {
      codigo_cliente: 'ITV_001',
      codigo_estacao: 'ETEN_002',
      codigo_bloco: 'BL_001',
      sub_origem: 'SUB_002',
      sub_destino: 'SUB_003',
      gavetas: [1279, 1280, 1281, 1282, 1283, 1284, 1285, 1303, 1304, 1305, 1306, 1307, 1308, 1309, 1327, 1328, 1329, 1330, 1331, 1332, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1374, 1375, 1376, 1377, 1378, 1379, 1380, 1398, 1399, 1400, 1401, 1402, 1403, 1421, 1422, 1423, 1424, 1425, 1426, 1427]
    };

    // Verificar sub-blocos
    const subBlocos = await query(`
      SELECT codigo_sub_bloco, denominacao, ativo 
      FROM sub_blocos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
      AND codigo_sub_bloco IN ($4, $5)
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.sub_destino]);

    // Verificar gavetas existentes
    const gavetasOrigem = await query(`
      SELECT numero_gaveta, disponivel, ativo 
      FROM gavetas 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
      AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5)
      ORDER BY numero_gaveta
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.gavetas]);

    // Verificar sepultamentos
    const sepultamentos = await query(`
      SELECT numero_gaveta, nome_sepultado, data_sepultamento, data_exumacao,
             CASE WHEN data_exumacao IS NULL THEN 'ATIVO' ELSE 'EXUMADO' END as status
      FROM sepultamentos 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
      AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5) AND ativo = true
      ORDER BY numero_gaveta
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.gavetas]);

    // Verificar ranges
    const ranges = await query(`
      SELECT codigo_sub_bloco, numero_inicio, numero_fim, ativo 
      FROM numeracoes_gavetas 
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 
      AND codigo_sub_bloco IN ($4, $5)
      ORDER BY codigo_sub_bloco, numero_inicio
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.sub_destino]);

    res.json({
      sub_blocos: subBlocos.rows,
      gavetas_origem: gavetasOrigem.rows,
      sepultamentos: sepultamentos.rows,
      ranges: ranges.rows,
      total_gavetas_transferir: params.gavetas.length,
      pronto_para_transferencia: subBlocos.rows.length === 2 && gavetasOrigem.rows.length > 0
    });

  } catch (error) {
    console.error('❌ Erro ao verificar status:', error);
    res.status(500).json({ 
      error: 'Erro ao verificar status da transferência',
      details: error.message 
    });
  }
});

// ===================================================================
// ENDPOINTS LEGADOS PARA TV_001 (MANTIDOS PARA COMPATIBILIDADE)
// ===================================================================

// Endpoint para executar transferência de gavetas específica - TV_001 (LEGADO)
router.post('/executar-transferencia-tv001', async (req, res) => {
  // Apenas administradores podem executar transferências
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({
      error: 'Acesso negado. Apenas administradores podem executar transferências de gavetas.'
    });
  }

  try {
    console.log('🔄 Iniciando transferência de gavetas TV_001 (LEGADO)...');

    // Parâmetros da transferência - TV_001
    const transferParams = {
      codigo_cliente: 'TV_001',
      codigo_estacao: 'ETEN_002',
      codigo_bloco: 'BL_001',
      sub_origem: 'SUB_002',
      sub_destino: 'SUB_003',
      gavetas: [
        1279, 1280, 1281, 1282, 1283, 1284, 1285,
        1303, 1304, 1305, 1306, 1307, 1308, 1309,
        1327, 1328, 1329, 1330, 1331, 1332,
        1350, 1351, 1352, 1353, 1354, 1355, 1356,
        1374, 1375, 1376, 1377, 1378, 1379, 1380,
        1398, 1399, 1400, 1401, 1402, 1403,
        1421, 1422, 1423, 1424, 1425, 1426, 1427
      ],
      range_remover: { inicio: 1279, fim: 1444 }
    };

    // ETAPA 1: Verificações de segurança
    console.log('🔍 Verificando sub-blocos...');

    const subBlocoOrigem = await query(`
      SELECT * FROM sub_blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, transferParams.sub_origem]);

    const subBlocoDestino = await query(`
      SELECT * FROM sub_blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco, transferParams.sub_destino]);

    if (subBlocoOrigem.rows.length === 0) {
      return res.status(400).json({ error: `Sub-bloco origem ${transferParams.sub_origem} não encontrado` });
    }

    if (subBlocoDestino.rows.length === 0) {
      return res.status(400).json({ error: `Sub-bloco destino ${transferParams.sub_destino} não encontrado` });
    }

    console.log('✅ Sub-blocos verificados');

    // ETAPA 2: Verificar sepultamentos existentes
    console.log('🔍 Verificando sepultamentos nas gavetas...');

    const sepultamentosExistentes = await query(`
      SELECT numero_gaveta, nome_sepultado, data_sepultamento, data_exumacao,
             CASE WHEN data_exumacao IS NULL THEN 'ATIVO' ELSE 'EXUMADO' END as status
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4
      AND numero_gaveta = ANY($5) AND ativo = true
      ORDER BY numero_gaveta
    `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco,
        transferParams.sub_origem, transferParams.gavetas]);

    console.log(`📊 Encontrados ${sepultamentosExistentes.rows.length} sepultamentos nas gavetas`);

    // ETAPA 3: Executar transferência em transação
    console.log('🔄 Executando transferência...');

    await query('BEGIN');

    try {
      // 3.1: Transferir sepultamentos
      const resultSepultamentos = await query(`
        UPDATE sepultamentos
        SET codigo_sub_bloco = $1, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4
        AND codigo_sub_bloco = $5 AND numero_gaveta = ANY($6)
      `, [transferParams.sub_destino, transferParams.codigo_cliente, transferParams.codigo_estacao,
          transferParams.codigo_bloco, transferParams.sub_origem, transferParams.gavetas]);

      console.log(`✅ ${resultSepultamentos.rowCount} sepultamentos transferidos`);

      // 3.2: Transferir gavetas
      const resultGavetas = await query(`
        UPDATE gavetas
        SET codigo_sub_bloco = $1, updated_at = CURRENT_TIMESTAMP
        WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4
        AND codigo_sub_bloco = $5 AND numero_gaveta = ANY($6)
      `, [transferParams.sub_destino, transferParams.codigo_cliente, transferParams.codigo_estacao,
          transferParams.codigo_bloco, transferParams.sub_origem, transferParams.gavetas]);

      console.log(`✅ ${resultGavetas.rowCount} gavetas transferidas`);

      // 3.3: Remover range 1279-1444 do SUB_002
      const resultRange = await query(`
        DELETE FROM numeracoes_gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $4 AND numero_inicio = $5 AND numero_fim = $6
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco,
          transferParams.sub_origem, transferParams.range_remover.inicio, transferParams.range_remover.fim]);

      console.log(`✅ ${resultRange.rowCount} ranges removidos do SUB_002`);

      // 3.4: Verificar se precisa criar range no SUB_003
      const rangeExistente = await query(`
        SELECT * FROM numeracoes_gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $4 AND numero_inicio <= $5 AND numero_fim >= $6
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco,
          transferParams.sub_destino, Math.min(...transferParams.gavetas), Math.max(...transferParams.gavetas)]);

      if (rangeExistente.rows.length === 0) {
        // Criar novo range no SUB_003
        await query(`
          INSERT INTO numeracoes_gavetas (
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio, numero_fim, ativo, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco,
            transferParams.sub_destino, Math.min(...transferParams.gavetas), Math.max(...transferParams.gavetas)]);

        console.log(`✅ Novo range criado no SUB_003: ${Math.min(...transferParams.gavetas)} - ${Math.max(...transferParams.gavetas)}`);
      } else {
        console.log('✅ Range já existe no SUB_003 que cobre as gavetas transferidas');
      }

      await query('COMMIT');
      console.log('✅ Transferência concluída com sucesso!');

      // ETAPA 4: Verificações finais
      const verificacaoFinal = await query(`
        SELECT
          'Gavetas no SUB_003' as tipo,
          COUNT(*) as quantidade
        FROM gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5)

        UNION ALL

        SELECT
          'Gavetas restantes no SUB_002' as tipo,
          COUNT(*) as quantidade
        FROM gavetas
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $6 AND numero_gaveta = ANY($5)

        UNION ALL

        SELECT
          'Sepultamentos no SUB_003' as tipo,
          COUNT(*) as quantidade
        FROM sepultamentos
        WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5) AND ativo = true
      `, [transferParams.codigo_cliente, transferParams.codigo_estacao, transferParams.codigo_bloco,
          transferParams.sub_destino, transferParams.gavetas, transferParams.sub_origem]);

      res.json({
        success: true,
        message: 'Transferência de gavetas TV_001 executada com sucesso!',
        detalhes: {
          sepultamentos_transferidos: resultSepultamentos.rowCount,
          gavetas_transferidas: resultGavetas.rowCount,
          ranges_removidos: resultRange.rowCount,
          sepultamentos_existentes: sepultamentosExistentes.rows,
          verificacao_final: verificacaoFinal.rows
        }
      });

    } catch (error) {
      await query('ROLLBACK');
      console.error('❌ Erro na transferência - Rollback executado:', error);
      console.log('🔄 Dados restaurados ao estado anterior');

      // Log detalhado do erro
      console.error('Detalhes do erro:', {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });

      throw new Error(`Falha na transferência de gavetas: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Erro na transferência de gavetas TV_001:', error);
    res.status(500).json({
      error: 'Erro ao executar transferência de gavetas',
      details: error.message
    });
  }
});

// Endpoint para verificar status antes da transferência - TV_001 (LEGADO)
router.get('/verificar-status-tv001', async (req, res) => {
  try {
    const params = {
      codigo_cliente: 'TV_001',
      codigo_estacao: 'ETEN_002',
      codigo_bloco: 'BL_001',
      sub_origem: 'SUB_002',
      sub_destino: 'SUB_003',
      gavetas: [1279, 1280, 1281, 1282, 1283, 1284, 1285, 1303, 1304, 1305, 1306, 1307, 1308, 1309, 1327, 1328, 1329, 1330, 1331, 1332, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1374, 1375, 1376, 1377, 1378, 1379, 1380, 1398, 1399, 1400, 1401, 1402, 1403, 1421, 1422, 1423, 1424, 1425, 1426, 1427]
    };

    // Verificar sub-blocos
    const subBlocos = await query(`
      SELECT codigo_sub_bloco, denominacao, ativo
      FROM sub_blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
      AND codigo_sub_bloco IN ($4, $5)
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.sub_destino]);

    // Verificar gavetas existentes
    const gavetasOrigem = await query(`
      SELECT numero_gaveta, disponivel, ativo
      FROM gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
      AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5)
      ORDER BY numero_gaveta
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.gavetas]);

    // Verificar sepultamentos
    const sepultamentos = await query(`
      SELECT numero_gaveta, nome_sepultado, data_sepultamento, data_exumacao,
             CASE WHEN data_exumacao IS NULL THEN 'ATIVO' ELSE 'EXUMADO' END as status
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
      AND codigo_sub_bloco = $4 AND numero_gaveta = ANY($5) AND ativo = true
      ORDER BY numero_gaveta
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.gavetas]);

    // Verificar ranges
    const ranges = await query(`
      SELECT codigo_sub_bloco, numero_inicio, numero_fim, ativo
      FROM numeracoes_gavetas
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
      AND codigo_sub_bloco IN ($4, $5)
      ORDER BY codigo_sub_bloco, numero_inicio
    `, [params.codigo_cliente, params.codigo_estacao, params.codigo_bloco, params.sub_origem, params.sub_destino]);

    res.json({
      sub_blocos: subBlocos.rows,
      gavetas_origem: gavetasOrigem.rows,
      sepultamentos: sepultamentos.rows,
      ranges: ranges.rows,
      total_gavetas_transferir: params.gavetas.length,
      pronto_para_transferencia: subBlocos.rows.length === 2 && gavetasOrigem.rows.length > 0
    });

  } catch (error) {
    console.error('❌ Erro ao verificar status TV_001:', error);
    res.status(500).json({
      error: 'Erro ao verificar status da transferência',
      details: error.message
    });
  }
});

module.exports = router;
