const express = require('express');
const { query } = require('../database/connection');

const router = express.Router();

// Rota para buscar estatísticas gerais do dashboard
router.get('/stats', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id, codigo_cliente } = req.query;

    console.log('🔍 Dashboard stats - Parâmetros:', { userId, userType, cliente_id, codigo_cliente });

    // Validação de entrada
    if (!userId || !userType) {
      console.error('❌ Dados de usuário inválidos:', { userId, userType });
      return res.status(400).json({ error: 'Dados de usuário inválidos' });
    }

    let whereClause = '';
    let params = [];

    // Filtrar por cliente se for usuário cliente
    if (userType === 'cliente') {
      // Usuários cliente sempre veem apenas seus próprios dados, ignorando parâmetros de URL
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && (cliente_id || codigo_cliente)) {
      // Admin filtrando por cliente específico - aceitar tanto ID quanto código
      if (codigo_cliente) {
        whereClause = 'WHERE p.codigo_cliente = $1';
        params = [codigo_cliente];
      } else if (cliente_id) {
        // Se for ID numérico, buscar pelo ID
        whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
        params = [parseInt(cliente_id)];
      }
    }

    // Buscar estatísticas gerais usando nova estrutura por códigos
    let statsQuery;
    if (userType === 'cliente') {
      // Para usuário cliente, usar o código do cliente do usuário
      statsQuery = `
        SELECT
          (SELECT COUNT(*) FROM sepultamentos s
           WHERE s.ativo = true AND s.status_exumacao = false
           AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as total_sepultamentos,
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.disponivel = false
           AND g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as gavetas_ocupadas,
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.disponivel = true
           AND g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as gavetas_disponiveis,
          (SELECT COUNT(*) FROM sepultamentos s
           WHERE s.status_exumacao = true
           AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as total_exumacoes,
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as total_gavetas
      `;
    } else if (userType === 'admin' && (codigo_cliente || cliente_id)) {
      // Para admin filtrando por cliente específico
      if (codigo_cliente) {
        statsQuery = `
          SELECT
            (SELECT COUNT(*) FROM sepultamentos WHERE ativo = true AND status_exumacao = false AND codigo_cliente = $1) as total_sepultamentos,
            (SELECT COUNT(*) FROM gavetas WHERE disponivel = false AND codigo_cliente = $1) as gavetas_ocupadas,
            (SELECT COUNT(*) FROM gavetas WHERE disponivel = true AND codigo_cliente = $1) as gavetas_disponiveis,
            (SELECT COUNT(*) FROM sepultamentos WHERE status_exumacao = true AND codigo_cliente = $1) as total_exumacoes,
            (SELECT COUNT(*) FROM gavetas WHERE codigo_cliente = $1) as total_gavetas
        `;
        params = [codigo_cliente];
      } else {
        statsQuery = `
          SELECT
            (SELECT COUNT(*) FROM sepultamentos WHERE ativo = true AND status_exumacao = false AND codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)) as total_sepultamentos,
            (SELECT COUNT(*) FROM gavetas WHERE disponivel = false AND codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)) as gavetas_ocupadas,
            (SELECT COUNT(*) FROM gavetas WHERE disponivel = true AND codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)) as gavetas_disponiveis,
            (SELECT COUNT(*) FROM sepultamentos WHERE status_exumacao = true AND codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)) as total_exumacoes,
            (SELECT COUNT(*) FROM gavetas WHERE codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)) as total_gavetas
        `;
        params = [parseInt(cliente_id)];
      }
    } else {
      // Admin sem filtro - todos os dados de todos os clientes
      console.log('📊 Admin consultando TODOS os dados (sem filtro)');
      statsQuery = `
        SELECT
          (SELECT COUNT(*) FROM sepultamentos WHERE ativo = true AND status_exumacao = false) as total_sepultamentos,
          (SELECT COUNT(*) FROM gavetas WHERE disponivel = false) as gavetas_ocupadas,
          (SELECT COUNT(*) FROM gavetas WHERE disponivel = true) as gavetas_disponiveis,
          (SELECT COUNT(*) FROM sepultamentos WHERE status_exumacao = true) as total_exumacoes,
          (SELECT COUNT(*) FROM gavetas) as total_gavetas
      `;
      params = [];
    }

    console.log('📊 Executando query de stats:', {
      userType,
      codigo_cliente,
      cliente_id,
      statsQuery: statsQuery.substring(0, 100) + '...',
      params
    });

    // Executar query com ou sem parâmetros
    const statsResult = params.length > 0
      ? await query(statsQuery, params)
      : await query(statsQuery);
    const stats = statsResult.rows[0];

    console.log('✅ Stats obtidas:', stats);

    // Calcular taxa de ocupação
    const totalGavetas = parseInt(stats.total_gavetas) || 0;
    const gavetasOcupadas = parseInt(stats.gavetas_ocupadas) || 0;
    const gavetasDisponiveis = parseInt(stats.gavetas_disponiveis) || 0;
    const taxaOcupacao = totalGavetas > 0 ? ((gavetasOcupadas / totalGavetas) * 100).toFixed(1) : 0;

    console.log('📊 Cálculo de ocupação:', {
      totalGavetas,
      gavetasOcupadas,
      gavetasDisponiveis,
      taxaOcupacao: `${taxaOcupacao}%`
    });

    // Calcular taxa de sepultamento por dia
    let taxaSepultamento = '--';
    if (stats.total_sepultamentos > 1) {
      const taxaQuery = `
        SELECT 
          EXTRACT(EPOCH FROM (MAX(data_sepultamento) - MIN(data_sepultamento))) / 86400 as dias_diferenca
        FROM sepultamentos s
        JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
        ${whereClause}
        AND s.ativo = true AND s.status_exumacao = false
      `;

      const taxaResult = await query(taxaQuery, params);
      const diasDiferenca = taxaResult.rows[0]?.dias_diferenca;
      
      if (diasDiferenca && diasDiferenca > 0) {
        taxaSepultamento = (stats.total_sepultamentos / diasDiferenca).toFixed(2);
      }
    }

    res.json({
      ...stats,
      taxa_sepultamento: taxaSepultamento,
      taxa_ocupacao: parseFloat(taxaOcupacao)
    });

  } catch (error) {
    console.error('❌ Erro ao buscar estatísticas do dashboard:', error);
    console.error('❌ Stack trace:', error.stack);
    console.error('❌ Parâmetros que causaram erro:', { userId: req.user?.id, userType: req.user?.tipo_usuario, query: req.query });
    res.status(500).json({
      error: 'Erro interno do servidor',
      message: 'Erro ao buscar estatísticas do dashboard'
    });
  }
});

// Rota para buscar próximas exumações
router.get('/proximas-exumacoes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id, codigo_cliente } = req.query;

    console.log('🔍 Próximas exumações - Parâmetros:', { userId, userType, cliente_id, codigo_cliente });

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && (cliente_id || codigo_cliente)) {
      if (codigo_cliente) {
        whereClause = 'AND p.codigo_cliente = $1';
        params = [codigo_cliente];
      } else if (cliente_id) {
        whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
        params = [parseInt(cliente_id)];
      }
    } else if (userType === 'admin') {
      // Admin sem filtro - mostrar todos os dados
      console.log('📊 Admin consultando TODAS as próximas exumações (sem filtro)');
      whereClause = '';
      params = [];
    }

    const proximasQuery = `
      SELECT
        s.id,
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        b.denominacao as denominacao_bloco,
        s.numero_gaveta,
        p.codigo_estacao,
        p.codigo_cliente,
        s.data_sepultamento,
        s.data_prevista_exumacao as data_exumacao,
        EXTRACT(DAYS FROM s.data_prevista_exumacao - CURRENT_DATE) as dias_restantes
      FROM sepultamentos s
      JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON b.codigo_cliente = s.codigo_cliente AND b.codigo_estacao = s.codigo_estacao
                         AND b.codigo_bloco = s.codigo_bloco
      WHERE s.ativo = true AND s.status_exumacao = false
      AND s.data_prevista_exumacao IS NOT NULL
      ${whereClause}
      AND s.data_prevista_exumacao >= CURRENT_DATE
      ORDER BY s.data_prevista_exumacao ASC
      LIMIT 10
    `;

    const result = await query(proximasQuery, params);
    res.json(result.rows);

  } catch (error) {
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de sepultamentos
router.get('/sepultamentos-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = $1';
      params = [cliente_id];
    }

    const detailsQuery = `
      SELECT 
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(s.id) as total_sepultamentos,
        MIN(s.data_sepultamento) as primeiro_sepultamento,
        MAX(s.data_sepultamento) as ultimo_sepultamento
      FROM produtos p
      LEFT JOIN sepultamentos s ON s.codigo_cliente = p.codigo_cliente 
                                AND s.codigo_estacao = p.codigo_estacao 
                                AND s.ativo = true
                                AND s.status_exumacao = false
      ${whereClause}
      GROUP BY p.codigo_cliente, p.codigo_estacao, p.denominacao
      ORDER BY total_sepultamentos DESC
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para detalhes de gavetas por produto
router.get('/gavetas-por-produto-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = $1';
      params = [cliente_id];
    }

    const detailsQuery = `
      SELECT
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)) as total_gavetas,
        COUNT(DISTINCT CASE WHEN g.disponivel = false THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END) as gavetas_ocupadas,
        COUNT(DISTINCT CASE WHEN g.disponivel = true THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END) as gavetas_disponiveis,
        ROUND(
          (COUNT(DISTINCT CASE WHEN g.disponivel = false THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END)::DECIMAL /
           NULLIF(COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)), 0)) * 100, 2
        ) as taxa_ocupacao
      FROM produtos p
      LEFT JOIN gavetas g ON g.codigo_cliente = p.codigo_cliente AND g.codigo_estacao = p.codigo_estacao AND g.ativo = true
      ${whereClause}
      GROUP BY p.codigo_cliente, p.codigo_estacao, p.denominacao
      HAVING COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)) > 0
      ORDER BY p.denominacao
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar exumações previstas para próximos 30 dias
router.get('/exumacoes-previstas-30-dias', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = $1';
      params = [cliente_id];
    }

    const countQuery = `
      SELECT COUNT(*) as total
      FROM sepultamentos s
      JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      WHERE s.ativo = true AND s.status_exumacao = false
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
    `;

    const result = await query(countQuery, params);
    res.json({ total: parseInt(result.rows[0].total) });

  } catch (error) {
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar todas as exumações dos próximos 30 dias (Ver Mais)
router.get('/todas-exumacoes-30-dias', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    console.log('🔍 Buscando todas as exumações dos próximos 30 dias');

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = $1';
      params = [cliente_id];
    }

    const detailsQuery = `
      SELECT
        s.id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_prevista_exumacao,
        p.denominacao as produto_nome,
        p.codigo_estacao,
        c.nome_fantasia as cliente_nome,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao_calculada,
        EXTRACT(DAYS FROM (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) - CURRENT_DATE) as dias_restantes
      FROM sepultamentos s
      JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      WHERE s.ativo = true AND s.status_exumacao = false
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
      ORDER BY (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) ASC
    `;

    const result = await query(detailsQuery, params);

    console.log('✅ Exumações dos próximos 30 dias encontradas:', result.rows.length);
    res.json(result.rows);

  } catch (error) {
    console.error('❌ Erro ao buscar exumações dos próximos 30 dias:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de exumações previstas (próximos e últimos 30 dias)
router.get('/exumacoes-previstas-detalhes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    console.log('🔍 Buscando detalhes de exumações previstas (próximos e últimos 30 dias)');

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = $1';
      params = [cliente_id];
    }

    // Buscar exumações dos últimos 30 dias
    const ultimosQuery = `
      SELECT
        s.id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_prevista_exumacao,
        p.denominacao as produto_nome,
        p.codigo_estacao,
        c.nome_fantasia as cliente_nome,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao_calculada,
        EXTRACT(DAYS FROM CURRENT_DATE - (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar)) as dias_passados,
        'passado' as periodo
      FROM sepultamentos s
      JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      WHERE s.ativo = true AND s.exumado_em IS NULL
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) BETWEEN CURRENT_DATE - INTERVAL '30 days' AND CURRENT_DATE
    `;

    // Buscar exumações dos próximos 30 dias
    const proximosQuery = `
      SELECT
        s.id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.data_prevista_exumacao,
        p.denominacao as produto_nome,
        p.codigo_estacao,
        c.nome_fantasia as cliente_nome,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao_calculada,
        EXTRACT(DAYS FROM (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) - CURRENT_DATE) as dias_restantes,
        'futuro' as periodo
      FROM sepultamentos s
      JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      JOIN clientes c ON p.codigo_cliente = c.codigo_cliente
      WHERE s.ativo = true AND s.status_exumacao = false
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
    `;

    const [ultimosResult, proximosResult] = await Promise.all([
      query(ultimosQuery, params),
      query(proximosQuery, params)
    ]);

    const response = {
      ultimos30Dias: ultimosResult.rows,
      proximos30Dias: proximosResult.rows,
      total: ultimosResult.rows.length + proximosResult.rows.length
    };

    console.log('✅ Detalhes de exumações encontrados:', {
      ultimos: ultimosResult.rows.length,
      proximos: proximosResult.rows.length,
      total: response.total
    });

    res.json(response);

  } catch (error) {
    console.error('❌ Erro ao buscar detalhes de exumações previstas:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes da taxa de ocupação por produto
router.get('/taxa-ocupacao-detalhes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id, codigo_cliente } = req.query;

    console.log('🔍 Taxa ocupação detalhes - Parâmetros:', { userId, userType, cliente_id, codigo_cliente });

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && (cliente_id || codigo_cliente)) {
      if (codigo_cliente) {
        whereClause = 'WHERE p.codigo_cliente = $1';
        params = [codigo_cliente];
      } else if (cliente_id) {
        whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
        params = [parseInt(cliente_id)];
      }
    } else if (userType === 'admin') {
      // Admin sem filtro - mostrar todos os dados
      console.log('📊 Admin consultando TODOS os detalhes de ocupação (sem filtro)');
      whereClause = '';
      params = [];
    }

    const detailsQuery = `
      SELECT
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)) as total_gavetas,
        COUNT(DISTINCT CASE WHEN g.disponivel = false THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END) as gavetas_ocupadas,
        COUNT(DISTINCT CASE WHEN g.disponivel = true THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END) as gavetas_disponiveis,
        ROUND(
          (COUNT(DISTINCT CASE WHEN g.disponivel = false THEN CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta) END)::numeric /
           NULLIF(COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)), 0)) * 100, 2
        ) as taxa_ocupacao
      FROM produtos p
      LEFT JOIN gavetas g ON g.codigo_cliente = p.codigo_cliente AND g.codigo_estacao = p.codigo_estacao AND g.ativo = true
      ${whereClause}
      GROUP BY p.codigo_cliente, p.codigo_estacao, p.denominacao
      HAVING COUNT(DISTINCT CONCAT(g.codigo_cliente, g.codigo_estacao, g.codigo_bloco, g.codigo_sub_bloco, g.numero_gaveta)) > 0
      ORDER BY p.denominacao
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('❌ Erro ao buscar detalhes da taxa de ocupação:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar exumações dos próximos 30 dias
router.get('/exumacoes-30-dias', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id, codigo_cliente } = req.query;

    console.log('🔍 Exumações 30 dias - Parâmetros:', { userId, userType, cliente_id, codigo_cliente });

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && (cliente_id || codigo_cliente)) {
      if (codigo_cliente) {
        whereClause = 'AND p.codigo_cliente = $1';
        params = [codigo_cliente];
      } else if (cliente_id) {
        whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
        params = [parseInt(cliente_id)];
      }
    } else if (userType === 'admin') {
      // Admin sem filtro - mostrar todos os dados
      console.log('📊 Admin consultando TODAS as exumações 30 dias (sem filtro)');
      whereClause = '';
      params = [];
    }

    const exumacoesQuery = `
      SELECT
        s.id as sepultamento_id,
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        b.denominacao as denominacao_bloco,
        s.numero_gaveta,
        p.codigo_estacao,
        p.codigo_cliente,
        s.data_sepultamento,
        s.data_prevista_exumacao as data_exumacao,
        EXTRACT(DAYS FROM s.data_prevista_exumacao - CURRENT_DATE) as dias_restantes
      FROM sepultamentos s
      JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON b.codigo_cliente = s.codigo_cliente AND b.codigo_estacao = s.codigo_estacao
                         AND b.codigo_bloco = s.codigo_bloco
      WHERE s.ativo = true AND s.status_exumacao = false
      AND s.data_prevista_exumacao IS NOT NULL
      ${whereClause}
      AND s.data_prevista_exumacao BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
      ORDER BY s.data_prevista_exumacao ASC
    `;

    const result = await query(exumacoesQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('❌ Erro ao buscar exumações dos próximos 30 dias:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar todas as exumações separadas por produto
router.get('/todas-exumacoes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id, codigo_cliente } = req.query;

    console.log('🔍 Todas exumações - Parâmetros:', { userId, userType, cliente_id, codigo_cliente });

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && (cliente_id || codigo_cliente)) {
      if (codigo_cliente) {
        whereClause = 'AND p.codigo_cliente = $1';
        params = [codigo_cliente];
      } else if (cliente_id) {
        whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
        params = [parseInt(cliente_id)];
      }
    } else if (userType === 'admin') {
      // Admin sem filtro - mostrar todos os dados
      console.log('📊 Admin consultando TODAS as exumações (sem filtro)');
      whereClause = '';
      params = [];
    }

    const todasExumacoesQuery = `
      SELECT
        s.id as sepultamento_id,
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        b.denominacao as denominacao_bloco,
        g.numero_gaveta,
        s.data_sepultamento,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao,
        EXTRACT(DAYS FROM (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) - CURRENT_DATE) as dias_restantes,
        CASE WHEN s.status_exumacao = true THEN 'Exumado' ELSE 'Pendente' END as status_exumacao
      FROM sepultamentos s
      JOIN gavetas g ON g.codigo_cliente = s.codigo_cliente AND g.codigo_estacao = s.codigo_estacao
                     AND g.codigo_bloco = s.codigo_bloco AND g.codigo_sub_bloco = s.codigo_sub_bloco
                     AND g.numero_gaveta = s.numero_gaveta
      JOIN sub_blocos sb ON sb.codigo_cliente = g.codigo_cliente AND sb.codigo_estacao = g.codigo_estacao
                         AND sb.codigo_bloco = g.codigo_bloco AND sb.codigo_sub_bloco = g.codigo_sub_bloco
      JOIN blocos b ON b.codigo_cliente = sb.codigo_cliente AND b.codigo_estacao = sb.codigo_estacao
                    AND b.codigo_bloco = sb.codigo_bloco
      JOIN produtos p ON p.codigo_cliente = b.codigo_cliente AND p.codigo_estacao = b.codigo_estacao
      WHERE s.ativo = true
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) >= CURRENT_DATE
      ORDER BY p.denominacao, data_exumacao ASC
    `;

    const result = await query(todasExumacoesQuery, params);

    // Agrupar por produto
    const exumacoesPorProduto = result.rows.reduce((acc, exumacao) => {
      const produto = exumacao.denominacao_produto;
      if (!acc[produto]) {
        acc[produto] = [];
      }
      acc[produto].push(exumacao);
      return acc;
    }, {});

    res.json(exumacoesPorProduto);

  } catch (error) {
    console.error('❌ Erro ao buscar todas as exumações:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes da taxa de sepultamento por produto
router.get('/taxa-sepultamento-detalhes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const detailsQuery = `
      SELECT
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(DISTINCT s.id) as total_sepultamentos,
        COUNT(DISTINCT CASE WHEN s.status_exumacao = false THEN s.id END) as sepultamentos_ativos,
        COUNT(DISTINCT CASE WHEN s.status_exumacao = true THEN s.id END) as sepultamentos_exumados,
        MIN(s.data_sepultamento) as primeiro_sepultamento,
        MAX(s.data_sepultamento) as ultimo_sepultamento,
        ROUND(
          (COUNT(DISTINCT CASE WHEN s.status_exumacao = false THEN s.id END)::numeric /
           NULLIF(COUNT(DISTINCT s.id), 0)) * 100, 2
        ) as taxa_sepultamentos_ativos,
        ROUND(
          COUNT(DISTINCT s.id)::numeric /
          NULLIF(EXTRACT(DAYS FROM (MAX(s.data_sepultamento) - MIN(s.data_sepultamento))) + 1, 0), 2
        ) as taxa_sepultamento_por_dia
      FROM produtos p
      LEFT JOIN sepultamentos s ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao AND s.ativo = true
      ${whereClause}
      GROUP BY p.codigo_cliente, p.codigo_estacao, p.denominacao
      HAVING COUNT(DISTINCT s.id) > 0
      ORDER BY p.denominacao
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('❌ Erro ao buscar detalhes da taxa de sepultamento:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

module.exports = router;
