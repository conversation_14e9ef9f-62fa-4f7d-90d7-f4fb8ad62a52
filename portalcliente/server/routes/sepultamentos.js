const express = require('express');
const { query } = require('../database/connection');
const { logSepultamento, logExumacao } = require('../utils/logger');

const router = express.Router();

// Listar sepultamentos
router.get('/', async (req, res) => {
  try {
    const {
      codigo_cliente,
      codigo_bloco,
      codigo_sub_bloco,
      codigo_estacao,
      produto_id,
      data_inicio,
      data_fim,
      ativo = 'true'
    } = req.query;
    
    let whereClause = 'WHERE s.ativo = $1';
    let params = [ativo === 'true'];
    let paramCount = 1;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    } else if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    if (codigo_estacao) {
      paramCount++;
      whereClause += ` AND p.codigo_estacao = $${paramCount}`;
      params.push(codigo_estacao);
    }

    if (codigo_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_bloco = $${paramCount}`;
      params.push(codigo_bloco);
    }

    if (codigo_sub_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_sub_bloco = $${paramCount}`;
      params.push(codigo_sub_bloco);
    }

    // Filtros para relatórios
    if (produto_id) {
      paramCount++;
      whereClause += ` AND p.id = $${paramCount}`;
      params.push(produto_id);
    }

    if (data_inicio) {
      paramCount++;
      whereClause += ` AND s.data_sepultamento >= $${paramCount}`;
      params.push(data_inicio);
    }

    if (data_fim) {
      paramCount++;
      whereClause += ` AND s.data_sepultamento <= $${paramCount}`;
      params.push(data_fim);
    }

    const result = await query(`
      SELECT
        s.*,
        s.nome_sepultado as nome_falecido,
        s.horario_sepultamento as hora_sepultamento,
        s.data_exumacao IS NOT NULL as exumado,
        c.nome as nome_cliente,
        p.codigo_estacao,
        p.denominacao as produto_denominacao,
        b.denominacao as denominacao_bloco,
        b.nome as bloco_nome,
        sb.nome as sub_bloco_nome
      FROM sepultamentos s
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      ${whereClause}
      ORDER BY s.data_sepultamento DESC
    `, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao listar sepultamentos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sepultamentos por estação (código)
router.get('/estacao/:codigoEstacao', async (req, res) => {
  try {
    const { codigoEstacao } = req.params;

    let whereClause = 'WHERE s.codigo_estacao = $1 AND s.ativo = true';
    let params = [codigoEstacao];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT s.*,
             p.denominacao as produto_denominacao,
             p.codigo_estacao,
             b.denominacao as denominacao_bloco,
             b.codigo_bloco,
             sb.denominacao as sub_bloco_nome,
             sb.codigo_sub_bloco
      FROM sepultamentos s
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.nome_sepultado ASC
    `, params);

    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao listar sepultamentos por estação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar sepultamento por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    let whereClause = 'WHERE s.id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT 
        s.*,
        g.posicao_x,
        g.posicao_y,
        g.altura_especial,
        c.nome as nome_cliente
      FROM sepultamentos s
      LEFT JOIN gavetas g ON s.gaveta_id = g.id
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      ${whereClause}
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Erro ao buscar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo sepultamento
router.post('/', async (req, res) => {
  try {
    const {
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      codigo_sub_bloco,
      numero_gaveta,
      posicao = 1,
      localizacao,
      observacoes
    } = req.body;

    console.log('📝 Dados recebidos para cadastro:', req.body);

    // Validações básicas
    if (!nome_sepultado || !data_sepultamento || !codigo_bloco || !codigo_sub_bloco || !numero_gaveta) {
      return res.status(400).json({ error: 'Campos obrigatórios não preenchidos' });
    }

    // Se não for admin, usar o código do cliente do usuário logado
    const clienteCode = req.user.tipo_usuario === 'admin' ? codigo_cliente : req.user.codigo_cliente;

    // Buscar codigo_estacao do produto do cliente
    let estacaoCode = codigo_estacao;
    if (!estacaoCode) {
      const produtoResult = await query(`
        SELECT codigo_estacao
        FROM produtos
        WHERE codigo_cliente = $1
        LIMIT 1
      `, [clienteCode]);

      if (produtoResult.rows.length === 0) {
        return res.status(404).json({ error: 'Produto não encontrado para este cliente' });
      }

      estacaoCode = produtoResult.rows[0].codigo_estacao;
    }

    // Verificar se já existe sepultamento ativo nesta gaveta
    const gavetaOcupada = await query(`
      SELECT id FROM sepultamentos
      WHERE codigo_cliente = $1
        AND codigo_estacao = $2
        AND codigo_bloco = $3
        AND codigo_sub_bloco = $4
        AND numero_gaveta = $5
        AND ativo = true
    `, [clienteCode, estacaoCode, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    if (gavetaOcupada.rows.length > 0) {
      return res.status(400).json({ error: 'Esta gaveta já está ocupada por outro sepultamento ativo' });
    }

    // Calcular data de exumação (36 meses após sepultamento)
    const dataExumacao = new Date(data_sepultamento);
    dataExumacao.setMonth(dataExumacao.getMonth() + 36);

    // Inserir sepultamento
    const result = await query(`
      INSERT INTO sepultamentos
      (nome_sepultado, data_sepultamento, horario_sepultamento, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao, localizacao, observacoes, data_exumacao, ativo)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, true)
      RETURNING *
    `, [nome_sepultado, data_sepultamento, horario_sepultamento, clienteCode, estacaoCode, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao, localizacao, observacoes, dataExumacao]);

    // Criar ou atualizar gaveta na tabela gavetas se necessário
    await query(`
      INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, posicao_x, posicao_y, disponivel)
      VALUES ($1, $2, $3, $4, $5, 1, 1, false)
      ON CONFLICT (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta)
      DO UPDATE SET disponivel = false
    `, [clienteCode, estacaoCode, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    const novoSepultamento = result.rows[0];

    // Registrar log e enviar webhook
    await logSepultamento(
      req.user.id,
      'CREATE',
      novoSepultamento,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Sepultamento cadastrado com sucesso',
      sepultamento: novoSepultamento
    });

  } catch (error) {
    console.error('Erro ao criar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar sepultamento
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      localizacao,
      observacoes
    } = req.body;

    // Buscar dados anteriores
    let whereClause = 'WHERE id = $1';
    let params = [id];

    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const dadosAnteriores = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    // Atualizar sepultamento
    const result = await query(`
      UPDATE sepultamentos
      SET nome_sepultado = $1, data_sepultamento = $2, horario_sepultamento = $3, localizacao = $4, observacoes = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [nome_sepultado, data_sepultamento, horario_sepultamento, localizacao, observacoes, id]);

    const sepultamentoAtualizado = result.rows[0];
    sepultamentoAtualizado.dadosAnteriores = dadosAnteriores.rows[0];

    // Registrar log e enviar webhook
    await logSepultamento(
      req.user.id,
      'EDIT',
      sepultamentoAtualizado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Sepultamento atualizado com sucesso',
      sepultamento: sepultamentoAtualizado
    });

  } catch (error) {
    console.error('Erro ao atualizar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Exumar sepultamento
router.post('/:id/exumar', async (req, res) => {
  try {
    const { id } = req.params;
    const { data_exumacao, horario_exumacao, observacoes_exumacao } = req.body;

    if (!data_exumacao) {
      return res.status(400).json({ error: 'Data de exumação é obrigatória' });
    }

    // Buscar dados anteriores
    let whereClause = 'WHERE id = $1';
    let params = [id];

    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const dadosAnteriores = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    if (dadosAnteriores.rows[0].data_exumacao) {
      return res.status(400).json({ error: 'Sepultamento já foi exumado' });
    }

    // Atualizar sepultamento com data e horário de exumação e marcar como inativo
    const result = await query(`
      UPDATE sepultamentos
      SET data_exumacao = $1, horario_exumacao = $2, observacoes_exumacao = $3, status_exumacao = true, ativo = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [data_exumacao, horario_exumacao, observacoes_exumacao, id]);

    // Liberar gaveta
    await query(
      'UPDATE gavetas SET disponivel = true WHERE id = $1',
      [dadosAnteriores.rows[0].gaveta_id]
    );

    const sepultamentoExumado = result.rows[0];
    sepultamentoExumado.dadosAnteriores = dadosAnteriores.rows[0];

    // Registrar log e enviar webhook
    await logExumacao(
      req.user.id,
      sepultamentoExumado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Exumação realizada com sucesso',
      sepultamento: sepultamentoExumado
    });

  } catch (error) {
    console.error('Erro ao exumar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sepultamento (apenas admin e apenas se exumado)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Apenas admin pode deletar
    if (req.user.tipo_usuario !== 'admin') {
      return res.status(403).json({ error: 'Apenas administradores podem deletar sepultamentos' });
    }

    // Buscar sepultamento
    const sepultamento = await query(
      'SELECT * FROM sepultamentos WHERE id = $1',
      [id]
    );

    if (sepultamento.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const sepultamentoData = sepultamento.rows[0];

    // Verificar se foi exumado
    if (!sepultamentoData.data_exumacao) {
      return res.status(400).json({ error: 'Apenas sepultamentos exumados podem ser deletados' });
    }

    // Deletar sepultamento
    await query('DELETE FROM sepultamentos WHERE id = $1', [id]);

    // Registrar log
    await logSepultamento(
      req.user.id,
      'DELETE',
      sepultamentoData,
      req.ip,
      req.get('User-Agent')
    );

    res.json({ message: 'Sepultamento deletado com sucesso' });

  } catch (error) {
    console.error('Erro ao deletar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
