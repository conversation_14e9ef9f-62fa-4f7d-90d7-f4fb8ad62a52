/**
 * =====================================================
 * ROTAS PARA GERENCIAMENTO DE RANGES DE GAVETAS
 * =====================================================
 * Implementa todas as operações CRUD para ranges de gavetas
 * conforme especificado em definindo_range.md
 */

const express = require('express');
const router = express.Router();
const rangeValidationService = require('../services/rangeValidationService');
const { query } = require('../database/connection');

// =====================================================
// LISTAR RANGES DE UM SUB-BLOCO (NOVA ARQUITETURA)
// =====================================================
router.get('/sub-blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/ranges', async (req, res) => {
    try {
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;

        console.log('📋 Listando ranges do sub-bloco (nova arquitetura):', { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco });

        // Usar função SQL detalhada para nova arquitetura
        const result = await query(`
            SELECT * FROM listar_ranges_detalhados($1, $2, $3, $4)
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

        const ranges = result.rows;

        // Calcular total geral de gavetas
        const totalGavetas = await query(`
            SELECT calcular_total_gavetas_ranges($1, $2, $3, $4) as total
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

        res.json({
            success: true,
            ranges: ranges,
            total_gavetas: totalGavetas.rows[0].total || 0,
            sub_bloco: {
                codigo_cliente,
                codigo_estacao,
                codigo_bloco,
                codigo_sub_bloco
            }
        });

    } catch (error) {
        console.error('❌ Erro ao listar ranges:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// CRIAR NOVO RANGE
// =====================================================
router.post('/sub-blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/ranges', async (req, res) => {
    try {
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;
        const { numero_inicio, numero_fim } = req.body;

        console.log('🔨 Criando novo range:', { 
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio, numero_fim 
        });

        // Validar dados obrigatórios
        if (!numero_inicio || !numero_fim) {
            return res.status(400).json({
                success: false,
                error: 'Número de início e fim são obrigatórios'
            });
        }

        // Verificar se sub-bloco existe
        const subBlocoCheck = await query(`
            SELECT 1 FROM sub_blocos 
            WHERE codigo_cliente = $1 AND codigo_estacao = $2 
              AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND ativo = true
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

        if (subBlocoCheck.rows.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Sub-bloco não encontrado'
            });
        }

        // PASSO 1: Validar associações hierárquicas conforme instrucao.md
        const associacaoResult = await query(`
            SELECT validar_associacoes_hierarquicas($1, $2, $3, $4) as resultado
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

        if (associacaoResult.rows[0].resultado !== 'OK') {
            return res.status(400).json({
                success: false,
                error: associacaoResult.rows[0].resultado
            });
        }

        // PASSO 2: Validar conflitos conforme instrucao.md
        const conflitosResult = await query(`
            SELECT validar_conflito_ranges($1, $2, $3, $4, $5, $6) as resultado
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, parseInt(numero_inicio), parseInt(numero_fim)]);

        if (conflitosResult.rows[0].resultado !== 'OK') {
            return res.status(400).json({
                success: false,
                error: conflitosResult.rows[0].resultado
            });
        }

        // PASSO 3: Criar range na tabela numeracoes_gavetas
        const rangeResult = await query(`
            INSERT INTO numeracoes_gavetas (
                codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
                numero_inicio, numero_fim, ativo, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, true, NOW(), NOW())
            RETURNING *
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, parseInt(numero_inicio), parseInt(numero_fim)]);

        // PASSO 4: Criar gavetas automaticamente 1 por 1 conforme instrucao.md
        const gavetasResult = await query(`
            SELECT criar_gavetas_automaticamente($1, $2, $3, $4, $5, $6) as resultado
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, parseInt(numero_inicio), parseInt(numero_fim)]);

        console.log('✅ Range criado e gavetas geradas:', gavetasResult.rows[0].resultado);

        res.status(201).json({
            success: true,
            range: rangeResult.rows[0],
            gavetas_info: gavetasResult.rows[0].resultado,
            message: 'Range criado com sucesso e gavetas geradas automaticamente'
        });

    } catch (error) {
        console.error('❌ Erro ao criar range:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// ATUALIZAR RANGE EXISTENTE
// =====================================================
router.put('/ranges/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim } = req.body;

        console.log('🔄 Atualizando range:', { 
            id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio, numero_fim 
        });

        // Validar dados obrigatórios
        if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !codigo_sub_bloco || !numero_inicio || !numero_fim) {
            return res.status(400).json({
                success: false,
                error: 'Todos os campos são obrigatórios'
            });
        }

        // Atualizar range
        const result = await rangeValidationService.updateRange(parseInt(id), {
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio: parseInt(numero_inicio),
            numero_fim: parseInt(numero_fim)
        });

        if (!result.success) {
            return res.status(400).json(result);
        }

        res.json(result);

    } catch (error) {
        console.error('❌ Erro ao atualizar range:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// DELETAR RANGE
// =====================================================
router.delete('/ranges/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.body;

        console.log('🗑️ Deletando range:', { 
            id, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco 
        });

        // Validar dados obrigatórios
        if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !codigo_sub_bloco) {
            return res.status(400).json({
                success: false,
                error: 'Códigos de identificação são obrigatórios'
            });
        }

        // Deletar range
        const result = await rangeValidationService.deleteRange(
            parseInt(id), codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
        );

        if (!result.success) {
            console.log('❌ Falha na deleção do range:', result.error);
            return res.status(400).json(result);
        }

        console.log('✅ Range deletado com sucesso');
        res.json(result);

    } catch (error) {
        console.error('❌ Erro ao deletar range:', error);

        // Melhor tratamento de erros específicos
        let errorMessage = 'Erro interno ao deletar range';
        if (error.message && error.message.includes('conflita')) {
            errorMessage = 'Erro interno ao deletar range - conflito detectado';
        }

        res.status(500).json({
            success: false,
            error: errorMessage
        });
    }
});

// =====================================================
// VALIDAR RANGE (ENDPOINT DE TESTE)
// =====================================================
router.post('/validate-range', async (req, res) => {
    try {
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, excluir_id } = req.body;

        console.log('🔍 Validando range:', req.body);

        const result = await rangeValidationService.validateRange({
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco,
            numero_inicio: parseInt(numero_inicio),
            numero_fim: parseInt(numero_fim),
            excluir_id: excluir_id ? parseInt(excluir_id) : null
        });

        res.json(result);

    } catch (error) {
        console.error('❌ Erro na validação:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// SINCRONIZAR GAVETAS
// =====================================================
router.post('/sub-blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/sync-gavetas', async (req, res) => {
    try {
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;

        console.log('🔄 Sincronizando gavetas:', { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco });

        await rangeValidationService.syncGavetas(
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
        );

        res.json({
            success: true,
            message: 'Gavetas sincronizadas com sucesso'
        });

    } catch (error) {
        console.error('❌ Erro na sincronização:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// VALIDAR DELEÇÃO HIERÁRQUICA
// =====================================================
router.post('/validate-deletion', async (req, res) => {
    try {
        const { tipo, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta } = req.body;

        console.log('🔍 Validando deleção:', req.body);

        const result = await rangeValidationService.validateDeletion(tipo, {
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta
        });

        res.json(result);

    } catch (error) {
        console.error('❌ Erro na validação de deleção:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// LISTAR GAVETAS DE UM RANGE
// =====================================================
router.get('/ranges/:id/gavetas', async (req, res) => {
    try {
        const { id } = req.params;

        console.log('📋 Listando gavetas do range:', id);

        // Buscar range
        const rangeResult = await query(`
            SELECT * FROM numeracoes_gavetas WHERE id = $1 AND ativo = true
        `, [id]);

        if (rangeResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Range não encontrado'
            });
        }

        const range = rangeResult.rows[0];

        // Buscar gavetas do range
        const gavetasResult = await query(`
            SELECT 
                g.*,
                CASE WHEN s.id IS NOT NULL THEN s.nome_sepultado ELSE NULL END as sepultado_nome,
                CASE WHEN s.id IS NOT NULL THEN s.data_sepultamento ELSE NULL END as data_sepultamento
            FROM gavetas g
            LEFT JOIN sepultamentos s ON g.codigo_cliente = s.codigo_cliente
                AND g.codigo_estacao = s.codigo_estacao
                AND g.codigo_bloco = s.codigo_bloco
                AND g.codigo_sub_bloco = s.codigo_sub_bloco
                AND g.numero_gaveta = s.numero_gaveta
                AND s.ativo = true AND s.data_exumacao IS NULL
            WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 
              AND g.codigo_bloco = $3 AND g.codigo_sub_bloco = $4
              AND g.numero_gaveta BETWEEN $5 AND $6
              AND g.ativo = true
            ORDER BY g.numero_gaveta
        `, [
            range.codigo_cliente, range.codigo_estacao, range.codigo_bloco, 
            range.codigo_sub_bloco, range.numero_inicio, range.numero_fim
        ]);

        res.json({
            success: true,
            range: range,
            gavetas: gavetasResult.rows
        });

    } catch (error) {
        console.error('❌ Erro ao listar gavetas do range:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor'
        });
    }
});

// =====================================================
// VALIDAR RANGE (CONFORME INSTRUCAO.MD)
// =====================================================
router.post('/validate-range', async (req, res) => {
    try {
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, range_id } = req.body;

        console.log('🔍 Validando range conforme instrucao.md:', {
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim
        });

        // Validar conflitos usando função do banco (CONFORME INSTRUCAO.MD)
        const conflictResult = await query(`
            SELECT validar_conflito_ranges($1, $2, $3, $4, $5, $6, $7) as resultado
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_inicio, numero_fim, range_id]);

        const resultado = conflictResult.rows[0].resultado;

        if (resultado !== 'OK') {
            return res.json({
                success: false,
                error: resultado,
                tipo: 'conflito'
            });
        }

        res.json({
            success: true,
            message: 'Range válido - sem conflitos'
        });
    } catch (error) {
        console.error('❌ Erro ao validar range:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

// =====================================================
// CALCULAR TOTAL DE GAVETAS POR RANGES (CONFORME INSTRUCAO.MD)
// =====================================================
router.get('/sub-blocos/:codigo_cliente/:codigo_estacao/:codigo_bloco/:codigo_sub_bloco/total-gavetas', async (req, res) => {
    try {
        const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco } = req.params;

        console.log('📊 Calculando total de gavetas por ranges conforme instrucao.md:', {
            codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco
        });

        // Calcular total usando função do banco (CONFORME INSTRUCAO.MD)
        const totalResult = await query(`
            SELECT calcular_total_gavetas_ranges($1, $2, $3, $4) as total_gavetas
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco]);

        const totalGavetas = totalResult.rows[0].total_gavetas || 0;

        res.json({
            success: true,
            total_gavetas: totalGavetas,
            codigo_cliente,
            codigo_estacao,
            codigo_bloco,
            codigo_sub_bloco
        });
    } catch (error) {
        console.error('❌ Erro ao calcular total de gavetas:', error);
        res.status(500).json({ error: 'Erro interno do servidor' });
    }
});

module.exports = router;
