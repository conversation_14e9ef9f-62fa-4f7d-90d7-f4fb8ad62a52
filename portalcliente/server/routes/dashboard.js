const express = require('express');
const { query } = require('../database/connection');

const router = express.Router();

// Rota para buscar estatísticas gerais do dashboard
router.get('/stats', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    // Filtrar por cliente se for usuário cliente
    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      // Admin filtrando por cliente específico
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    // Buscar estatísticas gerais
    let statsQuery;
    if (userType === 'cliente' || (userType === 'admin' && cliente_id)) {
      statsQuery = `
        SELECT
          (SELECT COUNT(*) FROM sepultamentos s
           WHERE s.ativo = true AND s.status_exumacao = false
           AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as total_sepultamentos,
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.disponivel = false AND g.ativo = true
           AND g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as gavetas_ocupadas,
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.disponivel = true AND g.ativo = true
           AND g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as gavetas_disponiveis,
          (SELECT COUNT(*) FROM sepultamentos s
           WHERE s.status_exumacao = true
           AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as total_exumacoes,
          (SELECT COUNT(*) FROM gavetas g
           WHERE g.ativo = true
           AND g.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)) as total_gavetas
      `;
    } else {
      statsQuery = `
        SELECT
          (SELECT COUNT(*) FROM sepultamentos WHERE ativo = true AND status_exumacao = false) as total_sepultamentos,
          (SELECT COUNT(*) FROM gavetas WHERE disponivel = false AND ativo = true) as gavetas_ocupadas,
          (SELECT COUNT(*) FROM gavetas WHERE disponivel = true AND ativo = true) as gavetas_disponiveis,
          (SELECT COUNT(*) FROM sepultamentos WHERE status_exumacao = true) as total_exumacoes,
          (SELECT COUNT(*) FROM gavetas WHERE ativo = true) as total_gavetas
      `;
    }

    const statsResult = await query(statsQuery, params);
    const stats = statsResult.rows[0];

    // Calcular taxa de sepultamento por dia baseada na média de todas as estações/produtos
    let taxaSepultamento = '0.00';
    if (stats.total_sepultamentos > 0) {
      try {
        const taxaQuery = `
          SELECT
            p.denominacao as produto,
            p.codigo_estacao,
            COUNT(s.id) as total_sepultamentos_produto,
            EXTRACT(EPOCH FROM (MAX(s.data_sepultamento) - MIN(s.data_sepultamento))) / 86400 as dias_diferenca_produto
          FROM produtos p
          LEFT JOIN sepultamentos s ON (
            p.codigo_cliente = s.codigo_cliente AND
            p.codigo_estacao = s.codigo_estacao AND
            s.ativo = true AND s.status_exumacao = false
          )
          ${whereClause}
          GROUP BY p.id, p.denominacao, p.codigo_estacao
          HAVING COUNT(s.id) >= 1
        `;

        const taxaResult = await query(taxaQuery, params);

        if (taxaResult.rows.length > 0) {
          // Calcular a média das taxas de todos os produtos/estações
          let somasTaxas = 0;
          let contadorProdutos = 0;

          taxaResult.rows.forEach(produto => {
            if (produto.total_sepultamentos_produto > 0) {
              let taxaProduto;
              if (produto.dias_diferenca_produto && produto.dias_diferenca_produto > 0) {
                // Se há diferença de dias, calcular taxa por dia
                taxaProduto = produto.total_sepultamentos_produto / produto.dias_diferenca_produto;
              } else {
                // Se não há diferença (apenas 1 sepultamento), considerar taxa de 1 por dia
                taxaProduto = 1;
              }
              somasTaxas += taxaProduto;
              contadorProdutos++;
            }
          });

          if (contadorProdutos > 0) {
            taxaSepultamento = (somasTaxas / contadorProdutos).toFixed(2);
          }
        }
      } catch (error) {
        console.error('Erro ao calcular taxa de sepultamento:', error);
        taxaSepultamento = '0.00';
      }
    }

    res.json({
      ...stats,
      taxa_sepultamento: taxaSepultamento
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes da taxa de sepultamento por produto
router.get('/taxa-sepultamento-detalhes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const taxaQuery = `
      SELECT
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(s.id) as total_sepultamentos,
        MIN(s.data_sepultamento) as primeiro_sepultamento,
        MAX(s.data_sepultamento) as ultimo_sepultamento,
        CASE
          WHEN COUNT(s.id) > 1 THEN
            EXTRACT(DAYS FROM MAX(s.data_sepultamento) - MIN(s.data_sepultamento))
          ELSE 0
        END as dias_diferenca,
        CASE
          WHEN COUNT(s.id) > 1 AND EXTRACT(DAYS FROM MAX(s.data_sepultamento) - MIN(s.data_sepultamento)) > 0 THEN
            ROUND(COUNT(s.id)::numeric / EXTRACT(DAYS FROM MAX(s.data_sepultamento) - MIN(s.data_sepultamento)), 2)
          WHEN COUNT(s.id) = 1 THEN 1.00
          ELSE 0.00
        END as taxa_sepultamento_por_dia
      FROM produtos p
      LEFT JOIN sepultamentos s ON (
        p.codigo_cliente = s.codigo_cliente AND
        p.codigo_estacao = s.codigo_estacao AND
        s.ativo = true AND s.status_exumacao = false
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao, p.codigo_estacao
      HAVING COUNT(s.id) > 0
      ORDER BY p.denominacao
    `;

    const result = await query(taxaQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes da taxa de sepultamento:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes da taxa de ocupação por produto
router.get('/taxa-ocupacao-detalhes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const ocupacaoQuery = `
      SELECT
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(CASE WHEN g.ativo = true THEN 1 END) as total_gavetas,
        COUNT(CASE WHEN g.disponivel = false AND g.ativo = true THEN 1 END) as gavetas_ocupadas,
        COUNT(CASE WHEN g.disponivel = true AND g.ativo = true THEN 1 END) as gavetas_disponiveis,
        ROUND(
          (COUNT(CASE WHEN g.disponivel = false AND g.ativo = true THEN 1 END)::numeric /
           NULLIF(COUNT(CASE WHEN g.ativo = true THEN 1 END), 0)) * 100, 2
        ) as taxa_ocupacao
      FROM produtos p
      LEFT JOIN gavetas g ON (
        p.codigo_cliente = g.codigo_cliente AND
        p.codigo_estacao = g.codigo_estacao
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao, p.codigo_estacao
      ORDER BY p.denominacao
    `;

    const result = await query(ocupacaoQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes da taxa de ocupação:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar próximas exumações
router.get('/proximas-exumacoes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND s.codigo_cliente = $1';
      params = [parseInt(cliente_id)];
    }

    const proximasQuery = `
      SELECT
        s.id,
        s.nome_sepultado,
        s.codigo_estacao,
        s.codigo_bloco,
        s.numero_gaveta,
        s.data_sepultamento,
        s.data_prevista_exumacao,
        EXTRACT(DAYS FROM s.data_prevista_exumacao - CURRENT_DATE) as dias_restantes,
        CASE WHEN s.status_exumacao = true THEN 'Exumado' ELSE 'Pendente' END as status_exumacao
      FROM sepultamentos s
      WHERE s.ativo = true AND s.status_exumacao = false
      ${whereClause}
      AND s.data_prevista_exumacao >= CURRENT_DATE
      ORDER BY s.data_prevista_exumacao ASC
      LIMIT 10
    `;

    const result = await query(proximasQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar próximas exumações:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar exumações dos próximos 30 dias
router.get('/exumacoes-30-dias', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const exumacoes30Query = `
      SELECT
        s.id,
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        p.codigo_estacao,
        b.denominacao as denominacao_bloco,
        g.numero_gaveta,
        s.data_sepultamento,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao,
        EXTRACT(DAYS FROM (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) - CURRENT_DATE) as dias_restantes,
        CASE WHEN s.exumado_em IS NOT NULL THEN 'Exumado' ELSE 'Pendente' END as status_exumacao
      FROM sepultamentos s
      JOIN gavetas g ON g.id = s.gaveta_id
      JOIN sub_blocos sb ON sb.id = g.sub_bloco_id
      JOIN blocos b ON b.id = sb.bloco_id
      JOIN produtos p ON p.id = b.produto_id
      WHERE s.ativo = true AND s.exumado_em IS NULL
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) >= CURRENT_DATE
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) <= CURRENT_DATE + INTERVAL '30 days'
      ORDER BY data_exumacao ASC
    `;

    const result = await query(exumacoes30Query, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar exumações dos próximos 30 dias:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar todas as exumações separadas por produto
router.get('/todas-exumacoes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const todasExumacoesQuery = `
      SELECT
        s.id,
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        p.codigo_estacao,
        b.denominacao as denominacao_bloco,
        g.numero_gaveta,
        s.data_sepultamento,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao,
        EXTRACT(DAYS FROM (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) - CURRENT_DATE) as dias_restantes,
        CASE WHEN s.exumado_em IS NOT NULL THEN 'Exumado' ELSE 'Pendente' END as status_exumacao
      FROM sepultamentos s
      JOIN gavetas g ON g.id = s.gaveta_id
      JOIN sub_blocos sb ON sb.id = g.sub_bloco_id
      JOIN blocos b ON b.id = sb.bloco_id
      JOIN produtos p ON p.id = b.produto_id
      WHERE s.ativo = true
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) >= CURRENT_DATE
      ORDER BY p.denominacao, data_exumacao ASC
    `;

    const result = await query(todasExumacoesQuery, params);

    // Agrupar por produto
    const exumacoesPorProduto = result.rows.reduce((acc, exumacao) => {
      const produto = exumacao.denominacao_produto;
      if (!acc[produto]) {
        acc[produto] = [];
      }
      acc[produto].push(exumacao);
      return acc;
    }, {});

    res.json(exumacoesPorProduto);

  } catch (error) {
    console.error('Erro ao buscar todas as exumações:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar atividades recentes
router.get('/atividades-recentes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;

    let whereClause = '';
    let params = [10]; // Limit

    if (userType === 'cliente') {
      whereClause = `
        AND (
          l.tabela_afetada = 'sepultamentos' 
          OR (l.tabela_afetada = 'produtos' AND l.dados_novos->>'codigo_cliente' = (SELECT codigo_cliente FROM usuarios WHERE id = $2))
        )
      `;
      params.push(userId);
    }

    const atividadesQuery = `
      SELECT 
        l.id,
        l.acao,
        l.tabela_afetada,
        l.created_at,
        u.nome as usuario_nome,
        l.dados_novos->>'descricao_atividade' as descricao
      FROM logs_auditoria l
      JOIN usuarios u ON u.id = l.usuario_id
      WHERE l.dados_novos->>'descricao_atividade' IS NOT NULL
      ${whereClause}
      ORDER BY l.created_at DESC
      LIMIT $1
    `;

    const result = await query(atividadesQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar atividades recentes:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de sepultamentos
router.get('/sepultamentos-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [cliente_id];
    }

    const detailsQuery = `
      SELECT 
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(s.id) as total_sepultamentos,
        MIN(s.data_sepultamento) as primeiro_sepultamento,
        MAX(s.data_sepultamento) as ultimo_sepultamento
      FROM produtos p
      LEFT JOIN sepultamentos s ON (
        p.codigo_cliente = s.codigo_cliente AND
        p.codigo_estacao = s.codigo_estacao AND
        s.ativo = true AND s.status_exumacao = false
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao, p.codigo_estacao
      ORDER BY total_sepultamentos DESC
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes de sepultamentos:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de gavetas ocupadas
router.get('/gavetas-ocupadas-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    }

    const detailsQuery = `
      SELECT 
        p.denominacao as produto,
        b.nome as bloco,
        COUNT(DISTINCT g.id) as total_gavetas,
        COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN g.id END) as gavetas_ocupadas,
        ROUND(
          (COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN g.id END)::DECIMAL / 
           NULLIF(COUNT(DISTINCT g.id), 0)) * 100, 2
        ) as taxa_ocupacao
      FROM produtos p
      LEFT JOIN gavetas g ON (
        p.codigo_cliente = g.codigo_cliente AND
        p.codigo_estacao = g.codigo_estacao
      )
      LEFT JOIN sepultamentos s ON (
        g.codigo_cliente = s.codigo_cliente AND
        g.codigo_estacao = s.codigo_estacao AND
        g.codigo_bloco = s.codigo_bloco AND
        g.codigo_sub_bloco = s.codigo_sub_bloco AND
        g.numero_gaveta = s.numero_gaveta AND
        s.ativo = true AND s.exumado_em IS NULL
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao
      HAVING COUNT(DISTINCT g.id) > 0
      ORDER BY taxa_ocupacao DESC
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes de gavetas ocupadas:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de gavetas disponíveis
router.get('/gavetas-disponiveis-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    }

    const detailsQuery = `
      SELECT 
        p.denominacao as produto,
        b.nome as bloco,
        COUNT(DISTINCT g.id) as total_gavetas,
        COUNT(DISTINCT CASE WHEN s.id IS NULL THEN g.id END) as gavetas_disponiveis,
        ROUND(
          (COUNT(DISTINCT CASE WHEN s.id IS NULL THEN g.id END)::DECIMAL / 
           NULLIF(COUNT(DISTINCT g.id), 0)) * 100, 2
        ) as taxa_disponibilidade
      FROM produtos p
      LEFT JOIN gavetas g ON (
        p.codigo_cliente = g.codigo_cliente AND
        p.codigo_estacao = g.codigo_estacao
      )
      LEFT JOIN sepultamentos s ON (
        g.codigo_cliente = s.codigo_cliente AND
        g.codigo_estacao = s.codigo_estacao AND
        g.codigo_bloco = s.codigo_bloco AND
        g.codigo_sub_bloco = s.codigo_sub_bloco AND
        g.numero_gaveta = s.numero_gaveta AND
        s.ativo = true AND s.exumado_em IS NULL
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao
      HAVING COUNT(DISTINCT g.id) > 0
      ORDER BY taxa_disponibilidade DESC
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes de gavetas disponíveis:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de exumações
router.get('/exumacoes-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    }

    const detailsQuery = `
      SELECT 
        p.denominacao as produto,
        COUNT(s.id) as total_exumacoes,
        MIN(s.data_sepultamento) as primeira_exumacao,
        MAX(s.data_sepultamento) as ultima_exumacao
      FROM produtos p
      LEFT JOIN sepultamentos s ON (
        p.codigo_cliente = s.codigo_cliente AND
        p.codigo_estacao = s.codigo_estacao AND
        s.status_exumacao = true
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao
      HAVING COUNT(s.id) > 0
      ORDER BY total_exumacoes DESC
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes de exumações:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para detalhes de gavetas por produto (nova)
router.get('/gavetas-por-produto-details', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'WHERE p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [cliente_id];
    }

    const detailsQuery = `
      SELECT
        p.denominacao as produto,
        p.codigo_estacao,
        COUNT(DISTINCT g.id) as total_gavetas,
        COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN g.id END) as gavetas_ocupadas,
        COUNT(DISTINCT CASE WHEN s.id IS NULL THEN g.id END) as gavetas_disponiveis,
        ROUND(
          (COUNT(DISTINCT CASE WHEN s.id IS NOT NULL THEN g.id END)::DECIMAL /
           NULLIF(COUNT(DISTINCT g.id), 0)) * 100, 2
        ) as taxa_ocupacao
      FROM produtos p
      LEFT JOIN gavetas g ON (
        p.codigo_cliente = g.codigo_cliente AND
        p.codigo_estacao = g.codigo_estacao
      )
      LEFT JOIN sepultamentos s ON (
        g.codigo_cliente = s.codigo_cliente AND
        g.codigo_estacao = s.codigo_estacao AND
        g.codigo_bloco = s.codigo_bloco AND
        g.codigo_sub_bloco = s.codigo_sub_bloco AND
        g.numero_gaveta = s.numero_gaveta AND
        s.ativo = true AND s.exumado_em IS NULL
      )
      ${whereClause}
      GROUP BY p.id, p.denominacao, p.codigo_estacao
      HAVING COUNT(DISTINCT g.id) > 0
      ORDER BY p.denominacao
    `;

    const result = await query(detailsQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes de gavetas por produto:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar exumações previstas para próximos 30 dias (total)
router.get('/exumacoes-previstas-30-dias', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND s.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND s.codigo_cliente = $1';
      params = [cliente_id];
    }

    const countQuery = `
      SELECT COUNT(*) as total
      FROM sepultamentos s
      WHERE s.ativo = true AND s.status_exumacao = false
      ${whereClause}
      AND s.data_prevista_exumacao BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
    `;

    const result = await query(countQuery, params);
    res.json({ total: parseInt(result.rows[0].total) });

  } catch (error) {
    console.error('Erro ao buscar exumações previstas 30 dias:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar todas as exumações dos próximos 30 dias (Ver Mais)
router.get('/todas-exumacoes-30-dias', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const exumacoesQuery = `
      SELECT
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        b.nome as denominacao_bloco,
        g.numero_gaveta,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao
      FROM sepultamentos s
      JOIN gavetas g ON g.id = s.gaveta_id
      JOIN sub_blocos sb ON sb.id = g.sub_bloco_id
      JOIN blocos b ON b.id = sb.bloco_id
      JOIN produtos p ON p.id = b.produto_id
      WHERE s.ativo = true AND s.exumado_em IS NULL
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days'
      ORDER BY data_exumacao ASC
    `;

    const result = await query(exumacoesQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar todas as exumações 30 dias:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Rota para buscar detalhes de exumações previstas (próximos e últimos 30 dias)
router.get('/exumacoes-previstas-detalhes', async (req, res) => {
  try {
    const userId = req.user.id;
    const userType = req.user.tipo_usuario;
    const { cliente_id } = req.query;

    let whereClause = '';
    let params = [];

    if (userType === 'cliente') {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM usuarios WHERE id = $1)';
      params = [userId];
    } else if (userType === 'admin' && cliente_id) {
      whereClause = 'AND p.codigo_cliente = (SELECT codigo_cliente FROM clientes WHERE id = $1)';
      params = [parseInt(cliente_id)];
    }

    const detalhesQuery = `
      SELECT
        s.id as sepultamento_id,
        s.nome_sepultado,
        p.denominacao as denominacao_produto,
        b.nome as denominacao_bloco,
        g.numero_gaveta,
        (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) as data_exumacao
      FROM sepultamentos s
      JOIN gavetas g ON g.id = s.gaveta_id
      JOIN sub_blocos sb ON sb.id = g.sub_bloco_id
      JOIN blocos b ON b.id = sb.bloco_id
      JOIN produtos p ON p.id = b.produto_id
      WHERE s.ativo = true AND s.exumado_em IS NULL
      ${whereClause}
      AND (s.data_sepultamento + INTERVAL '1 month' * p.meses_para_exumar) BETWEEN CURRENT_DATE - INTERVAL '30 days' AND CURRENT_DATE + INTERVAL '30 days'
      ORDER BY data_exumacao ASC
    `;

    const result = await query(detalhesQuery, params);
    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao buscar detalhes de exumações previstas:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

module.exports = router;
