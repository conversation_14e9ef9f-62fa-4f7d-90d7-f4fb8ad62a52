const express = require('express');
const { query } = require('../database/connection');
const { logAction } = require('../utils/logger');

// Função utilitária para sincronizar status das gavetas
async function sincronizarStatusGaveta(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta) {
  try {
    console.log('🔄 Sincronizando status da gaveta...');

    // Verificar se há sepultamento ativo e não exumado
    const sepultamentoAtivo = await query(`
      SELECT COUNT(*) as count FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
        AND codigo_sub_bloco = $4 AND numero_gaveta = $5
        AND ativo = true AND status_exumacao = false
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    const temSepultamentoAtivo = parseInt(sepultamentoAtivo.rows[0].count) > 0;
    const disponivel = !temSepultamentoAtivo;

    // Atualizar status da gaveta
    await query(`
      UPDATE gavetas
      SET disponivel = $1, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $2 AND codigo_estacao = $3 AND codigo_bloco = $4 AND codigo_sub_bloco = $5 AND numero_gaveta = $6
    `, [disponivel, codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    console.log(`✅ Gaveta sincronizada: disponível = ${disponivel}`);
    return disponivel;
  } catch (error) {
    console.error('❌ Erro ao sincronizar status da gaveta:', error);
    throw error;
  }
}

const router = express.Router();

// MIDDLEWARE DE DEBUG PARA TODAS AS ROTAS
router.use((req, res, next) => {
  console.log(`🔍 SEPULTAMENTOS ROUTE: ${req.method} ${req.path} - Params: ${JSON.stringify(req.params)} - Query: ${JSON.stringify(req.query)}`);
  next();
});

// ROTA SIMPLES DE TESTE
router.get('/teste-simples', (req, res) => {
  console.log('🚨 ROTA TESTE SIMPLES CHAMADA');
  res.json({
    message: 'Rota teste funcionando!',
    timestamp: new Date().toISOString()
  });
});

// ROTA DE TESTE PARA ESTAÇÃO
router.get('/test-estacao/:codigo_estacao', async (req, res) => {
  console.log(`🚨 ROTA DE TESTE CHAMADA: ${req.params.codigo_estacao}`);
  res.json({
    message: `Teste funcionando para estação ${req.params.codigo_estacao}`,
    timestamp: new Date().toISOString(),
    user: req.user ? req.user.email : 'não autenticado'
  });
});

// NOVA ROTA PARA ESTAÇÃO - REFATORADA
router.get('/por-estacao/:codigo_estacao', async (req, res) => {
  console.log(`🔥 NOVA ROTA ESTAÇÃO: ${req.params.codigo_estacao}`);

  try {
    const { codigo_estacao } = req.params;

    // Consulta direta sem filtros complexos para teste
    const result = await query(`
      SELECT
        id, nome_sepultado, data_sepultamento, codigo_estacao, codigo_bloco, numero_gaveta
      FROM sepultamentos
      WHERE ativo = true
        AND status_exumacao = false
        AND codigo_estacao = $1
      ORDER BY data_sepultamento DESC
      LIMIT 10
    `, [codigo_estacao]);

    console.log(`🔥 NOVA ROTA - Encontrados: ${result.rows.length} registros`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows,
      estacao: codigo_estacao
    });

  } catch (error) {
    console.error('🔥 NOVA ROTA - Erro:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      estacao: req.params.codigo_estacao
    });
  }
});

// Listar sepultamentos
router.get('/', async (req, res) => {
  try {
    const { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, estacao_only, status_exumacao, ativo, data_inicio, data_fim, incluir_todos_ate_data_fim } = req.query;
    console.log(`🔍 GET /sepultamentos - Params:`, { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, estacao_only, status_exumacao, ativo, data_inicio, data_fim, incluir_todos_ate_data_fim });
    
    let whereClause = 'WHERE 1=1';
    let params = [];
    let paramCount = 0;

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
    } else if (codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(codigo_cliente);
    }

    if (codigo_estacao) {
      paramCount++;
      whereClause += ` AND s.codigo_estacao = $${paramCount}`;
      params.push(codigo_estacao);
    }

    if (codigo_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_bloco = $${paramCount}`;
      params.push(codigo_bloco);
    }

    if (codigo_sub_bloco) {
      paramCount++;
      whereClause += ` AND s.codigo_sub_bloco = $${paramCount}`;
      params.push(codigo_sub_bloco);
    }

    if (numero_gaveta) {
      paramCount++;
      whereClause += ` AND s.numero_gaveta = $${paramCount}`;
      params.push(numero_gaveta);
    }

    // Filtro por status de exumação
    if (status_exumacao !== undefined) {
      whereClause += ` AND s.status_exumacao = ${status_exumacao === 'true'}`;
      console.log(`🔍 Filtro status_exumacao aplicado: ${status_exumacao}`);
    }

    // Filtro por ativo (padrão é true se não especificado)
    if (ativo !== undefined) {
      whereClause += ` AND s.ativo = ${ativo === 'true'}`;
      console.log(`🔍 Filtro ativo aplicado: ${ativo}`);
    } else {
      whereClause += ` AND s.ativo = true`;
      console.log(`🔍 Filtro ativo padrão aplicado: true`);
    }

    // Filtros de data para relatórios
    if (incluir_todos_ate_data_fim === 'true') {
      // Para cálculo de ocupação: incluir todos até a data fim (sem filtro de início)
      if (data_fim) {
        paramCount++;
        whereClause += ` AND s.data_sepultamento <= $${paramCount}`;
        params.push(data_fim);
        console.log(`🔍 Filtro incluir_todos_ate_data_fim aplicado até: ${data_fim}`);
      }
    } else {
      // Para relatórios normais: filtro de período completo
      if (data_inicio) {
        paramCount++;
        whereClause += ` AND s.data_sepultamento >= $${paramCount}`;
        params.push(data_inicio);
        console.log(`🔍 Filtro data_inicio aplicado: ${data_inicio}`);
      }

      if (data_fim) {
        paramCount++;
        whereClause += ` AND s.data_sepultamento <= $${paramCount}`;
        params.push(data_fim);
        console.log(`🔍 Filtro data_fim aplicado: ${data_fim}`);
      }
    }

    // Se for filtro apenas por estação (para funcionalidade do produto)
    if (estacao_only === 'true' && codigo_estacao) {
      whereClause += ' AND s.status_exumacao = false';
      console.log(`🔍 Filtro por estação aplicado: ${codigo_estacao}`);

      // Consulta simplificada para estação
      const result = await query(`
        SELECT
          s.id,
          s.nome_sepultado,
          s.data_sepultamento,
          s.horario_sepultamento,
          s.status_exumacao,
          s.codigo_cliente,
          s.codigo_estacao,
          s.codigo_bloco,
          s.codigo_sub_bloco,
          s.numero_gaveta
        FROM sepultamentos s
        ${whereClause}
        ORDER BY s.data_sepultamento DESC
        LIMIT 50
      `, params);

      console.log(`✅ Encontrados ${result.rows.length} sepultamentos para estação ${codigo_estacao}`);
      return res.json(result.rows);
    }

    const result = await query(`
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        b.denominacao as denominacao_bloco,
        sb.denominacao as sub_bloco_denominacao,
        c.nome_fantasia as cliente_nome
      FROM sepultamentos s
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.horario_sepultamento DESC
    `, params);

    res.json(result.rows);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Listar sepultamentos por produto
router.get('/produto/:produtoId', async (req, res) => {
  try {
    const { produtoId } = req.params;

    // Se não for admin, filtrar por código do cliente
    let whereClause = 'WHERE p.id = $1 AND s.ativo = true';
    let params = [produtoId];

    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        p.codigo_estacao,
        b.denominacao as denominacao_bloco,
        b.codigo_bloco,
        sb.denominacao as sub_bloco_denominacao,
        sb.codigo_sub_bloco
      FROM sepultamentos s
      JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      JOIN produtos p ON b.codigo_cliente = p.codigo_cliente AND b.codigo_estacao = p.codigo_estacao AND b.produto_id = p.id
      JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.nome_sepultado ASC
    `, params);

    res.json(result.rows);

  } catch (error) {
    console.error('Erro ao listar sepultamentos por produto:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// ROTA ESPECÍFICA PARA ESTAÇÃO - FUNCIONA INDEPENDENTE DO CACHE
router.get('/estacao/:codigo_estacao', async (req, res) => {
  console.log(`🔍 ROTA ESTAÇÃO CHAMADA: ${req.params.codigo_estacao}`);

  try {
    const { codigo_estacao } = req.params;
    console.log(`🔍 Buscando sepultamentos para estação: ${codigo_estacao}`);
    console.log(`👤 Usuário: ${req.user ? req.user.email : 'não autenticado'}`);

    let whereClause = 'WHERE s.ativo = true AND s.status_exumacao = false AND s.codigo_estacao = $1';
    let params = [codigo_estacao];
    let paramCount = 1;

    // Se não for admin, filtrar por código do cliente
    if (req.user && req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      paramCount++;
      whereClause += ` AND s.codigo_cliente = $${paramCount}`;
      params.push(req.user.codigo_cliente);
      console.log(`🔒 Filtro de cliente aplicado: ${req.user.codigo_cliente}`);
    }

    console.log(`📊 Executando consulta com whereClause: ${whereClause}`);
    console.log(`📊 Parâmetros: ${JSON.stringify(params)}`);

    // Consulta com JOIN para incluir dados do bloco
    const result = await query(`
      SELECT
        s.id,
        s.nome_sepultado,
        s.data_sepultamento,
        s.horario_sepultamento,
        s.status_exumacao,
        s.data_prevista_exumacao,
        s.exumado_em,
        s.horario_exumacao,
        s.observacoes_exumacao,
        s.codigo_cliente,
        s.codigo_estacao,
        s.codigo_bloco,
        s.codigo_sub_bloco,
        s.numero_gaveta,
        s.observacoes,
        b.denominacao as denominacao_bloco,
        b.nome as bloco_nome,
        p.denominacao as produto_denominacao
      FROM sepultamentos s
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente
        AND s.codigo_estacao = b.codigo_estacao
        AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente
        AND s.codigo_estacao = p.codigo_estacao
      ${whereClause}
      ORDER BY s.data_sepultamento DESC, s.horario_sepultamento DESC
      LIMIT 50
    `, params);

    console.log(`✅ Encontrados ${result.rows.length} sepultamentos para estação ${codigo_estacao}`);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ Erro ao listar sepultamentos por estação:', error);
    res.status(500).json({ error: 'Erro interno do servidor', details: error.message });
  }
});

// Buscar sepultamento por ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    let whereClause = 'WHERE s.id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND s.codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const result = await query(`
      SELECT
        s.*,
        p.denominacao as produto_denominacao,
        b.denominacao as denominacao_bloco,
        sb.denominacao as sub_bloco_denominacao,
        c.nome_fantasia as cliente_nome
      FROM sepultamentos s
      LEFT JOIN produtos p ON s.codigo_cliente = p.codigo_cliente AND s.codigo_estacao = p.codigo_estacao
      LEFT JOIN blocos b ON s.codigo_cliente = b.codigo_cliente AND s.codigo_estacao = b.codigo_estacao AND s.codigo_bloco = b.codigo_bloco
      LEFT JOIN sub_blocos sb ON s.codigo_cliente = sb.codigo_cliente AND s.codigo_estacao = sb.codigo_estacao AND s.codigo_bloco = sb.codigo_bloco AND s.codigo_sub_bloco = sb.codigo_sub_bloco
      LEFT JOIN clientes c ON s.codigo_cliente = c.codigo_cliente
      ${whereClause}
    `, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo sepultamento
router.post('/', async (req, res) => {
  try {
    console.log('🔄 CRIAR SEPULTAMENTO - Dados recebidos:', req.body);
    console.log('🔄 CRIAR SEPULTAMENTO - Usuário:', req.user?.email, req.user?.tipo_usuario);

    const {
      codigo_cliente,
      codigo_estacao,
      codigo_bloco,
      codigo_sub_bloco,
      numero_gaveta,
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      observacoes
    } = req.body;

    console.log('🔍 Validando campos obrigatórios...');
    if (!codigo_cliente || !codigo_estacao || !codigo_bloco || !codigo_sub_bloco || !numero_gaveta || !nome_sepultado || !data_sepultamento || !horario_sepultamento) {
      console.log('❌ Campos obrigatórios faltando:', {
        codigo_cliente: !!codigo_cliente,
        codigo_estacao: !!codigo_estacao,
        codigo_bloco: !!codigo_bloco,
        codigo_sub_bloco: !!codigo_sub_bloco,
        numero_gaveta: !!numero_gaveta,
        nome_sepultado: !!nome_sepultado,
        data_sepultamento: !!data_sepultamento,
        horario_sepultamento: !!horario_sepultamento
      });
      return res.status(400).json({ error: 'Todos os campos obrigatórios devem ser preenchidos' });
    }

    // Validações de formato
    console.log('🔍 Validando formatos...');

    // Validar formato da data (YYYY-MM-DD)
    const dataRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dataRegex.test(data_sepultamento)) {
      console.log('❌ Formato de data inválido:', data_sepultamento);
      return res.status(400).json({ error: 'Formato de data inválido. Use YYYY-MM-DD' });
    }

    // Validar formato do horário (HH:MM)
    const horarioRegex = /^\d{2}:\d{2}$/;
    if (!horarioRegex.test(horario_sepultamento)) {
      console.log('❌ Formato de horário inválido:', horario_sepultamento);
      return res.status(400).json({ error: 'Formato de horário inválido. Use HH:MM' });
    }

    // Validar se a data não é futura
    const dataAtual = new Date();
    const dataSepultamento = new Date(data_sepultamento);
    if (dataSepultamento > dataAtual) {
      console.log('❌ Data de sepultamento no futuro:', data_sepultamento);
      return res.status(400).json({ error: 'Data de sepultamento não pode ser no futuro' });
    }

    // Validar número da gaveta
    if (isNaN(numero_gaveta) || numero_gaveta <= 0) {
      console.log('❌ Número de gaveta inválido:', numero_gaveta);
      return res.status(400).json({ error: 'Número da gaveta deve ser um número positivo' });
    }

    // Se não for admin, verificar se pertence ao cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      if (req.user.codigo_cliente !== codigo_cliente) {
        return res.status(403).json({ error: 'Acesso negado' });
      }
    }

    console.log('🔍 Verificando se gaveta existe...');
    // Verificar se a gaveta existe
    const gaveta = await query(`
      SELECT g.id, g.disponivel, g.ativo FROM gavetas g
      WHERE g.codigo_cliente = $1 AND g.codigo_estacao = $2 AND g.codigo_bloco = $3 AND g.codigo_sub_bloco = $4 AND g.numero_gaveta = $5
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    console.log('🔍 Gaveta encontrada:', gaveta.rows.length > 0, gaveta.rows[0]);
    if (gaveta.rows.length === 0) {
      console.log('❌ Gaveta não encontrada para:', { codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta });

      // Tentar criar a gaveta automaticamente se não existir
      console.log('🔧 Tentando criar gaveta automaticamente...');
      try {
        await query(`
          INSERT INTO gavetas (codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta, disponivel, ativo)
          VALUES ($1, $2, $3, $4, $5, true, true)
        `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);
        console.log('✅ Gaveta criada automaticamente');
      } catch (createError) {
        console.log('❌ Erro ao criar gaveta:', createError.message);
        return res.status(404).json({ error: 'Gaveta não encontrada e não foi possível criar automaticamente' });
      }
    }

    console.log('🔍 Verificando sepultamento existente...');
    // Verificar se já existe sepultamento ativo e não exumado nesta gaveta
    const sepultamentoExistente = await query(`
      SELECT id, nome_sepultado, data_sepultamento, exumado_em FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND numero_gaveta = $5
        AND ativo = true AND exumado_em IS NULL
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    console.log('🔍 Sepultamento ativo existente:', sepultamentoExistente.rows.length > 0);
    if (sepultamentoExistente.rows.length > 0) {
      const ocupante = sepultamentoExistente.rows[0];
      console.log('❌ Gaveta já possui sepultamento ativo:', ocupante);
      return res.status(400).json({
        error: `Gaveta ${numero_gaveta} já está ocupada por ${ocupante.nome_sepultado} (sepultado em ${ocupante.data_sepultamento})`
      });
    }

    // Verificar se há registros anteriores exumados (para log)
    const registrosAnteriores = await query(`
      SELECT id, nome_sepultado, data_sepultamento, exumado_em
      FROM sepultamentos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3 AND codigo_sub_bloco = $4 AND numero_gaveta = $5
        AND (ativo = false OR exumado_em IS NOT NULL)
      ORDER BY data_sepultamento DESC
    `, [codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta]);

    if (registrosAnteriores.rows.length > 0) {
      console.log(`📋 Gaveta ${numero_gaveta} teve ${registrosAnteriores.rows.length} sepultamento(s) anterior(es) já exumado(s)`);
      registrosAnteriores.rows.forEach((reg, index) => {
        console.log(`   ${index + 1}. ${reg.nome_sepultado} - Sepultado: ${reg.data_sepultamento} - Exumado: ${reg.data_exumacao || 'N/A'}`);
      });
    }

    // Criar sepultamento
    console.log('💾 Inserindo sepultamento na base de dados...');
    console.log('📝 Parâmetros:', {
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta,
      nome_sepultado, data_sepultamento, horario_sepultamento, observacoes: observacoes || ''
    });

    // Calcular data prevista de exumação (36 meses após sepultamento)
    const dataPrevistaExumacao = new Date(data_sepultamento);
    dataPrevistaExumacao.setMonth(dataPrevistaExumacao.getMonth() + 36);

    const result = await query(`
      INSERT INTO sepultamentos (
        codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta,
        nome_sepultado, data_sepultamento, horario_sepultamento, observacoes, data_prevista_exumacao, ativo
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta,
      nome_sepultado, data_sepultamento, horario_sepultamento, observacoes || '', dataPrevistaExumacao, true
    ]);

    console.log('✅ Sepultamento inserido com sucesso:', result.rows[0]?.id);

    // Sincronizar status da gaveta
    await sincronizarStatusGaveta(codigo_cliente, codigo_estacao, codigo_bloco, codigo_sub_bloco, numero_gaveta);

    const novoSepultamento = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'CREATE',
      'sepultamentos',
      novoSepultamento.id,
      null,
      novoSepultamento,
      req.ip,
      req.get('User-Agent')
    );

    res.status(201).json({
      message: 'Sepultamento cadastrado com sucesso',
      sepultamento: novoSepultamento
    });

  } catch (error) {
    console.error('❌ ERRO ao criar sepultamento:', error);
    console.error('❌ Stack trace:', error.stack);
    console.error('❌ Dados recebidos:', req.body);

    // Tratar erro específico de constraint única
    if (error.code === '23505' && error.constraint && error.constraint.includes('gaveta')) {
      return res.status(400).json({
        error: 'Esta gaveta já possui um sepultamento ativo. Verifique se o sepultamento anterior foi exumado corretamente.',
        details: 'Constraint de gaveta única violada'
      });
    }

    // Tratar outros erros de constraint
    if (error.code === '23505') {
      return res.status(400).json({
        error: 'Dados duplicados detectados. Verifique se este sepultamento já foi cadastrado.',
        details: error.detail || 'Constraint única violada'
      });
    }

    // Tratar erro de foreign key
    if (error.code === '23503') {
      return res.status(400).json({
        error: 'Dados de referência inválidos. Verifique se o cliente, estação, bloco ou sub-bloco existem.',
        details: error.detail || 'Referência não encontrada'
      });
    }

    res.status(500).json({
      error: 'Erro interno do servidor',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Erro interno',
      details: error.message
    });
  }
});

// Atualizar sepultamento
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      nome_sepultado,
      data_sepultamento,
      horario_sepultamento,
      observacoes,
      ativo
    } = req.body;

    // Buscar dados anteriores
    let whereClause = 'WHERE id = $1';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
    }

    const dadosAnteriores = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (dadosAnteriores.rows.length === 0) {
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const result = await query(`
      UPDATE sepultamentos
      SET nome_sepultado = $1, data_sepultamento = $2, horario_sepultamento = $3, observacoes = $4, ativo = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [
      nome_sepultado || dadosAnteriores.rows[0].nome_sepultado,
      data_sepultamento || dadosAnteriores.rows[0].data_sepultamento,
      horario_sepultamento || dadosAnteriores.rows[0].horario_sepultamento,
      observacoes !== undefined ? observacoes : dadosAnteriores.rows[0].observacoes,
      ativo !== undefined ? ativo : dadosAnteriores.rows[0].ativo,
      id
    ]);

    const sepultamentoAtualizado = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'EDIT',
      'sepultamentos',
      id,
      dadosAnteriores.rows[0],
      sepultamentoAtualizado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Sepultamento atualizado com sucesso',
      sepultamento: sepultamentoAtualizado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Exumar sepultamento (admin e cliente)
router.post('/:id/exumar', async (req, res) => {
  try {
    const { id } = req.params;
    const { data_exumacao } = req.body;

    console.log(`🔄 EXUMAR sepultamento ID: ${id} - Usuário: ${req.user?.email} (${req.user?.tipo_usuario})`);
    console.log(`📅 Data da exumação:`, { data_exumacao });

    // Validar data de exumação
    if (!data_exumacao) {
      return res.status(400).json({ error: 'Data de exumação é obrigatória' });
    }

    // Buscar sepultamento com filtro por cliente se necessário
    let whereClause = 'WHERE id = $1 AND ativo = true';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
      console.log(`🔒 Filtro de cliente aplicado: ${req.user.codigo_cliente}`);
    }

    // Buscar sepultamento
    const sepultamento = await query(`SELECT * FROM sepultamentos ${whereClause}`, params);

    if (sepultamento.rows.length === 0) {
      console.log('❌ Sepultamento não encontrado');
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const dadosAnteriores = sepultamento.rows[0];
    console.log(`📋 Sepultamento encontrado: ${dadosAnteriores.nome_sepultado} - Já exumado: ${!!dadosAnteriores.exumado_em}`);

    // Verificar se já foi exumado
    if (dadosAnteriores.exumado_em) {
      console.log('❌ Sepultamento já foi exumado');
      return res.status(400).json({ error: 'Sepultamento já foi exumado' });
    }

    // Preparar dados da exumação
    const dataExumacaoFormatada = data_exumacao;

    console.log(`💾 Salvando exumação: ${dataExumacaoFormatada}`);

    // Verificar se existe bloco para este sepultamento e criar se necessário
    const blocoExiste = await query(`
      SELECT COUNT(*) as count FROM blocos
      WHERE codigo_cliente = $1 AND codigo_estacao = $2 AND codigo_bloco = $3
    `, [dadosAnteriores.codigo_cliente, dadosAnteriores.codigo_estacao, dadosAnteriores.codigo_bloco]);

    if (parseInt(blocoExiste.rows[0].count) === 0) {
      console.log(`🔧 Criando bloco faltante: ${dadosAnteriores.codigo_bloco}`);
      await query(`
        INSERT INTO blocos (codigo_cliente, codigo_estacao, codigo_bloco, denominacao, ativo)
        VALUES ($1, $2, $3, $4, true)
      `, [
        dadosAnteriores.codigo_cliente,
        dadosAnteriores.codigo_estacao,
        dadosAnteriores.codigo_bloco,
        `BLOCO TESTE` // Denominação padrão
      ]);
    }

    // Marcar como exumado mantendo ativo = true - LÓGICA CORRIGIDA
    const result = await query(`
      UPDATE sepultamentos
      SET
        status_exumacao = true,
        exumado_em = $2::date,
        observacoes = COALESCE(observacoes, '') || CASE WHEN observacoes IS NOT NULL THEN E'\n' ELSE '' END || $3,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id, dataExumacaoFormatada, `Exumação realizada em ${dataExumacaoFormatada} via sistema`]);

    // Sincronizar status da gaveta
    await sincronizarStatusGaveta(
      dadosAnteriores.codigo_cliente,
      dadosAnteriores.codigo_estacao,
      dadosAnteriores.codigo_bloco,
      dadosAnteriores.codigo_sub_bloco,
      dadosAnteriores.numero_gaveta
    );

    const sepultamentoExumado = result.rows[0];

    // Registrar log
    await logAction(
      req.user.id,
      'EXUMAR',
      'sepultamentos',
      id,
      dadosAnteriores,
      sepultamentoExumado,
      req.ip,
      req.get('User-Agent')
    );

    res.json({
      message: 'Exumação realizada com sucesso',
      sepultamento: sepultamentoExumado
    });

  } catch (error) {
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar sepultamento (apenas admin e apenas se exumado)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ DELETE sepultamento ID: ${id} - Usuário: ${req.user?.email} (${req.user?.tipo_usuario})`);

    // Buscar sepultamento primeiro para verificar permissões
    let whereClause = 'WHERE id = $1 AND ativo = true';
    let params = [id];

    // Se não for admin, filtrar por código do cliente
    if (req.user.tipo_usuario !== 'admin' && req.user.codigo_cliente) {
      whereClause += ' AND codigo_cliente = $2';
      params.push(req.user.codigo_cliente);
      console.log(`🔒 Filtro de cliente aplicado: ${req.user.codigo_cliente}`);
    }

    // Buscar sepultamento
    const sepultamento = await query(
      `SELECT * FROM sepultamentos ${whereClause}`,
      params
    );

    if (sepultamento.rows.length === 0) {
      console.log('❌ Sepultamento não encontrado');
      return res.status(404).json({ error: 'Sepultamento não encontrado' });
    }

    const sepultamentoData = sepultamento.rows[0];
    console.log(`📋 Sepultamento encontrado: ${sepultamentoData.nome_sepultado} - Exumado: ${!!sepultamentoData.status_exumacao}`);

    // Deletar sepultamento (soft delete - marcar como inativo)
    await query('UPDATE sepultamentos SET ativo = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1', [id]);

    // Liberar gaveta baseado nos códigos
    console.log(`🔓 Liberando gaveta: cliente=${sepultamentoData.codigo_cliente}, estacao=${sepultamentoData.codigo_estacao}, bloco=${sepultamentoData.codigo_bloco}, sub_bloco=${sepultamentoData.codigo_sub_bloco}, gaveta=${sepultamentoData.numero_gaveta}`);

    const gavetaUpdate = await query(`
      UPDATE gavetas
      SET disponivel = true, updated_at = CURRENT_TIMESTAMP
      WHERE codigo_cliente = $1
        AND codigo_estacao = $2
        AND codigo_bloco = $3
        AND codigo_sub_bloco = $4
        AND numero_gaveta = $5
    `, [
      sepultamentoData.codigo_cliente,
      sepultamentoData.codigo_estacao,
      sepultamentoData.codigo_bloco,
      sepultamentoData.codigo_sub_bloco,
      sepultamentoData.numero_gaveta
    ]);

    console.log(`✅ Gavetas liberadas: ${gavetaUpdate.rowCount}`);

    // Registrar log
    await logAction(
      req.user.id,
      'DELETE',
      'sepultamentos',
      id,
      sepultamentoData,
      { ...sepultamentoData, ativo: false },
      req.ip,
      req.get('User-Agent')
    );

    console.log(`✅ Sepultamento ${id} deletado com sucesso`);
    res.json({ message: 'Sepultamento deletado com sucesso' });

  } catch (error) {
    console.error('❌ Erro ao deletar sepultamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
