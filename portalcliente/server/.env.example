# Configurações do Portal Evolution

# JWT Secret
JWT_SECRET=portal-evolution-secret-key

# Configurações de Email
EMAIL_USER=<EMAIL>
EMAIL_PASS=sua_senha_de_app_aqui

# Configurações do Banco de Dados
DB_HOST=************
DB_PORT=5432
DB_NAME=dbetens
DB_USER=postgres
DB_PASS=ab3780bd73ee4e2804d566ce6fd96209

# Configurações do Servidor
PORT=3001
NODE_ENV=development

# INSTRUÇÕES PARA CONFIGURAR EMAIL:
# 
# 1. Crie uma conta Gmail ou use uma existente
# 2. Ative a verificação em duas etapas
# 3. Gere uma "Senha de App" específica para este sistema
# 4. Substitua EMAIL_USER pelo seu email
# 5. Substitua EMAIL_PASS pela senha de app gerada
# 6. Copie este arquivo para .env e configure suas credenciais
#
# IMPORTANTE: Nunca commite o arquivo .env com credenciais reais!
