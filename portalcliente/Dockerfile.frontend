# Dockerfile para Portal Cliente - Frontend React
FROM nginx:alpine

# Labels para identificação da imagem
LABEL maintainer="Portal Evolution Team"
LABEL description="Portal Evolution - Frontend React Application"
LABEL version="2.0"
LABEL component="frontend"

# Instalar curl para healthcheck
RUN apk add --no-cache curl

# Copiar arquivos de build para o Nginx
COPY dist/ /usr/share/nginx/html/

# Criar arquivo de configuração do Nginx
RUN cat > /etc/nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Configurações básicas
    sendfile on;
    keepalive_timeout 65;
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Configuração de logs
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Static assets with long cache
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
            try_files $uri =404;
        }

        # JavaScript files with correct MIME type
        location ~* \.js$ {
            expires 1y;
            add_header Content-Type "application/javascript; charset=utf-8";
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
            try_files $uri =404;
        }

        # CSS files
        location ~* \.css$ {
            expires 1y;
            add_header Content-Type "text/css; charset=utf-8";
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
            try_files $uri =404;
        }

        # HTML files (no cache)
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            try_files $uri =404;
        }

        # SPA fallback
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }
}
EOF

# Expor porta 80
EXPOSE 80

# Comando para iniciar o Nginx
CMD ["nginx", "-g", "daemon off;"]

