/**
 * Utilitário para formatação de mensagens de erro hi<PERSON>
 * Padroniza as mensagens de validação de dependências
 */

export const formatHierarchicalError = (errorData) => {
  if (!errorData || !errorData.dependencias) {
    return errorData?.error || 'Erro desconhecido';
  }

  let message = `❌ ${errorData.error}\n\n`;
  
  if (errorData.detalhes) {
    message += `📋 ${errorData.detalhes}\n\n`;
  }

  // Mostrar dependências encontradas
  if (errorData.dependencias) {
    message += `🔗 Dependências encontradas:\n`;
    
    Object.entries(errorData.dependencias).forEach(([tipo, quantidade]) => {
      if (quantidade > 0) {
        const emoji = getEmojiForType(tipo);
        message += `   ${emoji} ${quantidade} ${formatTypeName(tipo)}\n`;
      }
    });
    
    message += '\n';
  }

  // Mostrar ação necessária
  if (errorData.acao_necessaria) {
    message += `🔧 ${errorData.acao_necessaria}\n`;
  }

  // Mostrar passos obrigatórios
  if (errorData.passos_obrigatorios && errorData.passos_obrigatorios.length > 0) {
    message += '\n📝 Passos obrigatórios:\n';
    errorData.passos_obrigatorios.forEach((passo, index) => {
      message += `   ${index + 1}. ${passo}\n`;
    });
  }

  // Mostrar detalhes específicos
  if (errorData.blocos_nomes) {
    message += `\n📦 Blocos: ${errorData.blocos_nomes}`;
  }
  
  if (errorData.sub_blocos_nomes) {
    message += `\n📦 Sub-blocos: ${errorData.sub_blocos_nomes}`;
  }
  
  if (errorData.ranges_detalhes) {
    message += `\n📦 Ranges: ${errorData.ranges_detalhes}`;
  }
  
  if (errorData.gavetas_ocupadas) {
    message += `\n🔒 Gavetas ocupadas: ${errorData.gavetas_ocupadas}`;
  }
  
  if (errorData.sepultamentos_ativos) {
    message += `\n⚰️ Sepultamentos ativos: ${errorData.sepultamentos_ativos}`;
  }

  return message;
};

export const formatRangeError = (errorData) => {
  if (!errorData) {
    return 'Erro desconhecido';
  }

  let message = `❌ ${errorData.error}\n\n`;
  
  if (errorData.detalhes) {
    message += `📋 ${errorData.detalhes}\n\n`;
  }

  if (errorData.range_atual && errorData.range_novo) {
    message += `📊 Alteração solicitada:\n`;
    message += `   • Range atual: ${errorData.range_atual}\n`;
    message += `   • Range novo: ${errorData.range_novo}\n\n`;
  }

  if (errorData.gavetas_ocupadas) {
    message += `🔒 Gavetas ocupadas: ${errorData.gavetas_ocupadas}\n`;
  }

  if (errorData.sepultamentos_ativos) {
    message += `⚰️ Sepultamentos ativos:\n${errorData.sepultamentos_ativos}\n\n`;
  }

  if (errorData.acao_necessaria) {
    message += `🔧 ${errorData.acao_necessaria}`;
  }

  return message;
};

export const formatValidationError = (error) => {
  if (!error.response) {
    return 'Erro de conexão. Verifique sua internet e tente novamente.';
  }

  const status = error.response.status;
  const errorData = error.response.data;

  switch (status) {
    case 400:
      if (errorData.dependencias) {
        return formatHierarchicalError(errorData);
      } else if (errorData.gavetas_ocupadas || errorData.sepultamentos_ativos) {
        return formatRangeError(errorData);
      } else {
        return errorData.error || 'Dados inválidos para a operação';
      }
    
    case 401:
      return 'Sessão expirada. Faça login novamente.';
    
    case 403:
      return 'Acesso negado. Apenas administradores podem realizar esta ação.';
    
    case 404:
      return 'Item não encontrado. Pode ter sido removido por outro usuário.';
    
    case 409:
      return errorData.error || 'Conflito de dados. Verifique se não há duplicatas.';
    
    case 500:
      return 'Erro interno do servidor. Tente novamente em alguns minutos.';
    
    default:
      return errorData.error || `Erro ${status} na operação`;
  }
};

// Funções auxiliares
const getEmojiForType = (tipo) => {
  const emojiMap = {
    'blocos': '📦',
    'sub_blocos': '📋',
    'ranges': '📊',
    'gavetas': '🗃️',
    'gavetas_ocupadas': '🔒',
    'sepultamentos': '⚰️',
    'sepultamentos_ativos': '⚰️'
  };
  
  return emojiMap[tipo] || '📌';
};

const formatTypeName = (tipo) => {
  const nameMap = {
    'blocos': 'bloco(s)',
    'sub_blocos': 'sub-bloco(s)',
    'ranges': 'range(s) de gavetas',
    'gavetas': 'gaveta(s)',
    'gavetas_ocupadas': 'gaveta(s) ocupada(s)',
    'sepultamentos': 'sepultamento(s)',
    'sepultamentos_ativos': 'sepultamento(s) ativo(s)'
  };
  
  return nameMap[tipo] || tipo;
};

// Mensagens pré-definidas para casos comuns
export const HIERARCHICAL_MESSAGES = {
  PRODUTO_WITH_BLOCOS: {
    title: 'Produto possui blocos cadastrados',
    description: 'Para editar/deletar este produto, primeiro remova todos os blocos relacionados.',
    icon: '📦'
  },
  
  BLOCO_WITH_SUB_BLOCOS: {
    title: 'Bloco possui sub-blocos cadastrados',
    description: 'Para editar/deletar este bloco, primeiro remova todos os sub-blocos relacionados.',
    icon: '📋'
  },
  
  SUB_BLOCO_WITH_RANGES: {
    title: 'Sub-bloco possui ranges de gavetas',
    description: 'Para editar/deletar este sub-bloco, primeiro remova todos os ranges de gavetas.',
    icon: '📊'
  },
  
  RANGE_WITH_OCCUPIED_GAVETAS: {
    title: 'Range possui gavetas ocupadas',
    description: 'Para editar/deletar este range, primeiro exume todos os sepultamentos das gavetas ocupadas.',
    icon: '🔒'
  }
};

export default {
  formatHierarchicalError,
  formatRangeError,
  formatValidationError,
  HIERARCHICAL_MESSAGES
};
