/**
 * Console Security Manager
 * Oculta todas as mensagens do console para evitar exposição de informações sensíveis
 */

import { SECURITY_CONFIG, shouldApplyRestrictions } from '../config/security.js';

// Função para desabilitar completamente o console
const disableConsole = () => {
  // Salvar referências originais (caso necessário para debug interno)
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug,
    trace: console.trace,
    table: console.table,
    group: console.group,
    groupCollapsed: console.groupCollapsed,
    groupEnd: console.groupEnd,
    time: console.time,
    timeEnd: console.timeEnd,
    count: console.count,
    assert: console.assert,
    clear: console.clear,
    dir: console.dir,
    dirxml: console.dirxml
  };

  // Função vazia para substituir todos os métodos do console
  const emptyFunction = () => {};

  // Desabilitar todos os métodos do console
  console.log = emptyFunction;
  console.error = emptyFunction;
  console.warn = emptyFunction;
  console.info = emptyFunction;
  console.debug = emptyFunction;
  console.trace = emptyFunction;
  console.table = emptyFunction;
  console.group = emptyFunction;
  console.groupCollapsed = emptyFunction;
  console.groupEnd = emptyFunction;
  console.time = emptyFunction;
  console.timeEnd = emptyFunction;
  console.count = emptyFunction;
  console.assert = emptyFunction;
  console.clear = emptyFunction;
  console.dir = emptyFunction;
  console.dirxml = emptyFunction;

  // Também desabilitar window.console se existir
  if (typeof window !== 'undefined' && window.console) {
    window.console.log = emptyFunction;
    window.console.error = emptyFunction;
    window.console.warn = emptyFunction;
    window.console.info = emptyFunction;
    window.console.debug = emptyFunction;
    window.console.trace = emptyFunction;
    window.console.table = emptyFunction;
    window.console.group = emptyFunction;
    window.console.groupCollapsed = emptyFunction;
    window.console.groupEnd = emptyFunction;
    window.console.time = emptyFunction;
    window.console.timeEnd = emptyFunction;
    window.console.count = emptyFunction;
    window.console.assert = emptyFunction;
    window.console.clear = emptyFunction;
    window.console.dir = emptyFunction;
    window.console.dirxml = emptyFunction;
  }

  // Retornar as funções originais caso seja necessário restaurar (apenas para debug interno)
  return originalConsole;
};

// Função para bloquear tentativas de acesso ao console via DevTools
const blockDevToolsConsole = () => {
  // Detectar se DevTools está aberto e mostrar aviso
  let devtools = {
    open: false,
    orientation: null
  };

  const threshold = 160;

  setInterval(() => {
    if (window.outerHeight - window.innerHeight > threshold || 
        window.outerWidth - window.innerWidth > threshold) {
      if (!devtools.open) {
        devtools.open = true;
        // Limpar console quando DevTools for aberto
        if (console.clear) {
          console.clear();
        }
      }
    } else {
      devtools.open = false;
    }
  }, 500);

  // Bloquear atalhos de teclado para DevTools
  document.addEventListener('keydown', (e) => {
    // F12
    if (SECURITY_CONFIG.DISABLE_F12 && e.keyCode === 123) {
      e.preventDefault();
      return false;
    }
    // Ctrl+Shift+I
    if (SECURITY_CONFIG.DISABLE_CTRL_SHIFT_I && e.ctrlKey && e.shiftKey && e.keyCode === 73) {
      e.preventDefault();
      return false;
    }
    // Ctrl+Shift+C
    if (SECURITY_CONFIG.DISABLE_CTRL_SHIFT_C && e.ctrlKey && e.shiftKey && e.keyCode === 67) {
      e.preventDefault();
      return false;
    }
    // Ctrl+Shift+J
    if (SECURITY_CONFIG.DISABLE_CTRL_SHIFT_J && e.ctrlKey && e.shiftKey && e.keyCode === 74) {
      e.preventDefault();
      return false;
    }
    // Ctrl+U
    if (SECURITY_CONFIG.DISABLE_CTRL_U && e.ctrlKey && e.keyCode === 85) {
      e.preventDefault();
      return false;
    }
  });

  // Bloquear menu de contexto (botão direito)
  if (SECURITY_CONFIG.DISABLE_RIGHT_CLICK) {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      return false;
    });
  }

  // Bloquear seleção de texto
  if (SECURITY_CONFIG.DISABLE_TEXT_SELECTION) {
    document.addEventListener('selectstart', (e) => {
      e.preventDefault();
      return false;
    });
  }

  // Bloquear arrastar
  if (SECURITY_CONFIG.DISABLE_DRAG) {
    document.addEventListener('dragstart', (e) => {
      e.preventDefault();
      return false;
    });
  }
};

// Função principal para inicializar a segurança do console
const initConsoleSecurityy = () => {
  // Verificar se deve aplicar restrições
  if (!shouldApplyRestrictions()) {
    return;
  }

  // Desabilitar console se configurado
  if (SECURITY_CONFIG.DISABLE_CONSOLE) {
    disableConsole();
  }

  // Bloquear DevTools se configurado
  if (SECURITY_CONFIG.DISABLE_DEVTOOLS) {
    blockDevToolsConsole();
  }
};

export { initConsoleSecurityy, disableConsole, blockDevToolsConsole };
