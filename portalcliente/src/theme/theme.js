import { createTheme } from '@mui/material/styles';

// Paleta de cores padronizada seguindo Material Design e especificações do projeto
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2', // A<PERSON><PERSON> padr<PERSON> Material Design
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#10b981', // Verde Evolution
      light: '#34d399',
      dark: '#059669',
      contrastText: '#ffffff',
    },
    background: {
      default: '#f5f5f5', // Fundo padrão
      paper: '#ffffff',
      section: '#fafafa', // Fundo para seções
    },
    text: {
      primary: '#212121', // Texto principal
      secondary: '#757575', // Texto secundário
      disabled: '#bdbdbd',
    },
    grey: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#eeeeee',
      300: '#e0e0e0',
      400: '#bdbdbd',
      500: '#9e9e9e',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121',
    },
    error: {
      main: '#d32f2f',
      light: '#ef5350',
      dark: '#c62828',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#ed6c02',
      light: '#ff9800',
      dark: '#e65100',
      contrastText: '#ffffff',
    },
    success: {
      main: '#388e3c',
      light: '#4caf50',
      dark: '#2e7d32',
      contrastText: '#ffffff',
    },
    info: {
      main: '#0288d1',
      light: '#03a9f4',
      dark: '#01579b',
      contrastText: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    // Títulos padronizados conforme especificação
    h1: {
      fontSize: '2rem', // 32px - Título de página
      fontWeight: 700,
      lineHeight: '2.5rem', // 40px
      letterSpacing: '-0.01em',
      marginBottom: '1.5rem',
    },
    h2: {
      fontSize: '1.5rem', // 24px - Subtítulo
      fontWeight: 600,
      lineHeight: '2rem', // 32px
      marginBottom: '1rem',
    },
    h3: {
      fontSize: '1.25rem', // 20px - Seções
      fontWeight: 600,
      lineHeight: '1.75rem', // 28px
      marginBottom: '0.75rem',
    },
    h4: {
      fontSize: '1.125rem', // 18px - Subseções
      fontWeight: 600,
      lineHeight: '1.5rem', // 24px
      marginBottom: '0.5rem',
    },
    h5: {
      fontSize: '1rem', // 16px - Títulos menores
      fontWeight: 600,
      lineHeight: '1.5rem', // 24px
      marginBottom: '0.5rem',
    },
    h6: {
      fontSize: '0.875rem', // 14px - Legendas
      fontWeight: 600,
      lineHeight: '1.25rem', // 20px
      marginBottom: '0.25rem',
    },
    body1: {
      fontSize: '1rem', // 16px - Texto base
      fontWeight: 400,
      lineHeight: '1.5rem', // 24px
    },
    body2: {
      fontSize: '0.875rem', // 14px - Legendas
      fontWeight: 400,
      lineHeight: '1.25rem', // 20px
    },
    button: {
      fontSize: '1rem', // 16px - Botões
      fontWeight: 600,
      textTransform: 'uppercase',
      letterSpacing: '0.02em',
      lineHeight: '1.5rem', // 24px
    },
    caption: {
      fontSize: '0.75rem', // 12px - Textos muito pequenos
      fontWeight: 400,
      lineHeight: '1rem', // 16px
    },
    overline: {
      fontSize: '0.75rem', // 12px
      fontWeight: 600,
      letterSpacing: '0.5px',
      textTransform: 'uppercase',
      lineHeight: '1rem',
    },
  },
  shape: {
    borderRadius: 8, // Bordas arredondadas padrão
  },
  spacing: 8, // Espaçamento base de 8px

  // Breakpoints responsivos padronizados
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },

  // Sombras padronizadas
  shadows: [
    'none',
    '0px 2px 4px rgba(0, 0, 0, 0.05)', // elevation 1
    '0px 4px 6px rgba(0, 0, 0, 0.05)', // elevation 2
    '0px 6px 12px rgba(0, 0, 0, 0.1)', // elevation 3
    '0px 8px 16px rgba(0, 0, 0, 0.1)', // elevation 4
    '0px 12px 24px rgba(0, 0, 0, 0.15)', // elevation 5
    '0px 16px 32px rgba(0, 0, 0, 0.15)', // elevation 6
    '0px 20px 40px rgba(0, 0, 0, 0.2)', // elevation 7
    '0px 24px 48px rgba(0, 0, 0, 0.2)', // elevation 8
    // ... continua com as sombras padrão do MUI
    ...Array(16).fill(0).map((_, i) => `0px ${i + 1}px ${(i + 1) * 2}px rgba(0, 0, 0, 0.1)`),
  ],
  components: {
    // Botões padronizados
    MuiButton: {
      styleOverrides: {
        root: {
          height: '48px', // Altura padrão
          borderRadius: '8px',
          textTransform: 'uppercase',
          fontWeight: 600,
          fontSize: '1rem',
          padding: '12px 24px', // Padding horizontal padrão
          minWidth: '120px',
          boxShadow: 'none',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transform: 'translateY(-2px)',
          },
          '&:active': {
            transform: 'translateY(0px)',
          },
          '&:disabled': {
            opacity: 0.6,
            cursor: 'not-allowed',
            transform: 'none',
          },
        },
        contained: {
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            boxShadow: '0 6px 16px rgba(0, 0, 0, 0.15)',
          },
        },
        outlined: {
          borderWidth: '2px',
          '&:hover': {
            borderWidth: '2px',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          },
        },
        sizeSmall: {
          height: '36px',
          padding: '8px 16px',
          fontSize: '0.875rem',
          minWidth: '80px',
        },
        sizeLarge: {
          height: '56px',
          padding: '16px 32px',
          fontSize: '1.125rem',
          minWidth: '160px',
        },
      },
    },
    // Cards padronizados
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '16px', // Bordas arredondadas padrão
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          border: '1px solid transparent',
          backgroundColor: '#ffffff',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.1)',
            transform: 'translateY(-4px)',
          },
        },
      },
    },

    // Conteúdo dos cards
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: '24px', // Padding interno padrão
          '&:last-child': {
            paddingBottom: '24px',
          },
        },
      },
    },

    // Cabeçalho dos cards
    MuiCardHeader: {
      styleOverrides: {
        root: {
          padding: '24px 24px 16px 24px',
        },
        title: {
          fontSize: '1.25rem', // 20px
          fontWeight: 600,
          lineHeight: '1.75rem', // 28px
        },
        subheader: {
          fontSize: '0.875rem', // 14px
          fontWeight: 400,
          lineHeight: '1.25rem', // 20px
          marginTop: '4px',
        },
      },
    },

    // Ações dos cards
    MuiCardActions: {
      styleOverrides: {
        root: {
          padding: '16px 24px 24px 24px',
          justifyContent: 'flex-end',
        },
      },
    },
    // Campos de texto padronizados
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '8px',
            minHeight: '48px', // Altura mínima padrão
            '& input': {
              padding: '12px 14px',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#1976d2',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: '#1976d2',
              borderWidth: '2px',
            },
          },
          '& .MuiInputLabel-root': {
            fontSize: '1rem',
            fontWeight: 500,
          },
        },
      },
    },

    // Select/Dropdown padronizados
    MuiSelect: {
      styleOverrides: {
        root: {
          minHeight: '48px',
          '& .MuiSelect-select': {
            padding: '12px 14px',
            fontSize: '1rem',
          },
        },
      },
    },

    // FormControl padronizado
    MuiFormControl: {
      styleOverrides: {
        root: {
          marginBottom: '16px',
          '& .MuiInputLabel-root': {
            fontSize: '1rem',
            fontWeight: 500,
          },
        },
      },
    },
    // Tabelas padronizadas
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid #e0e0e0',
          padding: '16px',
          fontSize: '0.875rem',
        },
        head: {
          backgroundColor: '#f5f5f5',
          fontWeight: 600,
          color: '#212121',
          fontSize: '0.875rem',
          textTransform: 'uppercase',
          letterSpacing: '0.5px',
        },
      },
    },

    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:nth-of-type(even)': {
            backgroundColor: '#fafafa',
          },
          '&:hover': {
            backgroundColor: '#e3f2fd',
            cursor: 'pointer',
          },
        },
      },
    },

    // Container de tabelas
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          border: '1px solid #e0e0e0',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    // Chips padronizados
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '6px',
          fontWeight: 500,
          fontSize: '0.75rem',
          height: '28px',
        },
      },
    },

    // Paper/Container padronizado
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '12px',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
          backgroundColor: '#ffffff',
        },
        elevation1: {
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
        },
        elevation2: {
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
        },
        elevation3: {
          boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
        },
      },
    },

    // Container padronizado
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingTop: '24px',
          paddingBottom: '24px',
        },
      },
    },
    // AppBar padronizada
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#ffffff',
          color: '#212121',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          borderBottom: '1px solid #e0e0e0',
        },
      },
    },

    // Drawer/Sidebar padronizado
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1e3a8a', // Azul escuro para sidebar
          color: '#ffffff',
          borderRight: 'none',
          background: 'linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%)',
        },
      },
    },

    // Abas padronizadas
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: '48px',
          borderBottom: '1px solid #e0e0e0',
        },
        indicator: {
          height: '3px',
          borderRadius: '3px 3px 0 0',
        },
      },
    },

    MuiTab: {
      styleOverrides: {
        root: {
          minHeight: '48px',
          fontSize: '1rem',
          fontWeight: 600,
          textTransform: 'none',
          padding: '12px 16px',
          '&.Mui-selected': {
            fontWeight: 700,
          },
        },
      },
    },

    // Modais padronizados
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: '12px',
          padding: '8px',
        },
      },
    },

    MuiDialogTitle: {
      styleOverrides: {
        root: {
          fontSize: '1.5rem',
          fontWeight: 600,
          padding: '24px 24px 16px 24px',
        },
      },
    },

    MuiDialogContent: {
      styleOverrides: {
        root: {
          padding: '16px 24px',
        },
      },
    },

    MuiDialogActions: {
      styleOverrides: {
        root: {
          padding: '16px 24px 24px 24px',
          gap: '12px',
        },
      },
    },
  },
});

export default theme;
