import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Button } from '@mui/material';
import { Add as AddIcon, SwapHoriz as TransferIcon } from '@mui/icons-material';
import { produtoService, clienteService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import ProdutoModal from '../components/ProdutoModal';
import BlocoModal from '../components/BlocoModal';
import SubBlocoModal from '../components/SubBlocoModal';
import ProdutoDetalheModal from '../components/ProdutoDetalheModal';
import TransferenciaGavetasModal from '../components/TransferenciaGavetasModal';
import { StandardButton } from '../components/common';

const PageContainer = styled.div`
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const Title = styled.h1`
  color: #1f2937;
  margin: 0;
`;

const StyledButton = styled.button`
  background: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background: #2563eb;
  }

  &.secondary {
    background: #6b7280;
    &:hover {
      background: #4b5563;
    }
  }

  &.danger {
    background: #ef4444;
    &:hover {
      background: #dc2626;
    }
  }
`;

const TabContainer = styled.div`
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
`;

const TabList = styled.div`
  display: flex;
  gap: 0;
`;

const Tab = styled.button`
  background: none;
  border: none;
  padding: 12px 24px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.2s;

  &.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
  }

  &:hover {
    color: #3b82f6;
  }
`;

const Card = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'clickable',
})`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  cursor: ${props => props.clickable ? 'pointer' : 'default'};
  transition: all 0.2s;

  &:hover {
    ${props => props.clickable && `
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    `}
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const CardTitle = styled.h3`
  margin: 0;
  color: #1f2937;
`;

const CardActions = styled.div`
  display: flex;
  gap: 8px;
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const InfoLabel = styled.span`
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 4px;
`;

const InfoValue = styled.span`
  font-weight: 500;
  color: #1f2937;
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 48px;
  color: #6b7280;
`;

const GerenciamentoProdutosPage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('produtos');
  const [produtos, setProdutos] = useState([]);
  const [selectedProduto, setSelectedProduto] = useState(null);
  const [blocos, setBlocos] = useState([]);
  const [subBlocos, setSubBlocos] = useState([]);
  const [selectedBloco, setSelectedBloco] = useState(null);
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Modals
  const [showProdutoModal, setShowProdutoModal] = useState(false);
  const [showBlocoModal, setShowBlocoModal] = useState(false);
  const [showSubBlocoModal, setShowSubBlocoModal] = useState(false);
  const [showDetalheModal, setShowDetalheModal] = useState(false);
  const [showTransferenciaModal, setShowTransferenciaModal] = useState(false);
  const [editingProduto, setEditingProduto] = useState(null);
  const [editingBloco, setEditingBloco] = useState(null);
  const [editingSubBloco, setEditingSubBloco] = useState(null);
  const [selectedProdutoId, setSelectedProdutoId] = useState(null);

  useEffect(() => {
    loadProdutos();
    loadClientes();
  }, []);

  useEffect(() => {
    if (selectedProduto) {
      loadBlocos(selectedProduto.id);
    }
  }, [selectedProduto]);

  useEffect(() => {
    if (selectedBloco) {
      loadSubBlocos(selectedBloco.id);
    }
  }, [selectedBloco]);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      const response = await produtoService.listar();
      setProdutos(response.data);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    } finally {
      setLoading(false);
    }
  };

  const loadClientes = async () => {
    try {
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
    }
  };

  const loadBlocos = async (produtoId) => {
    try {
      const response = await produtoService.listarBlocos(produtoId);
      setBlocos(response.data);
    } catch (error) {
      console.error('Erro ao carregar blocos:', error);
    }
  };

  const loadSubBlocos = async (blocoId) => {
    try {
      const response = await produtoService.listarSubBlocos(blocoId);
      setSubBlocos(response.data);
    } catch (error) {
      console.error('Erro ao carregar sub-blocos:', error);
    }
  };

  const handleSelectProduto = (produto) => {
    setSelectedProduto(produto);
    setSelectedBloco(null);
    setSubBlocos([]);
    setActiveTab('blocos');
  };

  const handleSelectBloco = (bloco) => {
    setSelectedBloco(bloco);
    setActiveTab('sub-blocos');
  };

  const handleAddProduto = () => {
    setEditingProduto(null);
    setShowProdutoModal(true);
  };

  const handleEditProduto = (produto) => {
    setEditingProduto(produto);
    setShowProdutoModal(true);
  };

  const handleViewProduto = (produto) => {
    setSelectedProdutoId(produto.id);
    setShowDetalheModal(true);
  };

  const handleAddBloco = () => {
    if (!selectedProduto) return;
    setEditingBloco(null);
    setShowBlocoModal(true);
  };

  const handleEditBloco = (bloco) => {
    setEditingBloco(bloco);
    setShowBlocoModal(true);
  };

  const handleAddSubBloco = () => {
    if (!selectedBloco) return;
    setEditingSubBloco(null);
    setShowSubBlocoModal(true);
  };

  const handleEditSubBloco = (subBloco) => {
    setEditingSubBloco(subBloco);
    setShowSubBlocoModal(true);
  };

  const handleProdutoSuccess = () => {
    loadProdutos();
    setShowProdutoModal(false);
  };

  const handleBlocoSuccess = () => {
    if (selectedProduto) {
      loadBlocos(selectedProduto.id);
    }
    setShowBlocoModal(false);
  };

  const handleSubBlocoSuccess = () => {
    if (selectedBloco) {
      loadSubBlocos(selectedBloco.id);
    }
    setShowSubBlocoModal(false);
  };

  if (user?.tipo_usuario !== 'admin') {
    return (
      <PageContainer>
        <ErrorMessage>
          Acesso negado. Apenas administradores podem gerenciar produtos.
        </ErrorMessage>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <Header>
        <Title>Gerenciamento de Produtos</Title>
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          {/* Botão de Transferência de Gavetas - sempre visível para admins */}
          <StandardButton
            variant="outlined"
            startIcon={<TransferIcon />}
            onClick={() => setShowTransferenciaModal(true)}
            style={{
              borderColor: '#f59e0b',
              color: '#f59e0b',
              '&:hover': {
                backgroundColor: '#fef3c7',
                borderColor: '#d97706'
              }
            }}
          >
            Transferir Gavetas ITV_001
          </StandardButton>

          {activeTab === 'produtos' && (
            <StandardButton
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddProduto}
            >
              Novo Produto
            </StandardButton>
          )}
          {activeTab === 'blocos' && selectedProduto && (
            <StyledButton onClick={handleAddBloco}>+ Novo Bloco</StyledButton>
          )}
          {activeTab === 'sub-blocos' && selectedBloco && (
            <StyledButton onClick={handleAddSubBloco}>+ Novo Sub-bloco</StyledButton>
          )}
        </div>
      </Header>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <TabContainer>
        <TabList>
          <Tab 
            className={activeTab === 'produtos' ? 'active' : ''} 
            onClick={() => setActiveTab('produtos')}
          >
            Produtos
          </Tab>
          <Tab 
            className={activeTab === 'blocos' ? 'active' : ''} 
            onClick={() => setActiveTab('blocos')}
            disabled={!selectedProduto}
          >
            Blocos {selectedProduto && `(${selectedProduto.denominacao})`}
          </Tab>
          <Tab 
            className={activeTab === 'sub-blocos' ? 'active' : ''} 
            onClick={() => setActiveTab('sub-blocos')}
            disabled={!selectedBloco}
          >
            Sub-blocos {selectedBloco && `(${selectedBloco.nome})`}
          </Tab>
        </TabList>
      </TabContainer>

      {loading && <LoadingMessage>Carregando...</LoadingMessage>}

      {/* Aba Produtos */}
      {activeTab === 'produtos' && (
        <div>
          {produtos.map(produto => (
            <Card key={produto.id} clickable onClick={() => handleViewProduto(produto)}>
              <CardHeader>
                <CardTitle>{produto.denominacao}</CardTitle>
                <CardActions>
                  <StyledButton
                    className="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectProduto(produto);
                    }}
                  >
                    Ver Blocos
                  </StyledButton>
                  <StyledButton
                    className="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditProduto(produto);
                    }}
                  >
                    Editar
                  </StyledButton>
                </CardActions>
              </CardHeader>
              <InfoGrid>
                <InfoItem>
                  <InfoLabel>Código da Estação</InfoLabel>
                  <InfoValue>{produto.codigo_estacao}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Cliente</InfoLabel>
                  <InfoValue>{produto.codigo_cliente}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Meses para Exumar</InfoLabel>
                  <InfoValue>{produto.meses_para_exumar} meses</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Status</InfoLabel>
                  <InfoValue>{produto.ativo ? 'Ativo' : 'Inativo'}</InfoValue>
                </InfoItem>
              </InfoGrid>
              {produto.observacao && (
                <InfoItem>
                  <InfoLabel>Observações</InfoLabel>
                  <InfoValue>{produto.observacao}</InfoValue>
                </InfoItem>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Aba Blocos */}
      {activeTab === 'blocos' && selectedProduto && (
        <div>
          {blocos.map(bloco => (
            <Card key={bloco.id}>
              <CardHeader>
                <CardTitle>{bloco.nome}</CardTitle>
                <CardActions>
                  <StyledButton className="secondary" onClick={() => handleSelectBloco(bloco)}>
                    Ver Sub-blocos
                  </StyledButton>
                  <StyledButton className="secondary" onClick={() => handleEditBloco(bloco)}>
                    Editar
                  </StyledButton>
                </CardActions>
              </CardHeader>
              <InfoGrid>
                <InfoItem>
                  <InfoLabel>Código do Bloco</InfoLabel>
                  <InfoValue>{bloco.codigo_bloco}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Status</InfoLabel>
                  <InfoValue>{bloco.ativo ? 'Ativo' : 'Inativo'}</InfoValue>
                </InfoItem>
              </InfoGrid>
              {bloco.descricao && (
                <InfoItem>
                  <InfoLabel>Observações</InfoLabel>
                  <InfoValue>{bloco.descricao}</InfoValue>
                </InfoItem>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Aba Sub-blocos */}
      {activeTab === 'sub-blocos' && selectedBloco && (
        <div>
          {subBlocos.map(subBloco => (
            <Card key={subBloco.id}>
              <CardHeader>
                <CardTitle>{subBloco.nome}</CardTitle>
                <CardActions>
                  <StyledButton className="secondary" onClick={() => handleEditSubBloco(subBloco)}>
                    Editar
                  </StyledButton>
                </CardActions>
              </CardHeader>
              <InfoGrid>
                <InfoItem>
                  <InfoLabel>Código do Sub-bloco</InfoLabel>
                  <InfoValue>{subBloco.codigo_sub_bloco}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Total de Gavetas</InfoLabel>
                  <InfoValue>{subBloco.total_gavetas || 0}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Ranges de Gavetas</InfoLabel>
                  <InfoValue>{subBloco.ranges_gavetas || 'Nenhuma gaveta cadastrada'}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Status</InfoLabel>
                  <InfoValue>{subBloco.ativo ? 'Ativo' : 'Inativo'}</InfoValue>
                </InfoItem>
              </InfoGrid>
              {subBloco.descricao && (
                <InfoItem>
                  <InfoLabel>Observações</InfoLabel>
                  <InfoValue>{subBloco.descricao}</InfoValue>
                </InfoItem>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Modals */}
      <ProdutoModal
        isOpen={showProdutoModal}
        onClose={() => setShowProdutoModal(false)}
        produto={editingProduto}
        clientes={clientes}
        onSuccess={handleProdutoSuccess}
      />

      <BlocoModal
        isOpen={showBlocoModal}
        onClose={() => setShowBlocoModal(false)}
        bloco={editingBloco}
        produtoId={selectedProduto?.id}
        onSuccess={handleBlocoSuccess}
      />

      <SubBlocoModal
        isOpen={showSubBlocoModal}
        onClose={() => setShowSubBlocoModal(false)}
        subBloco={editingSubBloco}
        blocoId={selectedBloco?.id}
        onSuccess={handleSubBlocoSuccess}
      />

      <ProdutoDetalheModal
        isOpen={showDetalheModal}
        onClose={() => setShowDetalheModal(false)}
        produtoId={selectedProdutoId}
      />

      <TransferenciaGavetasModal
        open={showTransferenciaModal}
        onClose={() => setShowTransferenciaModal(false)}
        onSuccess={() => {
          // Recarregar dados se necessário
          console.log('Transferência concluída com sucesso!');
        }}
      />
    </PageContainer>
  );
};

export default GerenciamentoProdutosPage;
