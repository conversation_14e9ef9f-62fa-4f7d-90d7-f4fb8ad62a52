import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  Skeleton,
  Alert,
} from '@mui/material';
import {
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  PersonSearch as ExumacoesIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { produtoService } from '../services/api';
import {
  StandardContainer,
  StandardCard,
  StandardButton,
  FormGrid,
  FormGridItem,
  ContentSection,
  StatusChip,
} from '../components/common';

const BookExumacoesPage = () => {
  const [produtos, setProdutos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadProdutos();
  }, []);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      setError(null);

      // Usar API de estatísticas para obter dados completos
      const response = await produtoService.estatisticasExumacoes();
      setProdutos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar dados dos produtos. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleProdutoClick = (produto) => {
    navigate(`/dashboard/book-exumacoes/${produto.codigo_estacao}`, {
      state: { produto }
    });
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('pt-BR').format(num || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Não informado';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };



  if (loading) {
    return (
      <StandardContainer>
        <ContentSection>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              <ExumacoesIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
              BOOK DE EXUMAÇÕES
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Gerencie as exumações deste produto de forma organizada
            </Typography>
          </Box>

          <FormGrid>
            {[1, 2, 3, 4].map((item) => (
              <FormGridItem key={item} xs={12} sm={6} md={4} lg={3}>
                <StandardCard>
                  <Skeleton variant="rectangular" height={200} />
                </StandardCard>
              </FormGridItem>
            ))}
          </FormGrid>
        </ContentSection>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer>
        <ContentSection>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <StandardButton onClick={loadProdutos}>
            Tentar Novamente
          </StandardButton>
        </ContentSection>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer>
      <ContentSection>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            <ExumacoesIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
            BOOK DE EXUMAÇÕES
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gerencie as exumações deste produto de forma organizada
          </Typography>
        </Box>

        {produtos.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <ExumacoesIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Nenhum produto encontrado
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Este cliente ainda não possui produtos registrados.
            </Typography>
          </Box>
        ) : (
          <FormGrid>
            {produtos.map((produto) => {
              const totalExumacoes = produto.total_exumacoes || 0;

              return (
                <FormGridItem key={produto.codigo_estacao} xs={12} sm={6} md={4}>
                  <StandardCard
                    clickable
                    onClick={() => handleProdutoClick(produto)}
                    sx={{
                      height: '100%',
                      minHeight: '400px',
                      display: 'flex',
                      flexDirection: 'column',
                    }}
                  >
                    {/* Header do Card */}
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                      <BusinessIcon sx={{ mr: 2, color: 'primary.main', fontSize: 28 }} />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography
                          variant="h5"
                          component="h2"
                          sx={{
                            fontWeight: 600,
                            lineHeight: 1.3,
                            wordBreak: 'break-word',
                            mb: 1,
                          }}
                        >
                          {produto.denominacao || produto.codigo_estacao}
                        </Typography>
                        <StatusChip
                          status="Ativo"
                          color="success"
                        />
                      </Box>
                    </Box>

                    {/* Informações do Produto */}
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <CalendarIcon sx={{ fontSize: 20, mr: 1.5, color: 'text.secondary' }} />
                        <Typography
                          variant="body1"
                          color="text.secondary"
                          sx={{ fontWeight: 500 }}
                        >
                          Última exumação: {produto.ultima_exumacao ? formatDate(produto.ultima_exumacao) : 'Não informado'}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Estatísticas */}
                    <FormGrid spacing={2} sx={{ mb: 3 }}>
                      <FormGridItem xs={6}>
                        <Box sx={{
                          p: 2,
                          backgroundColor: 'primary.light',
                          borderRadius: 2,
                          textAlign: 'center',
                          color: 'primary.contrastText',
                        }}>
                          <Typography variant="caption" sx={{ fontSize: '0.75rem', opacity: 0.8 }}>
                            Blocos
                          </Typography>
                          <Typography variant="h5" sx={{ fontWeight: 700, mt: 0.5 }}>
                            {produto.total_blocos || 0}
                          </Typography>
                        </Box>
                      </FormGridItem>
                      <FormGridItem xs={6}>
                        <Box sx={{
                          p: 2,
                          backgroundColor: 'secondary.light',
                          borderRadius: 2,
                          textAlign: 'center',
                          color: 'secondary.contrastText',
                        }}>
                          <Typography variant="caption" sx={{ fontSize: '0.75rem', opacity: 0.8 }}>
                            Total Gavetas
                          </Typography>
                          <Typography variant="h5" sx={{ fontWeight: 700, mt: 0.5 }}>
                            {produto.total_gavetas || 0}
                          </Typography>
                        </Box>
                      </FormGridItem>
                    </FormGrid>

                    {/* Estatística de Exumações */}
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 600 }}>
                          Total de Exumações
                        </Typography>
                        <Typography variant="h6" fontWeight="bold" color="primary.main">
                          {formatNumber(totalExumacoes)}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Botão de Ação */}
                    <Box sx={{ mt: 'auto', pt: 2 }}>
                      <StandardButton
                        variant="contained"
                        fullWidth
                        startIcon={<VisibilityIcon />}
                        onClick={() => handleProdutoClick(produto)}
                      >
                        Ver Exumações
                      </StandardButton>
                    </Box>
                  </StandardCard>
                </FormGridItem>
              );
            })}
          </FormGrid>
        )}
      </ContentSection>
    </StandardContainer>
  );
};

export default BookExumacoesPage;
