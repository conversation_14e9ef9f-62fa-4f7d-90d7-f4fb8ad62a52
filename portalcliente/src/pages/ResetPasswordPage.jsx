import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Box,
  Alert,
  CircularProgress,
  InputAdornment,
  IconButton,
  LinearProgress
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Lock,
  CheckCircle,
  Error,
  Timer,
  Check,
  Close,
  Info
} from '@mui/icons-material';
import { authService } from '../services/api';

const ResetPasswordPage = () => {
  const { token } = useParams();
  const navigate = useNavigate();

  // Estados
  const [loading, setLoading] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [usuario, setUsuario] = useState(null);
  const [minutesRemaining, setMinutesRemaining] = useState(0);
  const [novaSenha, setNovaSenha] = useState('');
  const [confirmarSenha, setConfirmarSenha] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // Estados para validação de senha
  const [criteriosSenha, setCriteriosSenha] = useState([]);
  const [validacaoSenha, setValidacaoSenha] = useState(null);
  const [showCriterios, setShowCriterios] = useState(false);

  // Validar token ao carregar a página
  useEffect(() => {
    validateToken();
    loadPasswordCriteria();
  }, [token]);

  // Validar senha em tempo real
  useEffect(() => {
    if (novaSenha && novaSenha.length > 0) {
      validatePasswordRealTime(novaSenha);
    } else {
      setValidacaoSenha(null);
    }
  }, [novaSenha]);

  // Atualizar timer a cada minuto
  useEffect(() => {
    if (tokenValid && minutesRemaining > 0) {
      const timer = setInterval(() => {
        setMinutesRemaining(prev => {
          if (prev <= 1) {
            setTokenValid(false);
            setError('Link expirado. Solicite um novo link através do "Esqueci minha senha".');
            return 0;
          }
          return prev - 1;
        });
      }, 60000); // 1 minuto

      return () => clearInterval(timer);
    }
  }, [tokenValid, minutesRemaining]);

  const validateToken = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔍 Validando token:', token);

      const response = await authService.validateResetToken(token);

      if (response.data.valid) {
        setTokenValid(true);
        setUsuario(response.data.usuario);
        setMinutesRemaining(response.data.minutes_remaining);
        console.log('✅ Token válido para:', response.data.usuario.email);
      } else {
        setTokenValid(false);
        setError(response.data.error || 'Link inválido ou expirado');
        console.log('❌ Token inválido:', response.data.error);
      }
    } catch (error) {
      console.error('❌ Erro ao validar token:', error);
      setTokenValid(false);
      setError('Erro ao validar link. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const loadPasswordCriteria = async () => {
    try {
      const response = await authService.getPasswordCriteria();
      setCriteriosSenha(response.data.criterios || []);
      console.log('✅ Critérios de senha carregados:', response.data.criterios);
    } catch (error) {
      console.error('❌ Erro ao carregar critérios de senha:', error);
    }
  };

  const validatePasswordRealTime = async (senha) => {
    try {
      const response = await authService.validatePassword(senha);
      setValidacaoSenha(response.data);
    } catch (error) {
      console.error('❌ Erro ao validar senha:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!novaSenha || !confirmarSenha) {
      setError('Preencha todos os campos');
      return;
    }

    if (novaSenha !== confirmarSenha) {
      setError('As senhas não coincidem');
      return;
    }

    if (novaSenha.length < 6) {
      setError('A senha deve ter pelo menos 6 caracteres');
      return;
    }

    try {
      setSubmitting(true);
      setError('');

      console.log('🔄 Enviando nova senha...');

      const response = await authService.resetPassword({
        token,
        novaSenha,
        confirmarSenha
      });

      if (response.data.success) {
        setSuccess('Senha redefinida com sucesso! Redirecionando para o login...');
        console.log('✅ Senha redefinida com sucesso');
        
        setTimeout(() => {
          navigate('/login', { 
            state: { 
              message: 'Senha redefinida com sucesso! Faça login com sua nova senha.' 
            }
          });
        }, 3000);
      } else {
        setError(response.data.error || 'Erro ao redefinir senha');
      }
    } catch (error) {
      console.error('❌ Erro ao redefinir senha:', error);

      // Tratamento específico para erro de critérios de senha
      if (error.response?.data?.criterios_nao_atendidos) {
        const errorData = error.response.data;
        setError(errorData.error || 'Senha não atende aos critérios de segurança');
        setValidacaoSenha(errorData);
        setShowCriterios(true);
      } else {
        setError(error.response?.data?.error || 'Erro ao redefinir senha. Tente novamente.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const getPasswordStrength = (password) => {
    let strength = 0;
    if (password.length >= 6) strength += 25;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    return strength;
  };

  const getPasswordStrengthColor = (strength) => {
    if (strength < 50) return 'error';
    if (strength < 75) return 'warning';
    return 'success';
  };

  const getPasswordStrengthText = (strength) => {
    if (strength < 50) return 'Fraca';
    if (strength < 75) return 'Média';
    return 'Forte';
  };

  // Componente para exibir critérios de senha
  const PasswordCriteriaComponent = ({ validacao, criterios }) => {
    if (!validacao && !criterios.length) return null;

    const criteriosParaExibir = validacao?.todos_criterios || criterios.map(c => ({
      ...c,
      atendido: false
    }));

    return (
      <Box sx={{ mt: 2, mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.300' }}>
        <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
          <Info sx={{ fontSize: 16, mr: 1, color: 'primary.main' }} />
          Critérios de Segurança da Senha
        </Typography>

        {criteriosParaExibir.map((criterio) => (
          <Box key={criterio.id} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            {criterio.atendido ? (
              <Check sx={{ fontSize: 16, color: 'success.main', mr: 1 }} />
            ) : (
              <Close sx={{ fontSize: 16, color: 'error.main', mr: 1 }} />
            )}
            <Typography
              variant="caption"
              sx={{
                color: criterio.atendido ? 'success.main' : 'error.main',
                fontWeight: criterio.atendido ? 'normal' : 'medium'
              }}
            >
              {criterio.descricao}
            </Typography>
          </Box>
        ))}

        {validacao?.exemplo_senha_valida && (
          <Box sx={{ mt: 1, p: 1, bgcolor: 'success.50', borderRadius: 0.5, border: '1px solid', borderColor: 'success.200' }}>
            <Typography variant="caption" sx={{ color: 'success.dark', fontWeight: 'medium' }}>
              💡 Exemplo de senha válida: {validacao.exemplo_senha_valida}
            </Typography>
          </Box>
        )}
      </Box>
    );
  };

  if (loading) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Validando link...
        </Typography>
      </Container>
    );
  }

  if (!tokenValid) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
          <Error color="error" sx={{ fontSize: 60, mb: 2 }} />
          <Typography variant="h5" gutterBottom color="error">
            Link Inválido ou Expirado
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            {error || 'Este link não é válido ou já expirou.'}
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate('/login')}
            sx={{ mr: 2 }}
          >
            Voltar ao Login
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate('/login', { state: { openForgotPassword: true } })}
          >
            Solicitar Novo Link
          </Button>
        </Paper>
      </Container>
    );
  }

  if (success) {
    return (
      <Container maxWidth="sm" sx={{ mt: 8 }}>
        <Paper elevation={3} sx={{ p: 4, textAlign: 'center' }}>
          <CheckCircle color="success" sx={{ fontSize: 60, mb: 2 }} />
          <Typography variant="h5" gutterBottom color="success.main">
            Senha Redefinida!
          </Typography>
          <Typography variant="body1" sx={{ mb: 3 }}>
            {success}
          </Typography>
          <CircularProgress size={30} />
        </Paper>
      </Container>
    );
  }

  const passwordStrength = getPasswordStrength(novaSenha);

  return (
    <Container maxWidth="sm" sx={{ mt: 8 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Lock color="primary" sx={{ fontSize: 60, mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Redefinir Senha
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Portal do Cliente Evolution
          </Typography>
        </Box>

        {usuario && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Usuário:</strong> {usuario.nome} ({usuario.email})
            </Typography>
            <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Timer sx={{ fontSize: 16, mr: 1 }} />
              <strong>Tempo restante:</strong> {minutesRemaining} minutos
            </Typography>
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Nova Senha"
            type={showPassword ? 'text' : 'password'}
            value={novaSenha}
            onChange={(e) => setNovaSenha(e.target.value)}
            margin="normal"
            required
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          {novaSenha && (
            <Box sx={{ mt: 1, mb: 2 }}>
              <Typography variant="caption" color="text.secondary">
                Força da senha: {getPasswordStrengthText(passwordStrength)}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={passwordStrength}
                color={getPasswordStrengthColor(passwordStrength)}
                sx={{ mt: 0.5 }}
              />
            </Box>
          )}

          {/* Critérios de senha */}
          {(novaSenha || showCriterios) && (
            <PasswordCriteriaComponent
              validacao={validacaoSenha}
              criterios={criteriosSenha}
            />
          )}

          <TextField
            fullWidth
            label="Confirmar Nova Senha"
            type={showConfirmPassword ? 'text' : 'password'}
            value={confirmarSenha}
            onChange={(e) => setConfirmarSenha(e.target.value)}
            margin="normal"
            required
            error={confirmarSenha && novaSenha !== confirmarSenha}
            helperText={
              confirmarSenha && novaSenha !== confirmarSenha
                ? 'As senhas não coincidem'
                : ''
            }
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={submitting || !novaSenha || !confirmarSenha || novaSenha !== confirmarSenha}
            sx={{ mt: 3, mb: 2 }}
          >
            {submitting ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Redefinindo...
              </>
            ) : (
              'Redefinir Senha'
            )}
          </Button>

          <Box sx={{ textAlign: 'center' }}>
            <Button
              variant="text"
              onClick={() => navigate('/login')}
              disabled={submitting}
            >
              Voltar ao Login
            </Button>
          </Box>
        </form>
      </Paper>
    </Container>
  );
};

export default ResetPasswordPage;
