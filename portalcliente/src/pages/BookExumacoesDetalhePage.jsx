import React, { useState, useEffect } from 'react';
import { useParams, useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  Skeleton,
  Alert,
  Breadcrumbs,
  Link,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  PersonSearch as ExumacoesIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { sepultamentoService } from '../services/api';
import ExumacaoDetailsModal from '../components/ExumacaoDetailsModal';
import {
  StandardContainer,
  StandardButton,
  StandardTable,
  TableActions,
  StatusChip,
  ContentSection,
} from '../components/common';

const BookExumacoesDetalhePage = () => {
  const { codigoEstacao } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [produto] = useState(location.state?.produto || {});
  const [exumacoes, setExumacoes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredExumacoes, setFilteredExumacoes] = useState([]);
  const [selectedExumacao, setSelectedExumacao] = useState(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);

  useEffect(() => {
    loadExumacoes();
  }, [codigoEstacao]);

  useEffect(() => {
    filterExumacoes();
  }, [exumacoes, searchTerm]);

  const loadExumacoes = async () => {
    try {
      setLoading(true);
      setError(null);

      // CORREÇÃO: Buscar sepultamentos exumados (status_exumacao = true)
      const response = await sepultamentoService.listar({
        codigo_estacao: codigoEstacao,
        ativo: true, // Registro ativo
        status_exumacao: true // Mas exumado
      });

      setExumacoes(response.data || []);
      console.log(`📋 ${response.data?.length || 0} exumações carregadas para ${codigoEstacao}`);
    } catch (error) {
      console.error('Erro ao carregar exumações:', error);
      setError('Erro ao carregar dados das exumações. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const filterExumacoes = () => {
    if (!searchTerm.trim()) {
      setFilteredExumacoes(exumacoes);
      return;
    }

    const filtered = exumacoes.filter((exumacao) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        exumacao.nome_sepultado?.toLowerCase().includes(searchLower) ||
        exumacao.numero_gaveta?.toString().includes(searchLower) ||
        exumacao.data_sepultamento?.includes(searchTerm) ||
        exumacao.data_exumacao?.includes(searchTerm)
      );
    });

    setFilteredExumacoes(filtered);
  };

  const handleDetailsClick = (exumacao) => {
    setSelectedExumacao(exumacao);
    setDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setDetailsModalOpen(false);
    setSelectedExumacao(null);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    });
  };

  const formatTime = (timeString) => {
    if (!timeString) return '-';
    // Se for um horário completo (HH:MM:SS), pegar apenas HH:MM
    if (timeString.includes(':')) {
      return timeString.substring(0, 5);
    }
    return timeString;
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const columns = [
    {
      id: 'nome_sepultado',
      label: 'NOME DO SEPULTADO',
      minWidth: 200,
      format: (value) => value || '-',
    },
    {
      id: 'localizacao',
      label: 'LOCALIZAÇÃO',
      minWidth: 150,
      format: (value, row) => {
        const bloco = row.bloco_nome || row.denominacao_bloco || row.codigo_bloco || '-';
        return `${bloco} → Gaveta ${row.numero_gaveta || '-'}`;
      },
    },
    {
      id: 'numero_gaveta',
      label: 'GAVETA',
      minWidth: 80,
      align: 'center',
      format: (value) => value || '-',
    },
    {
      id: 'data_sepultamento',
      label: 'DATA SEPULTAMENTO',
      minWidth: 120,
      align: 'center',
      format: (value) => formatDate(value),
    },
    {
      id: 'data_exumacao',
      label: 'DATA EXUMAÇÃO',
      minWidth: 120,
      align: 'center',
      format: (value) => formatDate(value),
    },
    {
      id: 'horario_exumacao',
      label: 'HORÁRIO DA EXUMAÇÃO',
      minWidth: 150,
      align: 'center',
      format: (value) => formatTime(value),
    },
    {
      id: 'status',
      label: 'STATUS',
      minWidth: 100,
      align: 'center',
      format: (value, row) => (
        <StatusChip
          label="Exumado"
          color="secondary"
          size="small"
          sx={{
            backgroundColor: '#9c27b0',
            color: 'white',
            '&:hover': {
              backgroundColor: '#7b1fa2',
            }
          }}
        />
      ),
    },
  ];

  const actions = [
    {
      label: 'Ver Detalhes',
      icon: <VisibilityIcon />,
      onClick: handleDetailsClick,
      color: 'primary',
    },
  ];

  if (loading) {
    return (
      <StandardContainer>
        <ContentSection>
          <Box sx={{ mb: 3 }}>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={500} height={24} />
          </Box>
          <Skeleton variant="rectangular" height={400} />
        </ContentSection>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer>
        <ContentSection>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <StandardButton onClick={loadExumacoes}>
            Tentar Novamente
          </StandardButton>
        </ContentSection>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer>
      <ContentSection>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 2 }}>
          <Link
            component="button"
            variant="body2"
            onClick={() => navigate('/dashboard')}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />
            Início
          </Link>
          <Link
            component="button"
            variant="body2"
            onClick={() => navigate('/dashboard/book-exumacoes')}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <ExumacoesIcon sx={{ mr: 0.5, fontSize: 16 }} />
            Book de Exumações
          </Link>
          <Typography color="text.primary" variant="body2">
            {produto.denominacao || codigoEstacao}
          </Typography>
        </Breadcrumbs>

        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <StandardButton
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/book-exumacoes')}
            sx={{ mr: 2 }}
          >
            Voltar
          </StandardButton>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              {produto.denominacao || codigoEstacao}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Gerencie as exumações deste produto de forma organizada
            </Typography>
          </Box>
        </Box>

        {/* Search */}
        <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            placeholder="Buscar exumações"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            variant="outlined"
            size="small"
            sx={{ minWidth: 300 }}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              endAdornment: searchTerm && (
                <ClearIcon
                  sx={{ cursor: 'pointer', color: 'text.secondary' }}
                  onClick={clearSearch}
                />
              ),
            }}
          />
          <Typography variant="body2" color="text.secondary">
            Total: {filteredExumacoes.length} exumações - Digite e pressione ENTER para buscar
          </Typography>
        </Box>

        {/* Table */}
        {filteredExumacoes.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <ExumacoesIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Nenhuma exumação encontrada
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Este produto ainda não possui exumações registradas.
            </Typography>
          </Box>
        ) : (
          <StandardTable
            columns={columns}
            data={filteredExumacoes}
            actions={actions}
            onRowClick={handleDetailsClick}
          />
        )}

        {/* Details Modal */}
        {selectedExumacao && (
          <ExumacaoDetailsModal
            open={detailsModalOpen}
            onClose={handleCloseDetailsModal}
            exumacao={selectedExumacao}
            readOnly={true}
          />
        )}
      </ContentSection>
    </StandardContainer>
  );
};

export default BookExumacoesDetalhePage;
