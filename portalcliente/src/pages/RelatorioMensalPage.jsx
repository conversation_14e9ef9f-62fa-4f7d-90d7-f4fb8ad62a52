/**
 * PÁGINA: RELATÓRIO MENSAL
 * Interface para geração de relatórios mensais baseados na tabela daily_report
 * Acesso: Apenas administradores
 * Filtro: Por produto (automaticamente busca dados do cliente associado)
 * Autor: Sistema Multi-Agente
 * Data: 2025-01-21
 * Última atualização: 2025-01-21 - Alterado filtro de cliente para produto
 */

import React, { useState, useEffect } from 'react';
import {
  Typography,
  Box,
  Alert,
  CircularProgress,
  Grid,
  Divider,
} from '@mui/material';
import {
  Assessment as ReportIcon,
  Business as BusinessIcon,
  CalendarToday as CalendarIcon,
  PictureAsPdf as PdfIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { baixarPDFRelatorioMensal } from '../services/pdfRelatorioMensal';
import { produtoService } from '../services/api';
import {
  StandardContainer,
  StandardButton,
  StandardCard,
  StandardForm,
  StandardTextField,
  StandardSelect,
  StandardTable,
  FormGrid,
  FormGridItem,
  FormSection,
  ContentSection,
  StatusChip,
} from '../components/common';

// Meses do ano para dropdown
const MESES = [
  { value: 1, label: 'Janeiro' },
  { value: 2, label: 'Fevereiro' },
  { value: 3, label: 'Março' },
  { value: 4, label: 'Abril' },
  { value: 5, label: 'Maio' },
  { value: 6, label: 'Junho' },
  { value: 7, label: 'Julho' },
  { value: 8, label: 'Agosto' },
  { value: 9, label: 'Setembro' },
  { value: 10, label: 'Outubro' },
  { value: 11, label: 'Novembro' },
  { value: 12, label: 'Dezembro' },
];

const RelatorioMensalPage = () => {
  console.log('🎬 === RELATÓRIO MENSAL PAGE INICIANDO ===');
  console.log('🕐 Timestamp:', new Date().toISOString());

  const { user } = useAuth();
  console.log('👤 User do AuthContext:', user ? `${user.nome} (${user.tipo_usuario})` : 'null/undefined');

  // Estados para filtros
  const [produtos, setProdutos] = useState([]);
  const [anos, setAnos] = useState([]);
  const [selectedProduto, setSelectedProduto] = useState('');
  const [selectedMes, setSelectedMes] = useState('');
  const [selectedAno, setSelectedAno] = useState('');
  const [custoKwh, setCustoKwh] = useState('');

  // Estados para dados e controle
  const [loading, setLoading] = useState(false);
  const [loadingProdutos, setLoadingProdutos] = useState(false);
  const [error, setError] = useState('');
  const [relatorioData, setRelatorioData] = useState(null);




  // CORREÇÃO CRÍTICA: useEffect independente do user para garantir execução
  useEffect(() => {
    console.log('🔍 === RELATÓRIO MENSAL USEEFFECT EXECUTADO ===');
    console.log('🔍 User state:', user);
    console.log('🔍 Token no localStorage:', localStorage.getItem('token') ? 'Existe' : 'Não existe');
    console.log('🔍 Estado atual produtos:', produtos.length);
    console.log('🔍 Loading produtos:', loadingProdutos);

    const initializeComponent = async () => {
      const token = localStorage.getItem('token');

      if (!token) {
        console.log('❌ Nenhum token encontrado');
        window.location.href = '/login';
        return;
      }

      try {
        // Verificar se o token é válido
        const payload = JSON.parse(atob(token.split('.')[1]));
        const isExpired = Date.now() > payload.exp * 1000;

        if (isExpired) {
          console.log('❌ Token expirado');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
          return;
        }

        console.log('🔑 Token válido encontrado:', {
          userType: payload.tipo_usuario,
          nome: payload.nome,
          expiresAt: new Date(payload.exp * 1000).toISOString()
        });

        // Verificar se é admin
        if (payload.tipo_usuario !== 'admin') {
          console.log('❌ Usuário não é admin:', payload.tipo_usuario);
          setError('Acesso negado. Apenas administradores podem acessar relatórios mensais.');
          return;
        }

        console.log('✅ Usuário é admin, carregando dados...');

        // Verificar token com API para garantir que ainda é válido
        try {
          const response = await fetch(`${window.location.origin}/api/auth/verify`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error('Token inválido na API');
          }

          const data = await response.json();
          console.log('✅ Token verificado com API:', data.usuario);

          // Carregar dados independentemente do AuthContext
          console.log('🚀 Carregando produtos e anos...');
          await loadProdutos();
          await loadAnos();

          // Pré-selecionar mês e ano atuais
          const agora = new Date();
          const mesAtual = agora.getMonth() + 1;
          const anoAtual = agora.getFullYear();

          console.log('📅 Definindo valores iniciais:', { mesAtual, anoAtual });
          setSelectedMes(mesAtual);
          setSelectedAno(anoAtual);
          setCustoKwh('0.75');

          console.log('✅ Inicialização completa!', {
            mes: mesAtual,
            ano: anoAtual,
            custo: '0.75'
          });

        } catch (apiError) {
          console.log('❌ Erro na verificação da API:', apiError);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }

      } catch (e) {
        console.log('❌ Token inválido:', e);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
    };

    // Executar inicialização
    initializeComponent();

  }, []); // CORREÇÃO: Remover dependência [user] para garantir execução





  // Gerar relatório automaticamente quando todos os filtros estiverem preenchidos
  useEffect(() => {
    if (selectedProduto && selectedMes && selectedAno && custoKwh && !loading && !relatorioData) {
      console.log('🎯 Todos os filtros preenchidos, gerando relatório automaticamente...');
      gerarRelatorio();
    }
  }, [selectedProduto, selectedMes, selectedAno, custoKwh]);

  /**
   * Carrega lista de produtos
   */
  const loadProdutos = async () => {
    console.log('🚀 === INICIANDO LOADPRODUTOS ===');
    console.log('🕐 Timestamp:', new Date().toISOString());

    try {
      setLoadingProdutos(true);
      setError(''); // Limpar erros anteriores
      console.log('🔍 Carregando produtos...');

      const response = await produtoService.listar();
      console.log('📦 Resposta da API produtos:', response);

      const produtosList = response.data || [];
      console.log('📦 Produtos recebidos:', produtosList.length);

      if (produtosList.length === 0) {
        console.log('⚠️ Nenhum produto encontrado');
        setProdutos([]);
        setError('Nenhum produto encontrado');
        return [];
      }

      // Filtrar apenas produtos ativos
      const produtosAtivos = produtosList.filter(produto => produto.ativo === true);
      console.log('📦 Produtos ativos:', produtosAtivos.length);

      setProdutos(produtosAtivos);
      console.log('✅ Produtos carregados com sucesso:', produtosAtivos.length);

      console.log('✅ === LOADPRODUTOS CONCLUÍDO COM SUCESSO ===');
      return produtosAtivos;

    } catch (error) {
      console.error('❌ === ERRO NO LOADPRODUTOS ===');
      console.error('❌ Erro completo:', error);
      setError(`Erro ao carregar lista de produtos: ${error.message}`);
      setProdutos([]);
      throw error;
    } finally {
      setLoadingProdutos(false);
    }
  };







  /**
   * Carrega anos disponíveis
   */
  const loadAnos = async () => {
    try {
      console.log('🔍 Carregando anos disponíveis...');

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token de autenticação não encontrado');
      }

      const baseUrl = window.location.origin;
      const apiUrl = `${baseUrl}/api/relatorio-mensal/anos`;

      console.log('📡 URL da requisição anos:', apiUrl);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
        },
      });

      console.log('📡 Response anos status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erro ao carregar anos:', errorText);
        throw new Error(`Erro ao carregar anos: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('📊 Anos recebidos:', data);

      const anosList = data.anos || [];
      console.log('📅 Lista de anos:', anosList);

      setAnos(anosList);

      // Se o ano atual não estiver pré-selecionado e estiver na lista, selecionar
      const anoAtual = new Date().getFullYear();
      if (anosList.includes(anoAtual) && !selectedAno) {
        console.log('✅ Pré-selecionando ano atual:', anoAtual);
        setSelectedAno(anoAtual);
      } else if (anosList.length > 0 && !selectedAno) {
        // Se o ano atual não estiver disponível, selecionar o primeiro da lista
        console.log('✅ Pré-selecionando primeiro ano:', anosList[0]);
        setSelectedAno(anosList[0]);
      }
    } catch (error) {
      console.error('Erro ao carregar anos:', error);
      setError('Erro ao carregar lista de anos');
    }
  };

  /**
   * Gera o relatório mensal
   */
  const gerarRelatorio = async () => {
    if (!selectedProduto || !selectedMes || !selectedAno || !custoKwh) {
      setError('Todos os campos são obrigatórios');
      return;
    }

    if (parseFloat(custoKwh) <= 0) {
      setError('O custo do kWh deve ser maior que zero');
      return;
    }

    try {
      setLoading(true);
      setError('');
      console.log('🔍 Gerando relatório...');

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token de autenticação não encontrado');
      }

      const baseUrl = window.location.origin;
      const apiUrl = `${baseUrl}/api/relatorio-mensal/gerar`;

      const requestData = {
        produto_id: selectedProduto,
        mes: parseInt(selectedMes),
        ano: parseInt(selectedAno),
        custo_kwh: parseFloat(custoKwh),
      };

      console.log('📡 URL da requisição gerar:', apiUrl);
      console.log('📊 Dados da requisição:', requestData);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(requestData),
      });

      console.log('📡 Response gerar status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erro ao gerar relatório:', errorText);

        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch (e) {
          throw new Error(`Erro ao gerar relatório: ${response.status} - ${errorText}`);
        }

        throw new Error(errorData.error || 'Erro ao gerar relatório');
      }

      const data = await response.json();
      console.log('📊 Relatório gerado:', data);

      setRelatorioData(data.relatorio);
    } catch (error) {
      console.error('❌ Erro ao gerar relatório:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Limpa os filtros e dados
   */
  const limparFiltros = () => {
    setSelectedProduto('');
    setSelectedMes('');
    setSelectedAno('');
    setCustoKwh('');
    setRelatorioData(null);
    setError('');
  };

  /**
   * Gera e baixa o PDF do relatório
   */
  const exportarPDF = async () => {
    if (!relatorioData) {
      setError('Nenhum relatório foi gerado ainda');
      return;
    }

    try {
      setLoading(true);
      await baixarPDFRelatorioMensal(relatorioData);
    } catch (error) {
      console.error('Erro ao exportar PDF:', error);
      setError('Erro ao gerar PDF do relatório');
    } finally {
      setLoading(false);
    }
  };

  // Verificação de acesso
  if (!user) {
    return (
      <StandardContainer>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Carregando...
          </Typography>
        </Box>
      </StandardContainer>
    );
  }

  if (user.tipo_usuario !== 'admin') {
    return (
      <StandardContainer>
        <Alert severity="error">
          Acesso negado. Apenas administradores podem acessar relatórios mensais.
        </Alert>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer>
      {/* Cabeçalho */}
      <ContentSection>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <ReportIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold">
            Relatório Mensal Melhorado
          </Typography>
        </Box>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
          Gere relatórios mensais detalhados com gráficos de temperatura e umidade por bloco,
          organizados por período (manhã, tarde, noite) e otimizados para formato A4.
        </Typography>

        {/* Informações sobre melhorias */}
        <Box sx={{
          bgcolor: 'success.light',
          color: 'success.contrastText',
          p: 2,
          borderRadius: 1,
          mb: 3
        }}>
          <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
            ✨ Melhorias Implementadas:
          </Typography>
          <Typography variant="body2" component="div">
            • Layout otimizado para papel A4 • Gráficos de temperatura e umidade por bloco<br/>
            • Dados organizados por manhã, tarde e noite • Resumo executivo unificado<br/>
            • Tabelas de dados detalhadas por período • Performance otimizada
          </Typography>
        </Box>

        {/* Indicador de pré-seleção */}
        {(selectedProduto || selectedMes || selectedAno || custoKwh) && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              ✨ <strong>Filtros pré-selecionados automaticamente!</strong>
              {selectedProduto && ` Produto: ${produtos.find(p => p.id === selectedProduto)?.denominacao || selectedProduto}`}
              {selectedMes && ` | Mês: ${MESES.find(m => m.value === parseInt(selectedMes))?.label}`}
              {selectedAno && ` | Ano: ${selectedAno}`}
              {custoKwh && ` | kWh: R$ ${custoKwh}`}
            </Typography>
          </Alert>
        )}
      </ContentSection>



      {/* Formulário de Filtros */}
      <StandardCard title="Filtros do Relatório" icon={<CalendarIcon />}>
        <StandardForm>
          <FormGrid spacing={3}>
            {/* Produto */}
            <FormGridItem xs={12} md={4}>
              <StandardSelect
                label="Produto"
                value={selectedProduto}
                onChange={(e) => setSelectedProduto(e.target.value)}
                options={produtos.map(produto => ({
                  value: produto.id,
                  label: produto.denominacao || produto.nome
                }))}
                placeholder="Selecione um produto"
                required
                fullWidth
                disabled={loadingProdutos}
                sx={{
                  minHeight: '64px',
                  '& .MuiOutlinedInput-root': {
                    minHeight: '64px',
                  },
                  '& .MuiSelect-select': {
                    minHeight: '64px',
                    padding: '18px 14px',
                    fontSize: '1.1rem',
                    display: 'flex',
                    alignItems: 'center',
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '1.1rem',
                    fontWeight: 500,
                  }
                }}
              />
              {loadingProdutos && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
                  <CircularProgress size={20} />
                </Box>
              )}
            </FormGridItem>

            {/* Mês */}
            <FormGridItem xs={12} md={4}>
              <StandardSelect
                label="Mês"
                value={selectedMes}
                onChange={(e) => setSelectedMes(e.target.value)}
                options={MESES}
                placeholder="Selecione o mês"
                required
                fullWidth
                sx={{
                  minHeight: '64px',
                  '& .MuiOutlinedInput-root': {
                    minHeight: '64px',
                  },
                  '& .MuiSelect-select': {
                    minHeight: '64px',
                    padding: '18px 14px',
                    fontSize: '1.1rem',
                    display: 'flex',
                    alignItems: 'center',
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '1.1rem',
                    fontWeight: 500,
                  }
                }}
              />
            </FormGridItem>

            {/* Ano */}
            <FormGridItem xs={12} md={4}>
              <StandardSelect
                label="Ano"
                value={selectedAno}
                onChange={(e) => setSelectedAno(e.target.value)}
                options={anos.map(ano => ({ value: ano, label: ano.toString() }))}
                placeholder="Selecione o ano"
                required
                fullWidth
                sx={{
                  minHeight: '64px',
                  '& .MuiOutlinedInput-root': {
                    minHeight: '64px',
                  },
                  '& .MuiSelect-select': {
                    minHeight: '64px',
                    padding: '18px 14px',
                    fontSize: '1.1rem',
                    display: 'flex',
                    alignItems: 'center',
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '1.1rem',
                    fontWeight: 500,
                  }
                }}
              />
            </FormGridItem>

            {/* Custo kWh */}
            <FormGridItem xs={12} md={4}>
              <StandardTextField
                label="Custo médio do kWh (R$)"
                type="number"
                value={custoKwh}
                onChange={(e) => setCustoKwh(e.target.value)}
                required
                fullWidth
                sx={{
                  minHeight: '64px',
                  '& .MuiOutlinedInput-root': {
                    minHeight: '64px',
                  },
                  '& .MuiInputBase-input': {
                    minHeight: '64px',
                    padding: '18px 14px',
                    fontSize: '1.1rem',
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '1.1rem',
                    fontWeight: 500,
                  }
                }}
                inputProps={{
                  step: "0.01",
                  min: "0.01"
                }}
                placeholder="0.00"
              />
            </FormGridItem>
          </FormGrid>

          {/* Botões de Ação */}
          <Box sx={{ display: 'flex', gap: 2, mt: 4, justifyContent: 'flex-end' }}>
            <StandardButton
              variant="outlined"
              onClick={limparFiltros}
              disabled={loading}
            >
              Limpar
            </StandardButton>
            <StandardButton
              variant="contained"
              onClick={gerarRelatorio}
              disabled={loading || !selectedProduto || !selectedMes || !selectedAno || !custoKwh}
              startIcon={loading ? <CircularProgress size={20} /> : <ReportIcon />}
            >
              {loading ? 'Gerando...' : 'Gerar Relatório'}
            </StandardButton>



            {/* Indicador de status */}
            {!loading && selectedProduto && selectedMes && selectedAno && custoKwh && (
              <Typography variant="body2" color="success.main" sx={{ mt: 1 }}>
                ✅ Todos os filtros preenchidos - Clique para gerar ou aguarde geração automática
              </Typography>
            )}
          </Box>
        </StandardForm>
      </StandardCard>

      {/* Mensagens de Erro */}
      {error && (
        <Alert severity="error" sx={{ mt: 3 }}>
          {error}
        </Alert>
      )}

      {/* Botão de Exportar PDF - Posicionado entre Filtros e Informações do Cliente */}
      {relatorioData && (
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
          <StandardButton
            variant="contained"
            color="secondary"
            size="large"
            startIcon={<PdfIcon />}
            onClick={exportarPDF}
            disabled={loading}
          >
            EXPORTAR RELATÓRIO EM PDF
          </StandardButton>
        </Box>
      )}

      {/* Dados do Relatório */}
      {relatorioData && relatorioData.cliente && relatorioData.produto && relatorioData.parametros && (
        <Box sx={{ mt: 4 }}>
          {/* Informações do Cliente */}
          <StandardCard title="Informações do Cliente" icon={<BusinessIcon />}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Razão Social:</Typography>
                <Typography variant="body1" fontWeight="bold">{relatorioData.cliente.razao_social}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">CNPJ:</Typography>
                <Typography variant="body1">{relatorioData.cliente.cnpj}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Código Cliente:</Typography>
                <Typography variant="body1">{relatorioData.cliente.codigo_cliente}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Produto/Estação:</Typography>
                <Typography variant="body1">{relatorioData.produto.denominacao}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Custo médio kWh:</Typography>
                <Typography variant="body1">R$ {(relatorioData.parametros.custoKwh || 0).toFixed(2)}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2" color="text.secondary">Período:</Typography>
                <Typography variant="body1">
                  {MESES.find(m => m.value === relatorioData.parametros.mes)?.label} / {relatorioData.parametros.ano}
                </Typography>
              </Grid>
            </Grid>
          </StandardCard>

          {/* Resumo Executivo - Dados Universais */}
          {relatorioData.dadosUniversais && (
            <StandardCard title="Resumo Executivo - Dados Universais" icon={<MoneyIcon />} sx={{ mt: 3 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ p: 2, bgcolor: 'primary.light', borderRadius: 1, color: 'white' }}>
                    <Typography variant="h6" fontWeight="bold">
                      {relatorioData.dadosUniversais.consumoEletricoTotal || '0.00'} kWh
                    </Typography>
                    <Typography variant="body2">Consumo Elétrico Total</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box sx={{ p: 2, bgcolor: 'success.light', borderRadius: 1, color: 'white' }}>
                    <Typography variant="h6" fontWeight="bold">
                      R$ {relatorioData.dadosUniversais.custoOperacao || '0.00'}
                    </Typography>
                    <Typography variant="body2">Custo de Operação</Typography>
                  </Box>
                </Grid>
              </Grid>
            </StandardCard>
          )}

          {/* Resumo Executivo - Valores Mistos */}
          {relatorioData.valoresMistos && (
            <StandardCard title="Resumo Executivo - Valores Mistos" icon={<ReportIcon />} sx={{ mt: 3 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Volume de Ar Total (m³):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.volumeArTotal || '0.00'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Tempo Total Trocas Gasosas (s):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.tempoTotalTrocasGasosas || '0'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Fluxo Médio de Ar (m³/h):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.fluxoMedioAr || '0.00'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Potência Média Percentual (%):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.potenciaMediaPercentual || '0.00'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Temperatura Média Sistema (ºC):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.temperaturaMediaSistema || '0.00'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Umidade Relativa Média (%):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.umidadeRelativaMediaSistema || '0.00'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Pressão Alvo Estanqueidade (mmH2O):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.pressaoAlvoEstanqueidade || '0.00'}</Typography>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                  <Typography variant="body2" color="text.secondary">Pressão Média Obtida (mmH2O):</Typography>
                  <Typography variant="h6" fontWeight="bold">{relatorioData.valoresMistos.pressaoMediaObtidaEstanqueidade || '0.00'}</Typography>
                </Grid>
              </Grid>
            </StandardCard>
          )}

          {/* Dados Operacionais por Dia */}
          {relatorioData.dadosOperacionais && Array.isArray(relatorioData.dadosOperacionais) && (
            <StandardCard title="Dados Operacionais Diários" icon={<CalendarIcon />} sx={{ mt: 3 }}>
              <StandardTable
                columns={[
                  { id: 'data_leitura_formatada', label: 'Data', minWidth: 100 },
                  { id: 'codigo_bloco', label: 'Bloco', minWidth: 80 },
                  { id: 'volume_de_ar_total', label: 'Volume Ar (m³)', minWidth: 120, align: 'right' },
                  { id: 'consumo_eletrico_total', label: 'Consumo (kWh)', minWidth: 120, align: 'right' },
                  { id: 'potencia_media_percentual', label: 'Potência (%)', minWidth: 100, align: 'right' },
                ]}
                data={relatorioData.dadosOperacionais.map(row => ({
                  ...row,
                  data_leitura_formatada: row.data_leitura_formatada ? (() => {
                    // CORREÇÃO TIMEZONE: Usar split para evitar problema de fuso horário
                    const dateParts = row.data_leitura_formatada.split('T')[0].split('-');
                    if (dateParts.length === 3) {
                      const year = parseInt(dateParts[0]);
                      const month = parseInt(dateParts[1]) - 1; // Mês é 0-indexado
                      const day = parseInt(dateParts[2]);
                      const localDate = new Date(year, month, day);
                      return localDate.toLocaleDateString('pt-BR');
                    }
                    return new Date(row.data_leitura_formatada).toLocaleDateString('pt-BR');
                  })() : 'N/A',
                  volume_de_ar_total: (parseFloat(row.volume_de_ar_total) || 0).toFixed(2),
                  consumo_eletrico_total: (parseFloat(row.consumo_eletrico_total) || 0).toFixed(2),
                  potencia_media_percentual: (parseFloat(row.potencia_media_percentual) || 0).toFixed(2),
                }))}
                loading={false}
                emptyMessage="Nenhum dado operacional encontrado para o período selecionado"
              />
            </StandardCard>
          )}


        </Box>
      )}
    </StandardContainer>
  );
};

export default RelatorioMensalPage;
