import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  AdminPanelSettings as AdminIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import { usuarioService, clienteService, produtoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useAsyncOperation } from '../contexts/LoadingContext';
import UsuarioModal from '../components/UsuarioModal';
import {
  StandardContainer,
  StandardButton,
  StandardTable,
  TableActions,
  StatusChip,
  ContentSection,
} from '../components/common';

// Todos os styled-components removidos - usando Material-UI

const UsuariosPage = () => {
  const [usuarios, setUsuarios] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedUsuario, setSelectedUsuario] = useState(null);
  const [showUsuarioModal, setShowUsuarioModal] = useState(false);
  const { user } = useAuth();
  const { executeAsync } = useAsyncOperation();

  useEffect(() => {
    loadUsuarios();
  }, []);

  const loadUsuarios = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await usuarioService.listar();
      setUsuarios(response.data);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      setError('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = () => {
    setSelectedUsuario(null);
    setShowUsuarioModal(true);
  };

  const handleEditUser = (usuario) => {
    setSelectedUsuario(usuario);
    setShowUsuarioModal(true);
  };



  const handleDeleteUser = async (usuario) => {
    const confirmMessage = `⚠️ ATENÇÃO: EXCLUSÃO PERMANENTE ⚠️

Esta ação irá DELETAR PERMANENTEMENTE o usuário:

👤 Nome: ${usuario.nome}
📧 Email: ${usuario.email}
🏷️ Tipo: ${usuario.tipo_usuario === 'admin' ? 'Administrador' : 'Cliente'}
🏢 Cliente: ${usuario.codigo_cliente || 'N/A'}
📊 Status: ${usuario.ativo ? 'Ativo' : 'Inativo'}

⚠️ IMPORTANTE:
• Esta ação NÃO PODE ser desfeita
• O usuário será removido permanentemente do sistema
• Os logs de auditoria serão preservados para manter histórico

Digite "CONFIRMAR" para prosseguir:`;

    const confirmation = prompt(confirmMessage);

    if (confirmation === 'CONFIRMAR') {
      try {
        console.log(`🗑️ Iniciando exclusão do usuário: ${usuario.email}`);

        const response = await usuarioService.deletar(usuario.id);

        // Recarregar lista de usuários
        await loadUsuarios();

        // Mostrar mensagem de sucesso com detalhes
        const successMessage = `✅ Usuário deletado com sucesso!

👤 Usuário: ${usuario.nome}
📧 Email: ${usuario.email}
${response.data?.logs_preservados ? `📝 Logs preservados: ${response.data.logs_preservados}` : ''}

O histórico de auditoria foi mantido para preservar a rastreabilidade do sistema.`;

        alert(successMessage);

      } catch (error) {
        console.error('❌ Erro ao deletar usuário:', error);

        // Tratamento específico de diferentes tipos de erro
        let errorMessage = '❌ Erro ao deletar usuário';

        if (error.response?.data?.error) {
          errorMessage = `❌ ${error.response.data.error}`;

          if (error.response.data.detalhes) {
            errorMessage += `\n\n📋 Detalhes: ${error.response.data.detalhes}`;
          }
        } else if (error.response?.status === 404) {
          errorMessage = '❌ Usuário não encontrado';
        } else if (error.response?.status === 403) {
          errorMessage = '❌ Acesso negado. Apenas administradores podem deletar usuários.';
        } else if (error.response?.status === 400) {
          errorMessage = '❌ Não é possível deletar este usuário devido a restrições do sistema';
        } else if (error.message) {
          errorMessage = `❌ Erro de conexão: ${error.message}`;
        }

        alert(errorMessage);
      }
    } else if (confirmation !== null) {
      alert('❌ Exclusão cancelada. Digite exatamente "CONFIRMAR" para prosseguir.');
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (loading) {
    return (
      <StandardContainer title="Gestão de Usuários" subtitle="Gerenciamento de usuários">
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
          <CircularProgress size={40} />
        </Box>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer title="Gestão de Usuários" subtitle="Gerenciamento de usuários">
        <Alert severity="error" sx={{ textAlign: 'center' }}>
          {error}
        </Alert>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="Gestão de Usuários"
      subtitle="Gerenciamento de usuários"
      headerAction={
        <StandardButton
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
        >
          Novo Usuário
        </StandardButton>
      }
    >
      {usuarios.length === 0 ? (
        <StandardCard sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h1" sx={{ fontSize: '2rem', mb: 2 }}>👤</Typography>
          <Typography variant="h5" sx={{ mb: 1, color: 'text.primary' }}>
            Nenhum usuário encontrado
          </Typography>
        </StandardCard>
      ) : (
        <ContentSection>
          <StandardTable
            columns={[
              {
                id: 'nome',
                label: 'Nome',
                minWidth: 200,
                render: (value) => (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <PersonIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 20 }} />
                    <Typography variant="body2" fontWeight="medium">
                      {value}
                    </Typography>
                  </Box>
                ),
              },
              {
                id: 'email',
                label: 'Email',
                minWidth: 250,
                render: (value) => (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                    <Typography variant="body2">
                      {value}
                    </Typography>
                  </Box>
                ),
              },
              {
                id: 'tipo_usuario',
                label: 'Tipo',
                minWidth: 130,
                render: (value) => (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <AdminIcon sx={{ mr: 1, color: value === 'admin' ? 'primary.main' : 'secondary.main', fontSize: 16 }} />
                    <StatusChip
                      status={value === 'admin' ? 'Administrador' : 'Cliente'}
                      color={value === 'admin' ? 'primary' : 'secondary'}
                    />
                  </Box>
                ),
              },
              {
                id: 'codigo_cliente',
                label: 'Cliente',
                minWidth: 200,
                render: (value, row) => value ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <BusinessIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                    <Typography variant="body2">
                      {row.nome_cliente || value}
                    </Typography>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    -
                  </Typography>
                ),
              },
              {
                id: 'ativo',
                label: 'Status',
                minWidth: 100,
                render: (value) => (
                  <StatusChip
                    status={value ? 'Ativo' : 'Inativo'}
                    color={value ? 'success' : 'error'}
                  />
                ),
              },
              {
                id: 'created_at',
                label: 'Criado em',
                minWidth: 120,
                render: (value) => (
                  <Typography variant="body2">
                    {formatDate(value)}
                  </Typography>
                ),
              },
              {
                id: 'actions',
                label: 'Ações',
                minWidth: 180,
                align: 'center',
                render: (_, row) => {
                  const actions = [
                    {
                      icon: <EditIcon />,
                      tooltip: 'Editar',
                      onClick: () => handleEditUser(row),
                      color: 'warning',
                    },
                    {
                      icon: <DeleteIcon />,
                      tooltip: 'Deletar',
                      onClick: () => handleDeleteUser(row),
                      color: 'error',
                    },
                  ];

                  return <TableActions actions={actions} />;
                },
              },
            ]}
            data={usuarios}
            loading={false}
            emptyMessage="Nenhum usuário encontrado"
          />
        </ContentSection>
      )}

      {/* Modal de Usuário */}
      <UsuarioModal
        isOpen={showUsuarioModal}
        onClose={() => setShowUsuarioModal(false)}
        usuario={selectedUsuario}
        onSuccess={loadUsuarios}
      />
    </StandardContainer>
  );
};

export default UsuariosPage;
