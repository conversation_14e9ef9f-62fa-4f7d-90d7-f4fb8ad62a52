import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import {
  Box,
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useClientLogo } from '../hooks/useClientLogo';
import Sidebar from '../components/Sidebar';
import Footer from '../components/Footer';
import logoSemFundo from '../assets/logo_sem_fundo_branco.png';
import DashboardGeralPage from './DashboardGeralPage';
import ClientesPage from './ClientesPage';
import UsuariosPage from './UsuariosPage';
import LogsPage from './LogsPage';
import HomePage from './HomePage';
import CadastroProdutosPage from './CadastroProdutosPage';
import GerenciamentoProdutosPage from './GerenciamentoProdutosPage';
import BookSepultamentosPage from './BookSepultamentosPage';
import BookSepultamentosDetalhePage from './BookSepultamentosDetalhePage';
import BookExumacoesPage from './BookExumacoesPage';
import BookExumacoesDetalhePage from './BookExumacoesDetalhePage';
import CadastrosProdutosPage from './CadastrosProdutosPage';
import RelatoriosPage from './RelatoriosPage';
import RelatorioMensalPage from './RelatorioMensalPage';

const DRAWER_WIDTH = 280;

const Dashboard = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const { user, canAccessClientes, canAccessUsuarios, canAccessLogs, canEditProdutos, isClient } = useAuth();
  const { clientLogo, defaultLogo } = useClientLogo();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      {/* AppBar para mobile */}
      {isMobile && (
        <AppBar
          position="fixed"
          sx={{
            width: { md: `calc(100% - ${DRAWER_WIDTH}px)` },
            ml: { md: `${DRAWER_WIDTH}px` },
            display: { xs: 'block', md: 'none' },
            backgroundColor: 'primary.main',
            boxShadow: theme.shadows[4],
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>

            {/* Logo dinâmica no AppBar mobile */}
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <img
                src={clientLogo}
                alt="Logo da Empresa"
                style={{
                  height: '32px',
                  marginRight: '12px',
                  filter: clientLogo === defaultLogo ? 'brightness(0) invert(1)' : 'none', // Aplica filtro apenas na logo padrão
                }}
              />
              <Typography variant="h6" noWrap component="div">
                Portal do Cliente
              </Typography>
            </Box>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      <Sidebar mobileOpen={mobileOpen} onMobileClose={handleDrawerToggle} />

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { md: `calc(100% - ${DRAWER_WIDTH}px)` },
          mt: { xs: 8, md: 0 }, // Margem top para mobile (AppBar)
          backgroundColor: theme.palette.background.default,
          minHeight: '100vh',
        }}
      >
        <Routes>
          <Route path="/" element={<HomePage />} />

          {/* Rotas com controle de acesso */}
          {canAccessClientes() && <Route path="/clientes" element={<ClientesPage />} />}
          {canAccessUsuarios() && <Route path="/usuarios" element={<UsuariosPage />} />}
          {canAccessLogs() && <Route path="/logs" element={<LogsPage />} />}

          {/* Book de Sepultamentos - visível apenas para clientes */}
          {!canEditProdutos() && <Route path="/book-sepultamentos" element={<BookSepultamentosPage />} />}
          {!canEditProdutos() && <Route path="/book-sepultamentos/:codigoEstacao" element={<BookSepultamentosDetalhePage />} />}

          {/* Book de Exumações - visível apenas para clientes */}
          {!canEditProdutos() && <Route path="/book-exumacoes" element={<BookExumacoesPage />} />}
          {!canEditProdutos() && <Route path="/book-exumacoes/:codigoEstacao" element={<BookExumacoesDetalhePage />} />}

          {/* Nova rota para Relatórios - visível para todos os usuários */}
          <Route path="/relatorios" element={<RelatoriosPage />} />

          {/* Nova rota para Relatório Mensal - visível apenas para admins */}
          {canEditProdutos() && <Route path="/relatorio-mensal" element={<RelatorioMensalPage />} />}

          {/* Nova rota para Cadastros dos Produtos - visível apenas para admins */}
          {canEditProdutos() && <Route path="/cadastros-produtos" element={<CadastrosProdutosPage />} />}

          {/* Redirecionar rotas não autorizadas */}
          <Route path="*" element={<Navigate to="/dashboard" />} />
        </Routes>
      </Box>
    </Box>
  );
};

export default Dashboard;
