import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Assessment as ReportIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
} from '@mui/icons-material';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { useAuth } from '../contexts/AuthContext';
import { produtoService, sepultamentoService, gavetaService, relatorioService } from '../services/api';
import {
  StandardContainer,
  StandardButton,
  StandardCard,
  StandardForm,
  StandardTextField,
  StandardSelect,
  StandardTable,
  FormGrid,
  FormGridItem,
  FormSection,
  ContentSection,
  StatusChip,
} from '../components/common';

// Configurações do PDF - Mesmas do relatório mensal
const PDF_CONFIG = {
  format: 'a4',
  unit: 'mm',
  orientation: 'portrait',
  margins: {
    top: 20,
    right: 20,
    bottom: 15,
    left: 20 // Ajustado para 20mm para acomodar a margem da imagem (10mm) + espaço (10mm)
  },
  pageWidth: 210,
  pageHeight: 297,
  contentWidth: 170, // 210 - 40 (margens esquerda + direita: 20 + 20)
  contentHeight: 262, // 297 - 35 (margens superior + inferior)
  margemImagemWidth: 10, // Largura da margem da imagem
};

// Função para carregar a imagem da margem do arquivo
async function carregarImagemMargem() {
  try {
    console.log('📄 Carregando imagem da margem do arquivo...');

    // Carregar imagem do arquivo público
    const response = await fetch('/margem_evolution.jpg');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.warn('⚠️ Erro ao carregar imagem da margem:', error);
    return null; // Retorna null se não conseguir carregar
  }
}

/**
 * Adiciona imagem da margem esquerda em todas as páginas
 * @param {jsPDF} doc - Documento PDF
 * @param {string} imagemMargem - Imagem da margem em base64
 */
function adicionarImagemMargem(doc, imagemMargem) {
  if (!imagemMargem) {
    console.warn('⚠️ Imagem da margem não disponível');
    return;
  }

  const totalPages = doc.internal.getNumberOfPages();
  console.log('📄 Adicionando imagem da margem em', totalPages, 'páginas');

  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    console.log('📄 Processando página', i, 'de', totalPages);

    try {
      // Adicionar imagem da margem ocupando toda a folha A4
      // Posição: do início (0,0) até o final da página
      // Largura: 10mm conforme especificado
      // Altura: 297mm (altura total A4)
      doc.addImage(
        imagemMargem,
        'JPEG',
        0, // Posição X: início absoluto da página
        0, // Posição Y: início absoluto da página
        PDF_CONFIG.margemImagemWidth, // Largura: 10mm (dentro do tamanho real A4)
        PDF_CONFIG.pageHeight, // Altura: 297mm (altura total A4)
        undefined,
        'NONE'
      );
      console.log('✅ Imagem da margem adicionada na página', i);
    } catch (error) {
      console.warn('⚠️ Erro ao adicionar imagem da margem na página', i, ':', error);
    }
  }
}

const RelatoriosPage = () => {
  const { user, isClient } = useAuth();
  const [produtos, setProdutos] = useState([]);
  const [selectedProduto, setSelectedProduto] = useState('');
  const [dataInicio, setDataInicio] = useState('');
  const [dataFim, setDataFim] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [relatorioData, setRelatorioData] = useState(null);

  useEffect(() => {
    loadProdutos();
  }, []);

  const loadProdutos = async () => {
    try {
      const response = await produtoService.listar();
      setProdutos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    }
  };

  const handleGerarRelatorio = async () => {
    if (!selectedProduto || !dataInicio || !dataFim) {
      setError('Por favor, selecione o produto e as datas de início e fim');
      return;
    }

    if (new Date(dataInicio) > new Date(dataFim)) {
      setError('A data de início deve ser anterior à data de fim');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // Buscar produto selecionado para obter códigos
      const produtoResponse = await produtoService.buscarCompleto(selectedProduto);
      const produto = produtoResponse.data.produto; // Corrigido: acessar produto dentro da resposta

      console.log('🔍 Produto encontrado:', produto);
      console.log('🎯 Filtrando dados por:', {
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao
      });

      // CORREÇÃO DEFINITIVA: Buscar sepultamentos usando Data Fim +1 para garantir inclusão do último dia
      const dataFimMais1 = new Date(dataFim);
      dataFimMais1.setDate(dataFimMais1.getDate() + 1);
      const dataFimMais1Str = dataFimMais1.toISOString().split('T')[0];

      console.log('🔧 CORREÇÃO DEFINITIVA - Buscando dados do backend:', {
        dataInicio: dataInicio,
        dataFimOriginal: dataFim,
        dataFimMais1: dataFimMais1Str,
        observacao: 'Backend receberá Data Fim +1 para garantir inclusão do último dia'
      });

      // Usar nova API de relatórios que inclui exumações
      const response = await relatorioService.buscarDadosSepultamentos({
        produto_id: produto.id, // CORREÇÃO: Usar ID do produto em vez de codigo_estacao
        data_inicio: dataInicio,
        data_fim: dataFimMais1Str // Usar Data Fim +1 para busca no backend
      });

      const sepultamentos = response.data.sepultamentos || [];
      const exumacoes = response.data.exumacoes || [];
      const totalExumacoesPeriodo = response.data.totalExumacoesPeriodo || 0;

      console.log('🔍 Sepultamentos do período encontrados:', sepultamentos.length);
      console.log('🔍 Exumações do período encontradas:', exumacoes.length);
      console.log('🔍 Primeiros 3 sepultamentos:', sepultamentos.slice(0, 3));

      // Buscar dados de gavetas para cálculos de ocupação
      const gavetasResponse = await gavetaService.listarPorProduto({
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao
      });

      const gavetas = gavetasResponse.data || [];
      console.log('🔍 Gavetas encontradas:', gavetas.length);

      // Buscar TODOS os sepultamentos ativos (não exumados) até a data fim para cálculo de ocupação
      const sepultamentosAtivosResponse = await sepultamentoService.listar({
        codigo_cliente: produto.codigo_cliente,
        codigo_estacao: produto.codigo_estacao,
        data_fim: dataFim, // Apenas até a data fim
        incluir_todos_ate_data_fim: true // Flag para incluir todos até a data fim
      });

      const sepultamentosAtivos = sepultamentosAtivosResponse.data || [];
      console.log('🔍 Sepultamentos ativos até data fim:', sepultamentosAtivos.length);

      // Processar dados para o relatório com novos cálculos incluindo exumações
      const relatorio = processarDadosRelatorio(sepultamentos, gavetas, sepultamentosAtivos, produto, dataInicio, dataFim, exumacoes, totalExumacoesPeriodo);
      setRelatorioData(relatorio);

    } catch (error) {
      console.error('Erro ao gerar relatório:', error);

      // Tratamento específico de erros
      let errorMessage = 'Erro ao gerar relatório';

      if (error.response) {
        // Erro da API
        const status = error.response.status;
        const data = error.response.data;

        if (status === 404) {
          errorMessage = 'Produto não encontrado. Verifique se o produto selecionado é válido.';
        } else if (status === 400) {
          errorMessage = `Dados inválidos: ${data.details || data.error || 'Verifique os parâmetros informados'}`;
        } else if (status === 500) {
          errorMessage = 'Erro interno do servidor. Tente novamente em alguns instantes.';
        } else {
          errorMessage = `Erro ${status}: ${data.error || 'Erro desconhecido'}`;
        }
      } else if (error.request) {
        // Erro de rede
        errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const processarDadosRelatorio = (sepultamentos, gavetas, sepultamentosAtivos, produto, inicio, fim, exumacoes = [], totalExumacoesPeriodo = 0) => {
    // FUNÇÃO UTILITÁRIA: Extrair data string sem problemas de timezone (Brasil)
    const extrairDataString = (dateValue) => {
      const dataString = dateValue.toString();
      if (dataString.includes('T')) {
        // Formato ISO: 2025-07-04T00:00:00.000Z
        return dataString.split('T')[0];
      } else if (dataString.includes(' ')) {
        // Formato PostgreSQL: 2025-07-04 00:00:00
        return dataString.split(' ')[0];
      } else {
        // Formato apenas data: 2025-07-04
        return dataString;
      }
    };

    // Trabalhar apenas com strings de data para evitar problemas de fuso horário
    const dataInicioStr = inicio; // Formato YYYY-MM-DD
    const dataFimStr = fim; // Formato YYYY-MM-DD

    // Criar objetos Date apenas para cálculos de diferença
    const dataInicioObj = new Date(inicio + 'T00:00:00');
    const dataFimObj = new Date(fim + 'T00:00:00');

    // Calcular quantidade de dias no período ORIGINAL (para exibição)
    const diffTime = Math.abs(dataFimObj - dataInicioObj);
    const diffDaysOriginal = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir o último dia

    // NOVA LÓGICA: Criar variável "Data Corrigida" = "Data de Fim" + 1 dia
    const dataCorrigida = new Date(dataFimStr);
    dataCorrigida.setDate(dataCorrigida.getDate() + 1); // Data Corrigida = Data Fim + 1 dia
    const dataCorrigidaStr = dataCorrigida.toISOString().split('T')[0]; // Formato YYYY-MM-DD

    // NOVA LÓGICA: Criar array de dias incluindo Data Fim +1 para ambas as tabelas
    const diasPeriodo = [];
    const [anoInicio, mesInicio, diaInicio] = dataInicioStr.split('-').map(Number);
    const [anoCorrigida, mesCorrigida, diaCorrigida] = dataCorrigidaStr.split('-').map(Number);

    let currentDate = new Date(anoInicio, mesInicio - 1, diaInicio); // Mês é 0-indexado
    const endDateComDataFimMais1 = new Date(anoCorrigida, mesCorrigida - 1, diaCorrigida); // Data Fim +1

    while (currentDate <= endDateComDataFimMais1) {
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      diasPeriodo.push(`${year}-${month}-${day}`);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log('🔧 Período solicitado (CORREÇÃO DEFINITIVA - BACKEND COM DATA FIM +1):', {
      inicio: dataInicioStr,
      fim: dataFimStr,
      dataCorrigida: dataCorrigidaStr,
      inicioFormatado: dataInicioObj.toLocaleDateString('pt-BR'),
      fimFormatado: dataFimObj.toLocaleDateString('pt-BR'),
      dataCorrigidaFormatada: dataCorrigida.toLocaleDateString('pt-BR'),
      totalDiasComDataFimMais1: diasPeriodo.length,
      diasPeriodo: diasPeriodo.slice(0, 5).concat(['...', diasPeriodo[diasPeriodo.length - 1]]),
      dataInicioCompleta: new Date(dataInicioStr + 'T00:00:00').toISOString(),
      dataFimCompleta: new Date(dataFimStr + 'T23:59:59').toISOString(),
      dataCorrigidaCompleta: new Date(dataCorrigidaStr + 'T23:59:59').toISOString(),
      observacao: 'CORREÇÃO DEFINITIVA: Backend busca com Data Fim +1, tabelas incluem Data Fim +1, Resumo por Dia esconde último registro'
    });

    // CORREÇÃO TIMEZONE BRASIL: Filtrar sepultamentos usando apenas comparação de strings de data
    const sepultamentosFiltrados = sepultamentos.filter(sep => {
      const dataSepultamento = extrairDataString(sep.data_sepultamento);
      return dataSepultamento >= dataInicioStr && dataSepultamento <= dataFimStr;
    });

    // LÓGICA REMOVIDA: Não é mais necessária com a correção de timezone

    // Agrupar sepultamentos por dia - CORREÇÃO TIMEZONE BRASIL
    const sepultamentosPorDia = {};
    sepultamentosFiltrados.forEach(sep => {
      const dataSep = extrairDataString(sep.data_sepultamento);
      if (!sepultamentosPorDia[dataSep]) {
        sepultamentosPorDia[dataSep] = [];
      }
      sepultamentosPorDia[dataSep].push(sep);
    });

    // Agrupar exumações por dia - CORREÇÃO TIMEZONE BRASIL
    const exumacoesPorDia = {};
    exumacoes.forEach(exumacao => {
      const dataExum = extrairDataString(exumacao.exumado_em);
      if (!exumacoesPorDia[dataExum]) {
        exumacoesPorDia[dataExum] = [];
      }
      exumacoesPorDia[dataExum].push(exumacao);
    });

    console.log('🔍 Debug filtros de data (COM HORÁRIO COMPLETO):', {
      sepultamentosTotais: sepultamentos.length,
      sepultamentosFiltrados: sepultamentosFiltrados.length,
      primeiroSepultamentoFiltrado: sepultamentosFiltrados[0]?.data_sepultamento,
      ultimoSepultamentoFiltrado: sepultamentosFiltrados[sepultamentosFiltrados.length - 1]?.data_sepultamento,
      diasComSepultamentos: Object.keys(sepultamentosPorDia).sort(),
      sepultamentosUltimoDia: sepultamentosPorDia[dataFimStr]?.length || 0,
      sepultamentosUltimoDiaDetalhes: sepultamentosPorDia[dataFimStr]?.map(s => ({
        nome: s.nome_sepultado,
        data: s.data_sepultamento,
        horario: s.horario_sepultamento
      })) || []
    });

    // Criar visão geral por dia - CORREÇÃO APLICADA: diasPeriodo inclui Data Corrigida + Exumações
    const visaoGeralCompleta = diasPeriodo.map(diaStr => {
      const sepultamentosDia = sepultamentosPorDia[diaStr] || [];
      const exumacoesDia = exumacoesPorDia[diaStr] || [];

      // Converter string para Date apenas para formatação
      const [ano, mes, dia] = diaStr.split('-').map(Number);
      const dataObj = new Date(ano, mes - 1, dia);

      return {
        data: diaStr,
        dataFormatada: dataObj.toLocaleDateString('pt-BR'),
        quantidade: sepultamentosDia.length,
        quantidadeExumacoes: exumacoesDia.length,
        totalMovimentacoes: sepultamentosDia.length + exumacoesDia.length,
        status: sepultamentosDia.length > 0 || exumacoesDia.length > 0
          ? `${sepultamentosDia.length} sepultamento(s)${exumacoesDia.length > 0 ? ` e ${exumacoesDia.length} exumação(ões)` : ''}`
          : 'Nenhum movimento',
        sepultamentos: sepultamentosDia,
        exumacoes: exumacoesDia
      };
    });

    // NOVA LÓGICA: Tabela "Resumo por Dia" esconde último registro (Data Fim +1)
    // Tabela "Todos os Sepultamentos" mantém todos os dados incluindo Data Fim +1
    // Requisito: Dados até Data Fim +1, mas Resumo por Dia esconde último dia
    const visaoGeral = visaoGeralCompleta.slice(0, -1); // Remove último elemento (Data Fim +1) da tabela Resumo por Dia

    console.log('🔧 Debug visão geral (CORREÇÃO DEFINITIVA - BACKEND COM DATA FIM +1):', {
      totalDiasVisaoGeralCompleta: visaoGeralCompleta.length,
      totalDiasVisaoGeralTabela: visaoGeral.length,
      primeiroDiaVisaoGeral: visaoGeral[0]?.data,
      ultimoDiaVisaoGeral: visaoGeral[visaoGeral.length - 1]?.data,
      ultimoDiaOcultoResumo: visaoGeralCompleta[visaoGeralCompleta.length - 1]?.data,
      periodoUsuario: `${dataInicioStr} até ${dataFimStr}`,
      periodoTabelas: `${dataInicioStr} até ${dataCorrigidaStr}`,
      diasComQuantidade: visaoGeral.filter(d => d.quantidade > 0).map(d => ({ data: d.data, dataFormatada: d.dataFormatada, quantidade: d.quantidade })),
      totalSepultamentosVisaoGeral: visaoGeral.reduce((sum, d) => sum + d.quantidade, 0),
      totalSepultamentosCompletos: visaoGeralCompleta.reduce((sum, d) => sum + d.quantidade, 0),
      sepultamentosPorDiaKeys: Object.keys(sepultamentosPorDia).sort(),
      observacao: 'CORREÇÃO DEFINITIVA APLICADA: Backend busca com Data Fim +1, dados até Data Fim +1, Resumo por Dia esconde último registro'
    });

    // Ordenar sepultamentos por data crescente - CORREÇÃO TIMEZONE BRASIL
    const todosSepultamentos = sepultamentosFiltrados.sort((a, b) => {
      const dataA = extrairDataString(a.data_sepultamento);
      const dataB = extrairDataString(b.data_sepultamento);
      return dataA.localeCompare(dataB);
    });

    // Criar variável específica para tabela "Todos os Sepultamentos" com dados completos
    const visaoGeralCompleta_TodosSepultamentos = visaoGeralCompleta; // Mantém todos os dados incluindo Data Fim +1

    // CORREÇÃO FINAL: Indicadores usam apenas dias originais, dados usam +1 dia
    const totalSepultamentosPeriodo = sepultamentosFiltrados.length;
    const taxaSepultamentosPorDia = diffDaysOriginal > 0 ? (totalSepultamentosPeriodo / diffDaysOriginal).toFixed(2) : 0;

    // CORREÇÃO COMPLETA: Debug específico para mostrar diferença entre período exibido e cálculos
    console.log('🎯 CORREÇÃO COMPLETA - Diferença entre período exibido e cálculos:', {
      periodoExibido: {
        inicio: dataInicioStr,
        fim: dataFimStr,
        diasOriginais: diffDaysOriginal,
        textoCompleto: `${dataInicioObj.toLocaleDateString('pt-BR')} a ${dataFimObj.toLocaleDateString('pt-BR')} - ${diffDaysOriginal} dias`
      },
      calculosIndicadores: {
        inicio: dataInicioStr,
        fim: dataFimStr,
        diasOriginais: diffDaysOriginal,
        textoCompleto: `${dataInicioObj.toLocaleDateString('pt-BR')} a ${dataFimObj.toLocaleDateString('pt-BR')} - ${diffDaysOriginal} dias (para indicadores)`
      },
      indicadores: {
        totalSepultamentos: totalSepultamentosPeriodo,
        taxaSepultamentosPorDia: `${totalSepultamentosPeriodo} ÷ ${diffDaysOriginal} = ${taxaSepultamentosPorDia}`
      },
      observacao: 'CORREÇÃO DEFINITIVA IMPLEMENTADA: Backend busca com Data Fim +1, dados até Data Fim +1, Resumo por Dia esconde último registro'
    });

    // NOVOS CÁLCULOS DE GAVETAS CORRIGIDOS
    const totalGavetas = gavetas.length;

    // Gavetas ocupadas = Total de sepultamentos ativos (não exumados) até Data Fim - CORREÇÃO TIMEZONE BRASIL
    const sepultamentosAtivosAteFim = sepultamentosAtivos.filter(sep => {
      const dataSepultamento = extrairDataString(sep.data_sepultamento);
      return dataSepultamento <= dataFimStr && !sep.status_exumacao && sep.ativo !== false;
    });

    const gavetasOcupadas = sepultamentosAtivosAteFim.length;

    // Gavetas disponíveis = Total de gavetas - Gavetas ocupadas
    const gavetasDisponiveis = totalGavetas - gavetasOcupadas;

    // Taxa de ocupação = (Gavetas ocupadas / Total de gavetas) * 100
    const taxaOcupacao = totalGavetas > 0 ? ((gavetasOcupadas / totalGavetas) * 100).toFixed(1) : 0;

    return {
      visaoGeral, // Resumo por Dia (esconde último registro)
      visaoGeralCompleta, // Dados completos incluindo Data Fim +1
      todosSepultamentos, // Todos os sepultamentos (incluindo Data Fim +1)
      totalSepultamentos: totalSepultamentosPeriodo, // Apenas do período
      totalSepultamentosPeriodo, // Novo campo específico
      taxaSepultamentosPorDia, // Novo cálculo (usa diffDaysOriginal)
      diffDays: diffDaysOriginal, // Quantidade de dias ORIGINAL (para exibição e indicadores)
      // Novos dados de gavetas
      totalGavetas,
      gavetasOcupadas,
      totalExumacoesPeriodo, // Substituir gavetasDisponiveis por totalExumacoesPeriodo
      taxaOcupacao,
      produto: {
        denominacao: produto?.denominacao || produto?.nome || 'Produto não identificado',
        nome_completo: produto?.denominacao || produto?.nome || 'Produto não identificado',
        codigo_cliente: produto?.codigo_cliente,
        codigo_estacao: produto?.codigo_estacao,
        // Debug: incluir dados brutos para verificação
        _debug_produto: produto
      },
      periodo: {
        inicio: dataInicioObj.toLocaleDateString('pt-BR'),
        fim: dataFimObj.toLocaleDateString('pt-BR'),
        // CORREÇÃO COMPLETA: Período exibido usa datas originais (conforme inserido pelo usuário)
        textoCompleto: `${dataInicioObj.toLocaleDateString('pt-BR')} a ${dataFimObj.toLocaleDateString('pt-BR')} - Total de ${diffDaysOriginal} Dias`
      }
    };
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';

    // CORREÇÃO TIMEZONE: Usar split para evitar problema de fuso horário
    // Problema: new Date(dateString) converte para UTC e subtrai 1 dia
    // Solução: Fazer parse manual da data no formato YYYY-MM-DD
    const dateParts = dateString.split('T')[0].split('-');
    if (dateParts.length === 3) {
      const year = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]) - 1; // Mês é 0-indexado
      const day = parseInt(dateParts[2]);
      const localDate = new Date(year, month, day);
      return localDate.toLocaleDateString('pt-BR');
    }

    // Fallback para formato antigo (não deveria acontecer)
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatTime = (timeString) => {
    if (!timeString) return '-';
    return timeString.substring(0, 5); // HH:MM
  };

  const handleEmitirPDF = async () => {
    if (!relatorioData) return;

    try {
      console.log('📄 Iniciando geração do PDF de relatório de sepultamentos...');

      // Carregar imagem da margem
      console.log('📄 Carregando imagem da margem...');
      const imagemMargem = await carregarImagemMargem();
      console.log('📄 Imagem da margem carregada:', imagemMargem ? 'Sucesso' : 'Falhou');

      // Criar PDF com texto nativo para melhor qualidade
      const pdf = new jsPDF(PDF_CONFIG.orientation, PDF_CONFIG.unit, PDF_CONFIG.format);
      const pageWidth = PDF_CONFIG.pageWidth; // A4 width in mm
      const pageHeight = PDF_CONFIG.pageHeight; // A4 height in mm
      let currentY = 40; // Posição Y inicial (após cabeçalho)
      let pageNumber = 1;

      // Função para adicionar cabeçalho e rodapé otimizada
      const addHeaderFooter = (pdf, pageNum, totalPages) => {
        // Cabeçalho
        pdf.setFontSize(10);
        pdf.setTextColor(100, 100, 100);
        pdf.text('Portal Evolution - Sistema de Gestão de Sepultamentos', PDF_CONFIG.margins.left, 15);
        pdf.text(`${relatorioData.produto?.nome_completo || 'N/A'}`, PDF_CONFIG.margins.left, 25);

        // Linha separadora do cabeçalho
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(0.5);
        pdf.line(PDF_CONFIG.margins.left, 30, pageWidth - PDF_CONFIG.margins.right, 30);

        // Rodapé
        pdf.setFontSize(8);
        pdf.setTextColor(150, 150, 150);
        const footerY = 285;

        // Linha separadora do rodapé
        pdf.line(PDF_CONFIG.margins.left, footerY - 5, pageWidth - PDF_CONFIG.margins.right, footerY - 5);

        // Informações do rodapé
        pdf.text(`Página ${pageNum}`, PDF_CONFIG.margins.left, footerY);
        pdf.text(`Gerado em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}`, 105, footerY, { align: 'center' });
        pdf.text(`${relatorioData.periodo.textoCompleto}`, pageWidth - PDF_CONFIG.margins.right, footerY, { align: 'right' });
      };

      // Carregar logo para marca d'água
      const logoImg = new Image();
      logoImg.crossOrigin = 'anonymous';

      const generatePDFContent = () => {
        // Adicionar cabeçalho e rodapé na primeira página
        addHeaderFooter(pdf, pageNumber, 1);

        // Título principal
        pdf.setFontSize(20);
        pdf.setTextColor(25, 118, 210);
        pdf.text('Relatório de Sepultamentos', 105, currentY, { align: 'center' });
        currentY += 15;

        // Informações do produto e período
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text(`Produto: ${relatorioData.produto?.nome_completo || 'N/A'}`, PDF_CONFIG.margins.left, currentY);
        currentY += 8;
        pdf.text(`Período: ${relatorioData.periodo.textoCompleto}`, PDF_CONFIG.margins.left, currentY);
        currentY += 15;

        // Linha separadora
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(1);
        pdf.line(PDF_CONFIG.margins.left, currentY, pageWidth - PDF_CONFIG.margins.right, currentY);
        currentY += 15;

        // Título do Resumo Executivo
        pdf.setFontSize(16);
        pdf.setTextColor(25, 118, 210);
        pdf.text('Resumo Executivo', PDF_CONFIG.margins.left, currentY);
        currentY += 15;

        // Criar grid de resumo executivo com retângulos
        const resumoData = [
          { valor: relatorioData.totalSepultamentosPeriodo, label: 'Total de Sepultamentos no Período' },
          { valor: relatorioData.taxaSepultamentosPorDia, label: 'Taxa de Sepultamentos por Dia' },
          { valor: `${relatorioData.taxaOcupacao}%`, label: 'Taxa de Ocupação Final' },
          { valor: relatorioData.totalGavetas, label: 'Total de Gavetas' },
          { valor: relatorioData.gavetasOcupadas, label: 'Gavetas Ocupadas' },
          { valor: relatorioData.totalExumacoesPeriodo, label: 'Total de Exumações no Período' }
        ];

        // Desenhar grid 3x2
        let gridX = PDF_CONFIG.margins.left;
        let gridY = currentY;
        const boxWidth = 56;
        const boxHeight = 25;
        const spacing = 5;

        resumoData.forEach((item, index) => {
          const col = index % 3;
          const row = Math.floor(index / 3);
          const x = gridX + col * (boxWidth + spacing);
          const y = gridY + row * (boxHeight + spacing + 5);

          // Desenhar retângulo de fundo
          pdf.setFillColor(245, 245, 245);
          pdf.rect(x, y, boxWidth, boxHeight, 'F');

          // Desenhar borda
          pdf.setDrawColor(200, 200, 200);
          pdf.rect(x, y, boxWidth, boxHeight);

          // Valor principal
          pdf.setFontSize(14);
          pdf.setTextColor(25, 118, 210);
          pdf.text(String(item.valor), x + boxWidth/2, y + 12, { align: 'center' });

          // Label
          pdf.setFontSize(8);
          pdf.setTextColor(100, 100, 100);
          const lines = pdf.splitTextToSize(item.label, boxWidth - 4);
          pdf.text(lines, x + boxWidth/2, y + 18, { align: 'center' });
        });

        currentY += 85; // Espaço após o grid - AUMENTADO para melhor separação visual
      };

      // Função para desenhar tabela "Resumo por Dia"
      const drawResumoPorDiaTable = () => {
        // Título da seção
        pdf.setFontSize(16);
        pdf.setTextColor(25, 118, 210);
        pdf.text('Resumo por Dia', PDF_CONFIG.margins.left, currentY);
        currentY += 5;

        // Linha separadora do título
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(0.5);
        pdf.line(PDF_CONFIG.margins.left, currentY, pageWidth - PDF_CONFIG.margins.right, currentY);
        currentY += 15;

        // Configurações da tabela
        const tableWidth = PDF_CONFIG.contentWidth; // Largura total da tabela
        const colWidths = [51, 51, 68]; // Larguras das colunas: Data, Total de Movimentações, Status
        const rowHeight = 8;
        const headerHeight = 12;

        let tableX = PDF_CONFIG.margins.left;
        let tableY = currentY;

        // Desenhar cabeçalho da tabela
        pdf.setFillColor(25, 118, 210);
        pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');

        // Texto do cabeçalho
        pdf.setFontSize(10);
        pdf.setTextColor(255, 255, 255);
        pdf.text('Data', tableX + colWidths[0]/2, tableY + 8, { align: 'center' });
        pdf.text('Total de Movimentações', tableX + colWidths[0] + colWidths[1]/2, tableY + 8, { align: 'center' });
        pdf.text('Status', tableX + colWidths[0] + colWidths[1] + colWidths[2]/2, tableY + 8, { align: 'center' });

        tableY += headerHeight;

        // Desenhar linhas de dados
        pdf.setTextColor(0, 0, 0);
        pdf.setFontSize(9);

        relatorioData.visaoGeral.forEach((dia, index) => {
          // Verificar se precisa de nova página
          if (tableY + rowHeight > 270) {
            pdf.addPage();
            pageNumber++;
            addHeaderFooter(pdf, pageNumber, 1);
            tableY = 45;

            // Repetir cabeçalho na nova página
            pdf.setFillColor(25, 118, 210);
            pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');
            pdf.setFontSize(10);
            pdf.setTextColor(255, 255, 255);
            pdf.text('Data', tableX + colWidths[0]/2, tableY + 8, { align: 'center' });
            pdf.text('Total de Movimentações', tableX + colWidths[0] + colWidths[1]/2, tableY + 8, { align: 'center' });
            pdf.text('Status', tableX + colWidths[0] + colWidths[1] + colWidths[2]/2, tableY + 8, { align: 'center' });
            tableY += headerHeight;
            pdf.setTextColor(0, 0, 0);
            pdf.setFontSize(9);
          }

          // Alternar cor de fundo das linhas
          if (index % 2 === 0) {
            pdf.setFillColor(249, 249, 249);
            pdf.rect(tableX, tableY, tableWidth, rowHeight, 'F');
          }

          // Desenhar bordas das células
          pdf.setDrawColor(221, 221, 221);
          pdf.rect(tableX, tableY, colWidths[0], rowHeight);
          pdf.rect(tableX + colWidths[0], tableY, colWidths[1], rowHeight);
          pdf.rect(tableX + colWidths[0] + colWidths[1], tableY, colWidths[2], rowHeight);

          // Conteúdo das células
          pdf.text(dia.dataFormatada, tableX + colWidths[0]/2, tableY + 5, { align: 'center' });

          // Total de Movimentações em azul e negrito
          pdf.setTextColor(25, 118, 210);
          pdf.setFont(undefined, 'bold');
          pdf.text(String(dia.totalMovimentacoes), tableX + colWidths[0] + colWidths[1]/2, tableY + 5, { align: 'center' });

          // Status
          pdf.setTextColor(0, 0, 0);
          pdf.setFont(undefined, 'normal');
          pdf.text(dia.status, tableX + colWidths[0] + colWidths[1] + colWidths[2]/2, tableY + 5, { align: 'center' });

          tableY += rowHeight;
        });

        currentY = tableY + 20;
      };

      // Função para desenhar tabela "Todos os Sepultamentos" com nomes completos
      const drawSepultamentosTable = () => {
        // Nova página para a tabela de sepultamentos
        pdf.addPage();
        pageNumber++;
        addHeaderFooter(pdf, pageNumber, 1);

        let tableY = 45;

        // Título da seção
        pdf.setFontSize(16);
        pdf.setTextColor(25, 118, 210);
        pdf.text(`Todos os Movimentos do Período (${relatorioData.todosSepultamentos.length})`, PDF_CONFIG.margins.left, tableY);
        tableY += 5;

        // Linha separadora do título
        pdf.setDrawColor(25, 118, 210);
        pdf.setLineWidth(0.5);
        pdf.line(PDF_CONFIG.margins.left, tableY, pageWidth - PDF_CONFIG.margins.right, tableY);
        tableY += 15;

        // Configurações da tabela com larguras otimizadas
        const tableWidth = 170;
        const colWidths = [25, 15, 70, 25, 20, 15]; // Data, Hora, Nome, Bloco, Gaveta, Status
        const baseRowHeight = 8;
        const headerHeight = 12;

        let tableX = 20;

        // Desenhar cabeçalho da tabela
        pdf.setFillColor(25, 118, 210);
        pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');

        // Texto do cabeçalho
        pdf.setFontSize(9);
        pdf.setTextColor(255, 255, 255);
        let headerX = tableX;
        pdf.text('Data', headerX + colWidths[0]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[0];
        pdf.text('Hora', headerX + colWidths[1]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[1];
        pdf.text('Nome do Sepultado', headerX + colWidths[2]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[2];
        pdf.text('Bloco', headerX + colWidths[3]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[3];
        pdf.text('Gaveta', headerX + colWidths[4]/2, tableY + 8, { align: 'center' });
        headerX += colWidths[4];
        pdf.text('Status', headerX + colWidths[5]/2, tableY + 8, { align: 'center' });

        tableY += headerHeight;

        // Desenhar linhas de dados
        pdf.setTextColor(0, 0, 0);
        pdf.setFontSize(8);

        relatorioData.todosSepultamentos.forEach((sep, index) => {
          // Calcular altura necessária para o nome (com quebra de linha)
          const nomeCompleto = sep.nome_sepultado || 'N/A';
          const nomeLines = pdf.splitTextToSize(nomeCompleto, colWidths[2] - 2);
          const blocoCompleto = sep.bloco_denominacao || sep.codigo_bloco || 'N/A';
          const blocoLines = pdf.splitTextToSize(blocoCompleto, colWidths[3] - 2);

          const maxLines = Math.max(nomeLines.length, blocoLines.length, 1);
          const rowHeight = Math.max(baseRowHeight, maxLines * 4 + 4);

          // Verificar se precisa de nova página
          if (tableY + rowHeight > 270) {
            pdf.addPage();
            pageNumber++;
            addHeaderFooter(pdf, pageNumber, 1);
            tableY = 45;

            // Repetir cabeçalho na nova página
            pdf.setFillColor(25, 118, 210);
            pdf.rect(tableX, tableY, tableWidth, headerHeight, 'F');
            pdf.setFontSize(9);
            pdf.setTextColor(255, 255, 255);
            let headerX = tableX;
            pdf.text('Data', headerX + colWidths[0]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[0];
            pdf.text('Hora', headerX + colWidths[1]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[1];
            pdf.text('Nome do Sepultado', headerX + colWidths[2]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[2];
            pdf.text('Bloco', headerX + colWidths[3]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[3];
            pdf.text('Gaveta', headerX + colWidths[4]/2, tableY + 8, { align: 'center' });
            headerX += colWidths[4];
            pdf.text('Status', headerX + colWidths[5]/2, tableY + 8, { align: 'center' });
            tableY += headerHeight;
            pdf.setTextColor(0, 0, 0);
            pdf.setFontSize(8);
          }

          // Alternar cor de fundo das linhas
          if (index % 2 === 0) {
            pdf.setFillColor(249, 249, 249);
            pdf.rect(tableX, tableY, tableWidth, rowHeight, 'F');
          }

          // Desenhar bordas das células
          pdf.setDrawColor(221, 221, 221);
          let cellX = tableX;
          for (let i = 0; i < colWidths.length; i++) {
            pdf.rect(cellX, tableY, colWidths[i], rowHeight);
            cellX += colWidths[i];
          }

          // Conteúdo das células
          cellX = tableX;
          const cellY = tableY + 5;

          // Data
          pdf.text(formatDate(sep.data_sepultamento), cellX + colWidths[0]/2, cellY, { align: 'center' });
          cellX += colWidths[0];

          // Hora
          pdf.text(formatTime(sep.horario_sepultamento) || '-', cellX + colWidths[1]/2, cellY, { align: 'center' });
          cellX += colWidths[1];

          // Nome completo com quebra de linha
          pdf.text(nomeLines, cellX + 1, cellY);
          cellX += colWidths[2];

          // Bloco completo com quebra de linha
          pdf.text(blocoLines, cellX + 1, cellY);
          cellX += colWidths[3];

          // Gaveta
          pdf.text(String(sep.numero_gaveta || 'N/A'), cellX + colWidths[4]/2, cellY, { align: 'center' });
          cellX += colWidths[4];

          // Status
          pdf.text(sep.status_exumacao ? 'Exumado' : 'Ativo', cellX + colWidths[5]/2, cellY, { align: 'center' });

          tableY += rowHeight;
        });
      };

      // Função principal para gerar o PDF
      const generateCompletePDF = () => {
        // Gerar primeira página com resumo
        generatePDFContent();

        // Gerar tabela resumo por dia
        drawResumoPorDiaTable();

        // Gerar tabela de sepultamentos se houver dados
        if (relatorioData.todosSepultamentos.length > 0) {
          drawSepultamentosTable();
        }

        // Adicionar imagem da margem em todas as páginas
        adicionarImagemMargem(pdf, imagemMargem);

        // Adicionar marca d'água em todas as páginas
        const totalPages = pdf.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
          pdf.setPage(i);

          // Adicionar marca d'água
          pdf.setGState(new pdf.GState({opacity: 0.1}));
          pdf.addImage(logoImg, 'PNG', 52.5, 100, 105, 105); // Centralizado
          pdf.setGState(new pdf.GState({opacity: 1}));
        }

        // Salvar PDF
        const fileName = `relatorio_sepultamentos_${relatorioData.produto?.denominacao?.replace(/[^a-zA-Z0-9]/g, '_') || 'produto'}_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);
      };

      // Carregar logo e gerar PDF
      logoImg.onload = () => {
        generateCompletePDF();
      };

      logoImg.onerror = () => {
        // Se não conseguir carregar a logo, gerar PDF sem marca d'água
        console.warn('Não foi possível carregar a logo para marca d\'água');
        generateCompletePDF();
      };

      // Tentar carregar a logo
      logoImg.src = '/logo_sem_fundo_branco.png';

    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      alert('Erro ao gerar PDF. Tente novamente.');
    }
  };

  const handleExportarXLSX = () => {
    if (!relatorioData || !relatorioData.todosSepultamentos || relatorioData.todosSepultamentos.length === 0) {
      alert('Não há dados para exportar. Gere um relatório primeiro.');
      return;
    }

    try {
      console.log('📊 Iniciando exportação XLSX...');

      // Preparar dados da tabela "Todos os Movimentos do Período"
      const dadosParaExportar = relatorioData.todosSepultamentos.map((sepultamento, index) => {
        return {
          'Nº': index + 1,
          'Data': formatDate(sepultamento.data_sepultamento),
          'Hora': formatTime(sepultamento.horario_sepultamento),
          'Nome do Sepultado': sepultamento.nome_sepultado,
          'Bloco': sepultamento.bloco_denominacao || 'N/A',
          'Gaveta': sepultamento.numero_gaveta || 'N/A',
          'Status': sepultamento.status_exumacao ? 'Exumado' : 'Ativo'
        };
      });

      // Criar workbook
      const workbook = XLSX.utils.book_new();

      // Criar worksheet com os dados
      const worksheet = XLSX.utils.json_to_sheet(dadosParaExportar);

      // Configurar larguras das colunas
      const columnWidths = [
        { wch: 5 },   // Nº
        { wch: 12 },  // Data
        { wch: 8 },   // Hora
        { wch: 30 },  // Nome do Sepultado
        { wch: 20 },  // Bloco
        { wch: 8 },   // Gaveta
        { wch: 10 }   // Status
      ];
      worksheet['!cols'] = columnWidths;

      // Adicionar informações do cabeçalho
      const headerInfo = [
        [`Relatório de Sepultamentos`],
        [`Produto: ${relatorioData.produto?.nome_completo || 'N/A'}`],
        [`Período: ${relatorioData.periodo.textoCompleto}`],
        [`Total de Movimentos: ${relatorioData.todosSepultamentos.length}`],
        [`Gerado em: ${new Date().toLocaleString('pt-BR')}`],
        [], // Linha vazia
      ];

      // Inserir cabeçalho no início da planilha
      XLSX.utils.sheet_add_aoa(worksheet, headerInfo, { origin: 'A1' });

      // Ajustar a posição dos dados (começar após o cabeçalho)
      const dadosComCabecalho = [
        ['Nº', 'Data', 'Hora', 'Nome do Sepultado', 'Bloco', 'Gaveta', 'Status'],
        ...dadosParaExportar.map(item => [
          item['Nº'],
          item['Data'],
          item['Hora'],
          item['Nome do Sepultado'],
          item['Bloco'],
          item['Gaveta'],
          item['Status']
        ])
      ];

      XLSX.utils.sheet_add_aoa(worksheet, dadosComCabecalho, { origin: 'A7' });

      // Adicionar worksheet ao workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Todos os Movimentos');

      // Gerar nome do arquivo
      const fileName = `relatorio_movimentos_${relatorioData.produto?.denominacao?.replace(/[^a-zA-Z0-9]/g, '_') || 'produto'}_${new Date().toISOString().split('T')[0]}.xlsx`;

      // Salvar arquivo
      XLSX.writeFile(workbook, fileName);

      console.log('✅ Exportação XLSX concluída:', fileName);

    } catch (error) {
      console.error('Erro ao exportar XLSX:', error);
      alert('Erro ao exportar arquivo XLSX. Tente novamente.');
    }
  };

  return (
    <StandardContainer
      title="Relatórios de Sepultamentos"
      subtitle="Gere relatórios detalhados dos sepultamentos por produto e período selecionado"
    >
      {/* Seção de Filtros */}
      <ContentSection title="Filtros do Relatório" subtitle="Configure os parâmetros para gerar seu relatório">
        <StandardForm>
          <FormSection title="Seleção de Dados">
            <FormGrid spacing={3}>
              <FormGridItem xs={12} md={4}>
                <StandardSelect
                  label="Produto"
                  value={selectedProduto}
                  onChange={(e) => setSelectedProduto(e.target.value)}
                  options={produtos.map(produto => ({
                    value: produto.id,
                    label: produto.denominacao || produto.nome
                  }))}
                  placeholder="Selecione um produto"
                  required
                  error={!selectedProduto && error}
                  helperText={!selectedProduto && error ? "Produto é obrigatório" : ""}
                />
              </FormGridItem>

              <FormGridItem xs={12} md={3}>
                <StandardTextField
                  label="Data de Início"
                  type="date"
                  value={dataInicio}
                  onChange={(e) => setDataInicio(e.target.value)}
                  required
                  InputLabelProps={{ shrink: true }}
                  error={!dataInicio && error}
                  helperText={!dataInicio && error ? "Data de início é obrigatória" : ""}
                />
              </FormGridItem>

              <FormGridItem xs={12} md={3}>
                <StandardTextField
                  label="Data de Fim"
                  type="date"
                  value={dataFim}
                  onChange={(e) => setDataFim(e.target.value)}
                  required
                  InputLabelProps={{ shrink: true }}
                  error={!dataFim && error}
                  helperText={!dataFim && error ? "Data de fim é obrigatória" : ""}
                />
              </FormGridItem>

              <FormGridItem xs={12} md={2}>
                <StandardButton
                  variant="contained"
                  onClick={handleGerarRelatorio}
                  disabled={loading}
                  fullWidth
                  startIcon={loading ? <CircularProgress size={20} /> : <ReportIcon />}
                  sx={{ height: '48px' }}
                >
                  {loading ? 'Gerando...' : 'Gerar Relatório'}
                </StandardButton>
              </FormGridItem>
            </FormGrid>
          </FormSection>
        </StandardForm>
      </ContentSection>

      {/* Botões de Exportação */}
      {relatorioData && (
        <ContentSection>
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 3 }}>
            <StandardButton
              variant="outlined"
              color="secondary"
              startIcon={<PdfIcon />}
              onClick={handleEmitirPDF}
              size="large"
            >
              Exportar Relatório em PDF
            </StandardButton>
            <StandardButton
              variant="outlined"
              color="primary"
              startIcon={<ExcelIcon />}
              onClick={handleExportarXLSX}
              size="large"
            >
              Exportar XLSX
            </StandardButton>
          </Box>
        </ContentSection>
      )}

      {/* Exibição de Erro */}
      {error && (
        <ContentSection>
          <Alert severity="error">
            {error}
          </Alert>
        </ContentSection>
      )}

      {/* Resultados do Relatório */}
      {relatorioData && (
        <ContentSection
          title="Resultados do Relatório"
          subtitle={`Período: ${dataInicio} até ${dataFim}`}
        >
          {/* Resumo Estatístico */}
          <StandardCard
            title="Resumo do Período"
            sx={{ mb: 4 }}
          >
            <FormGrid spacing={3}>
              {/* Estatísticas principais */}
              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{
                  textAlign: 'center',
                  p: 3,
                  backgroundColor: 'primary.main',
                  borderRadius: 2,
                  color: 'white',
                }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {relatorioData.totalSepultamentosPeriodo}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Sepultamentos no Período
                  </Typography>
                </Box>
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{
                  textAlign: 'center',
                  p: 3,
                  backgroundColor: 'success.main',
                  borderRadius: 2,
                  color: 'white',
                }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {relatorioData.taxaSepultamentosPorDia}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Taxa por Dia
                  </Typography>
                </Box>
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{
                  textAlign: 'center',
                  p: 3,
                  backgroundColor: 'warning.main',
                  borderRadius: 2,
                  color: 'white',
                }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {relatorioData.taxaOcupacao}%
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Taxa de Ocupação
                  </Typography>
                </Box>
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{
                  textAlign: 'center',
                  p: 3,
                  backgroundColor: 'secondary.main',
                  borderRadius: 2,
                  color: 'white',
                }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {relatorioData.totalGavetas}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Total de Gavetas
                  </Typography>
                </Box>
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{
                  textAlign: 'center',
                  p: 3,
                  backgroundColor: 'error.main',
                  borderRadius: 2,
                  color: 'white',
                }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {relatorioData.totalExumacoesPeriodo}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Total de Exumações no Período
                  </Typography>
                </Box>
              </FormGridItem>

              <FormGridItem xs={12} sm={6} md={3}>
                <Box sx={{
                  textAlign: 'center',
                  p: 3,
                  backgroundColor: 'info.main',
                  borderRadius: 2,
                  color: 'white',
                }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, mb: 1 }}>
                    {relatorioData.gavetasOcupadas}
                  </Typography>
                  <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    Gavetas Ocupadas
                  </Typography>
                </Box>
              </FormGridItem>
            </FormGrid>
          </StandardCard>

          {/* Tabelas de Dados */}
          <FormGrid spacing={4}>
            {/* Resumo por Dia */}
            <FormGridItem xs={12} lg={6}>
              <StandardCard title="Resumo por Dia">
                <StandardTable
                  columns={[
                    { id: 'data', label: 'Data', minWidth: 100 },
                    { id: 'totalMovimentacoes', label: 'Total de Movimentações', minWidth: 120, align: 'center' },
                    { id: 'status', label: 'Status', minWidth: 200, align: 'center' },
                  ]}
                  data={relatorioData.visaoGeral || []}
                  loading={false}
                  emptyMessage="Nenhum registro encontrado no período"
                />
              </StandardCard>
            </FormGridItem>

            {/* Lista Completa */}
            <FormGridItem xs={12} lg={6}>
              <StandardCard title="Todos os Movimentos do Período">
                <StandardTable
                  columns={[
                    {
                      id: 'nome_sepultado',
                      label: 'Nome',
                      minWidth: 150,
                      render: (value) => (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PersonIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                          <Typography variant="body2" fontWeight="medium">
                            {value}
                          </Typography>
                        </Box>
                      ),
                    },
                    {
                      id: 'data_sepultamento',
                      label: 'Data',
                      minWidth: 100,
                      render: (value) => (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <CalendarIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                          <Typography variant="body2">
                            {formatDate(value)}
                          </Typography>
                        </Box>
                      ),
                    },
                    {
                      id: 'bloco_denominacao',
                      label: 'Bloco',
                      minWidth: 120,
                      render: (value, row) => (
                        <Typography variant="body2">
                          {value || row.codigo_bloco || 'N/A'}
                        </Typography>
                      ),
                    },
                    {
                      id: 'situacao',
                      label: 'Situação',
                      minWidth: 100,
                      render: (value) => (
                        <StatusChip
                          status={value}
                          color={value === 'Exumado' ? 'warning' : 'success'}
                        />
                      ),
                    },
                  ]}
                  data={relatorioData.todosSepultamentos || []}
                  loading={false}
                  emptyMessage="Nenhum sepultamento encontrado no período"
                />
              </StandardCard>
            </FormGridItem>
          </FormGrid>
        </ContentSection>
      )}
    </StandardContainer>
  );
};

export default RelatoriosPage;
