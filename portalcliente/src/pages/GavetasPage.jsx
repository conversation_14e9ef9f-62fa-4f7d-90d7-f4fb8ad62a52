import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { produtoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import GavetaGrid from '../components/GavetaGrid';
import GavetaModal from '../components/GavetaModal';

const PageContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;

  @media (max-width: 768px) {
    padding: 0 8px;
  }
`;

const PageHeader = styled.div`
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
`;

const PageDescription = styled.p`
  color: #6b7280;
  margin: 0;
`;

const FiltersContainer = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
`;

const FiltersGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    cursor: not-allowed;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  &::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const GavetasPage = () => {
  const [produtos, setProdutos] = useState([]);
  const [blocos, setBlocos] = useState([]);
  const [subBlocos, setSubBlocos] = useState([]);
  const [selectedProduto, setSelectedProduto] = useState('');
  const [selectedBloco, setSelectedBloco] = useState('');
  const [selectedSubBloco, setSelectedSubBloco] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedGaveta, setSelectedGaveta] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    loadProdutos();
    loadEstatisticas();
  }, []);

  const loadEstatisticas = async () => {
    try {
      const response = await produtoService.estatisticas();
      setProdutos(response.data);
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  useEffect(() => {
    if (selectedProduto) {
      loadBlocos(selectedProduto);
    } else {
      setBlocos([]);
      setSelectedBloco('');
    }
  }, [selectedProduto]);

  useEffect(() => {
    if (selectedBloco) {
      loadSubBlocos(selectedBloco);
    } else {
      setSubBlocos([]);
      setSelectedSubBloco('');
    }
  }, [selectedBloco]);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      const response = await produtoService.listar();
      setProdutos(response.data);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBlocos = async (produtoId) => {
    try {
      const response = await produtoService.listarBlocos(produtoId);
      setBlocos(response.data);
    } catch (error) {
      console.error('Erro ao carregar blocos:', error);
    }
  };

  const loadSubBlocos = async (blocoId) => {
    try {
      const response = await produtoService.listarSubBlocos(blocoId);
      setSubBlocos(response.data);
    } catch (error) {
      console.error('Erro ao carregar sub-blocos:', error);
    }
  };

  const handleGavetaClick = (gaveta) => {
    setSelectedGaveta(gaveta);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedGaveta(null);
  };

  if (loading) {
    return (
      <PageContainer>
        <LoadingSpinner />
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Gestão de Gavetas</PageTitle>
        <PageDescription>
          Visualize e gerencie as gavetas dos produtos cadastrados
        </PageDescription>
      </PageHeader>

      {/* Dashboard de Produtos */}
      {produtos.length > 0 && (
        <div style={{
          background: 'white',
          borderRadius: '12px',
          padding: '24px',
          marginBottom: '24px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)',
          border: '1px solid #e5e7eb'
        }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '1.125rem', fontWeight: '600', color: '#1f2937' }}>
            Dashboard de Produtos
          </h3>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '16px'
          }}>
            {produtos.map((produto) => (
              <div key={produto.id} style={{
                padding: '16px',
                background: '#f9fafb',
                borderRadius: '8px',
                border: '1px solid #e5e7eb'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                  <h4 style={{ margin: 0, fontSize: '0.875rem', fontWeight: '500', color: '#1f2937' }}>
                    {produto.nome}
                  </h4>
                  <span style={{
                    padding: '2px 8px',
                    borderRadius: '12px',
                    fontSize: '0.75rem',
                    fontWeight: '500',
                    background: produto.tipo === 'ETEN' ? '#dbeafe' : '#dcfce7',
                    color: produto.tipo === 'ETEN' ? '#1e40af' : '#059669'
                  }}>
                    {produto.tipo}
                  </span>
                </div>
                <div style={{ fontSize: '0.75rem', color: '#6b7280', marginBottom: '12px' }}>
                  Status: <span style={{
                    color: produto.ativo ? '#059669' : '#dc2626',
                    fontWeight: '500'
                  }}>
                    {produto.ativo ? 'Ativo' : 'Inativo'}
                  </span>
                </div>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '8px', marginBottom: '12px' }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold', color: '#1f2937' }}>
                      {produto.total_blocos || 0}
                    </div>
                    <div style={{ fontSize: '0.625rem', color: '#6b7280' }}>Blocos</div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold', color: '#1f2937' }}>
                      {produto.total_sub_blocos || 0}
                    </div>
                    <div style={{ fontSize: '0.625rem', color: '#6b7280' }}>Sub-blocos</div>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold', color: '#1f2937' }}>
                      {produto.total_gavetas || 0}
                    </div>
                    <div style={{ fontSize: '0.625rem', color: '#6b7280' }}>Gavetas</div>
                  </div>
                </div>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.75rem', marginBottom: '4px' }}>
                    <span>Ocupação</span>
                    <span style={{ fontWeight: '500' }}>{produto.percentual_ocupacao || 0}%</span>
                  </div>
                  <div style={{
                    width: '100%',
                    height: '6px',
                    background: '#e5e7eb',
                    borderRadius: '3px',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      height: '100%',
                      background: 'linear-gradient(90deg, #059669 0%, #10b981 100%)',
                      width: `${produto.percentual_ocupacao || 0}%`,
                      transition: 'width 0.3s ease'
                    }} />
                  </div>
                  <div style={{ fontSize: '0.625rem', color: '#6b7280', textAlign: 'center', marginTop: '4px' }}>
                    {produto.gavetas_ocupadas || 0} ocupadas / {produto.total_gavetas || 0} total
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <FiltersContainer>
        <FiltersGrid>
          <FormGroup>
            <Label htmlFor="produto">Produto</Label>
            <Select
              id="produto"
              value={selectedProduto}
              onChange={(e) => setSelectedProduto(e.target.value)}
            >
              <option value="">Selecione um produto</option>
              {produtos.map((produto) => (
                <option key={produto.id} value={produto.id}>
                  {produto.nome} ({produto.tipo})
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="bloco">Bloco</Label>
            <Select
              id="bloco"
              value={selectedBloco}
              onChange={(e) => setSelectedBloco(e.target.value)}
              disabled={!selectedProduto}
            >
              <option value="">Selecione um bloco</option>
              {blocos.map((bloco) => (
                <option key={bloco.id} value={bloco.id}>
                  {bloco.codigo_bloco} - {bloco.nome}
                </option>
              ))}
            </Select>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="subBloco">Sub-bloco</Label>
            <Select
              id="subBloco"
              value={selectedSubBloco}
              onChange={(e) => setSelectedSubBloco(e.target.value)}
              disabled={!selectedBloco}
            >
              <option value="">Selecione um sub-bloco</option>
              {subBlocos.map((subBloco) => (
                <option key={subBloco.id} value={subBloco.id}>
                  {subBloco.codigo_sub_bloco} - {subBloco.nome}
                </option>
              ))}
            </Select>
          </FormGroup>
        </FiltersGrid>
      </FiltersContainer>

      {selectedSubBloco && (
        <GavetaGrid
          subBlocoId={selectedSubBloco}
          onGavetaClick={handleGavetaClick}
        />
      )}

      <GavetaModal
        gaveta={selectedGaveta}
        show={showModal}
        onClose={handleCloseModal}
      />
    </PageContainer>
  );
};

export default GavetasPage;
