import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { sepultamentoService, produtoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import Modal from '../components/Modal';
import SepultamentoModal from '../components/SepultamentoModal';
import ExumacaoConfirmationModal from '../components/ExumacaoConfirmationModal';

const PageContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
`;

const AddButton = styled.button`
  background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
`;

const TableContainer = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background: #f9fafb;
`;

const TableRow = styled.tr`
  border-bottom: 1px solid #e5e7eb;

  &:hover {
    background: #f9fafb;
  }
`;

const TableHeaderCell = styled.th`
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
`;

const TableCell = styled.td`
  padding: 12px 16px;
  color: #6b7280;
  font-size: 0.875rem;
`;

const StatusBadge = styled.span`
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  background: ${props => props.exumado ? '#fef2f2' : '#f0fdf4'};
  color: ${props => props.exumado ? '#dc2626' : '#059669'};
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  
  &::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 16px;
`;

const EmptyTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 8px 0;
`;

const EmptyDescription = styled.p`
  margin: 0;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: #059669;
  cursor: pointer;
  font-size: 0.875rem;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f0fdf4;
  }
`;

const DetailModal = styled.div`
  max-height: 70vh;
  overflow-y: auto;
`;

const DetailSection = styled.div`
  margin-bottom: 24px;
`;

const DetailTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
`;

const DetailItem = styled.div`
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
`;

const DetailLabel = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 4px;
  text-transform: uppercase;
  font-weight: 500;
`;

const DetailValue = styled.div`
  font-size: 1rem;
  color: #1f2937;
  font-weight: 500;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
`;

const ActionButtonModal = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  flex: 1;
  min-width: 120px;

  &.primary {
    background: #059669;
    color: white;

    &:hover {
      background: #047857;
    }
  }

  &.secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;

    &:hover {
      background: #f9fafb;
    }
  }

  &.danger {
    background: #dc2626;
    color: white;

    &:hover {
      background: #b91c1c;
    }
  }
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const FilterSelect = styled.select`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  min-width: 200px;
  font-size: 0.875rem;
`;

const FilterLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
`;

const SepultamentosPage = () => {
  const [sepultamentos, setSepultamentos] = useState([]);
  const [produtos, setProdutos] = useState([]);
  const [filtroCodigoEstacao, setFiltroCodigoEstacao] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedSepultamento, setSelectedSepultamento] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showSepultamentoModal, setShowSepultamentoModal] = useState(false);
  const [showExumarModal, setShowExumarModal] = useState(false);
  const [exumarLoading, setExumarLoading] = useState(false);
  const { user, canAddEditExhumeSepultamentos, canDeleteSepultamentos } = useAuth();

  useEffect(() => {
    loadSepultamentos();
    loadProdutos();
  }, []);

  useEffect(() => {
    loadSepultamentos();
  }, [filtroCodigoEstacao]);

  const loadSepultamentos = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {};
      if (filtroCodigoEstacao) {
        params.codigo_estacao = filtroCodigoEstacao;
      }

      const response = await sepultamentoService.listar(params);
      setSepultamentos(response.data);
    } catch (error) {
      console.error('Erro ao carregar sepultamentos:', error);
      setError('Erro ao carregar sepultamentos');
    } finally {
      setLoading(false);
    }
  };

  const loadProdutos = async () => {
    try {
      const response = await produtoService.listar();
      setProdutos(response.data);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';

    // CORREÇÃO TIMEZONE: Usar split para evitar problema de fuso horário
    // Problema: new Date(dateString) converte para UTC e subtrai 1 dia
    // Solução: Fazer parse manual da data no formato YYYY-MM-DD
    const dateParts = dateString.split('T')[0].split('-');
    if (dateParts.length === 3) {
      const year = parseInt(dateParts[0]);
      const month = parseInt(dateParts[1]) - 1; // Mês é 0-indexado
      const day = parseInt(dateParts[2]);
      const localDate = new Date(year, month, day);
      return localDate.toLocaleDateString('pt-BR');
    }

    // Fallback para formato antigo (não deveria acontecer)
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const handleViewDetails = (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setShowDetailModal(true);
  };

  const handleAddSepultamento = () => {
    setSelectedSepultamento(null);
    setShowSepultamentoModal(true);
  };

  const handleEditSepultamento = (sepultamento) => {
    // Verificar se é cliente tentando editar sepultamento exumado
    if (user.tipo_usuario !== 'admin' && sepultamento.data_exumacao) {
      alert('Sepultamentos exumados só podem ser editados por administradores');
      return;
    }

    setSelectedSepultamento(sepultamento);
    setShowSepultamentoModal(true);
  };

  const canEditSepultamento = (sepultamento) => {
    if (!canAddEditExhumeSepultamentos()) return false;
    if (user.tipo_usuario === 'admin') return true;
    return !sepultamento.data_exumacao; // Cliente só pode editar se não estiver exumado
  };

  const canDeleteSepultamentoItem = (sepultamento) => {
    if (!canDeleteSepultamentos()) return false;
    // Tanto admin quanto cliente podem deletar qualquer sepultamento (sem exigir exumação)
    return true;
  };

  const handleExumar = async (sepultamento) => {
    setSelectedSepultamento(sepultamento);
    setShowExumarModal(true);
  };

  const handleExumarConfirm = async (dadosExumacao) => {
    try {
      setExumarLoading(true);
      await sepultamentoService.exumar(selectedSepultamento.id, dadosExumacao);

      // Recarregar lista
      loadSepultamentos();
      setShowDetailModal(false);
      setShowExumarModal(false);
      setSelectedSepultamento(null);

      alert('Exumação realizada com sucesso! A gaveta foi liberada automaticamente.');
    } catch (error) {
      console.error('Erro ao exumar sepultamento:', error);
      alert('Erro ao exumar sepultamento: ' + (error.response?.data?.error || error.message));
    } finally {
      setExumarLoading(false);
    }
  };

  const handleDeleteSepultamento = async (sepultamento) => {
    if (!canDeleteSepultamentos()) {
      alert('Você não tem permissão para deletar sepultamentos');
      return;
    }

    // Mensagem de confirmação mais clara sobre a permanência da ação
    const confirmMessage = `⚠️ ATENÇÃO: DELEÇÃO PERMANENTE ⚠️

Tem certeza que deseja DELETAR PERMANENTEMENTE o sepultamento de "${sepultamento.nome_sepultado}"?

🚨 IMPORTANTE: Esta ação irá:
• Remover o registro DEFINITIVAMENTE do banco de dados
• NÃO PODE ser desfeita
• O registro será perdido para sempre

Digite "CONFIRMAR" para prosseguir:`;

    const userConfirmation = prompt(confirmMessage);

    if (userConfirmation !== 'CONFIRMAR') {
      alert('Deleção cancelada. O registro foi preservado.');
      return;
    }

    try {
      await sepultamentoService.deletar(sepultamento.id);
      loadSepultamentos();
      setShowDetailModal(false);
      alert('✅ Sepultamento deletado permanentemente com sucesso!');
    } catch (error) {
      console.error('Erro ao deletar sepultamento:', error);
      const errorMessage = error.response?.data?.error || 'Erro interno do servidor';
      alert(`❌ Erro ao deletar sepultamento: ${errorMessage}`);
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <PageContainer>
        <div style={{ color: '#dc2626', textAlign: 'center', padding: '48px' }}>
          {error}
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>Book de Sepultamentos</PageTitle>
        {canAddEditExhumeSepultamentos() && (
          <AddButton onClick={handleAddSepultamento}>
            + Novo Sepultamento
          </AddButton>
        )}
      </PageHeader>

      <FilterContainer>
        <FilterLabel htmlFor="filtro-estacao">Filtrar por Produto/Estação:</FilterLabel>
        <FilterSelect
          id="filtro-estacao"
          value={filtroCodigoEstacao}
          onChange={(e) => setFiltroCodigoEstacao(e.target.value)}
        >
          <option value="">Todos os produtos</option>
          {produtos.map(produto => (
            <option key={produto.codigo_estacao} value={produto.codigo_estacao}>
              {produto.denominacao} ({produto.codigo_estacao})
            </option>
          ))}
        </FilterSelect>
      </FilterContainer>

      <TableContainer>
        {sepultamentos.length === 0 ? (
          <EmptyState>
            <EmptyIcon>⚰️</EmptyIcon>
            <EmptyTitle>Nenhum sepultamento encontrado</EmptyTitle>
            <EmptyDescription>
              Comece adicionando o primeiro sepultamento ao sistema.
            </EmptyDescription>
          </EmptyState>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHeaderCell>Nome do Sepultado</TableHeaderCell>
                <TableHeaderCell>Código Estação</TableHeaderCell>
                <TableHeaderCell>Denominação do Bloco</TableHeaderCell>
                <TableHeaderCell>Gaveta</TableHeaderCell>
                <TableHeaderCell>Data Sepultamento</TableHeaderCell>
                <TableHeaderCell>Horário</TableHeaderCell>
                <TableHeaderCell>Status</TableHeaderCell>
                <TableHeaderCell>Ações</TableHeaderCell>
              </TableRow>
            </TableHeader>
            <tbody>
              {sepultamentos.map((sepultamento) => (
                <TableRow key={sepultamento.id} onClick={() => handleViewDetails(sepultamento)} style={{ cursor: 'pointer' }}>
                  <TableCell>{sepultamento.nome_sepultado}</TableCell>
                  <TableCell>{sepultamento.codigo_estacao}</TableCell>
                  <TableCell>{sepultamento.denominacao_bloco}</TableCell>
                  <TableCell>{sepultamento.numero_gaveta}</TableCell>
                  <TableCell>{formatDate(sepultamento.data_sepultamento)}</TableCell>
                  <TableCell>{sepultamento.horario_sepultamento || '-'}</TableCell>
                  <TableCell>
                    <StatusBadge exumado={sepultamento.status_exumacao}>
                      {sepultamento.status_exumacao ? 'Exumado' : 'Sepultado'}
                    </StatusBadge>
                  </TableCell>
                  <TableCell>
                    <ActionButton onClick={(e) => {
                      e.stopPropagation();
                      handleViewDetails(sepultamento);
                    }}>
                      Ver Detalhes
                    </ActionButton>
                  </TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        )}
      </TableContainer>

      {/* Modal de Detalhes */}
      <Modal
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title={`Detalhes: ${selectedSepultamento?.nome_sepultado}`}
        maxWidth="800px"
      >
        {selectedSepultamento && (
          <DetailModal>
            <DetailSection>
              <DetailTitle>👤 Informações do Sepultado</DetailTitle>
              <DetailGrid>
                <DetailItem>
                  <DetailLabel>Nome Completo</DetailLabel>
                  <DetailValue>{selectedSepultamento.nome_sepultado}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Data de Sepultamento</DetailLabel>
                  <DetailValue>{formatDate(selectedSepultamento.data_sepultamento)}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Horário de Sepultamento</DetailLabel>
                  <DetailValue>{selectedSepultamento.horario_sepultamento || 'Não informado'}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Data de Registro</DetailLabel>
                  <DetailValue>
                    {selectedSepultamento.created_at
                      ? new Date(selectedSepultamento.created_at).toLocaleString('pt-BR')
                      : 'Não disponível'
                    }
                  </DetailValue>
                </DetailItem>
                {selectedSepultamento.observacoes && (
                  <DetailItem style={{ gridColumn: '1 / -1' }}>
                    <DetailLabel>Observações do Sepultamento</DetailLabel>
                    <DetailValue style={{
                      backgroundColor: '#f8f9fa',
                      padding: '12px',
                      borderRadius: '6px',
                      border: '1px solid #e9ecef',
                      whiteSpace: 'pre-wrap'
                    }}>
                      {selectedSepultamento.observacoes}
                    </DetailValue>
                  </DetailItem>
                )}
              </DetailGrid>
            </DetailSection>

            <DetailSection>
              <DetailTitle>📍 Localização e Produto</DetailTitle>
              <DetailGrid>
                <DetailItem>
                  <DetailLabel>Produto/Estação</DetailLabel>
                  <DetailValue>
                    <strong>{selectedSepultamento.produto_denominacao || 'Não disponível'}</strong>
                    <br />
                    <small style={{ color: '#6b7280' }}>
                      Código: {selectedSepultamento.codigo_estacao}
                    </small>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Cliente</DetailLabel>
                  <DetailValue>
                    {selectedSepultamento.nome_cliente || selectedSepultamento.cliente_nome || 'Não disponível'}
                    <br />
                    <small style={{ color: '#6b7280' }}>
                      Código: {selectedSepultamento.codigo_cliente}
                    </small>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Bloco</DetailLabel>
                  <DetailValue>
                    <strong>{selectedSepultamento.denominacao_bloco || 'Não disponível'}</strong>
                    <br />
                    <small style={{ color: '#6b7280' }}>
                      Código: {selectedSepultamento.codigo_bloco}
                    </small>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Sub-Bloco</DetailLabel>
                  <DetailValue>
                    {selectedSepultamento.sub_bloco_nome || 'Não disponível'}
                    <br />
                    <small style={{ color: '#6b7280' }}>
                      Código: {selectedSepultamento.codigo_sub_bloco}
                    </small>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Gaveta</DetailLabel>
                  <DetailValue>
                    <span style={{
                      fontSize: '1.2em',
                      fontWeight: 'bold',
                      color: '#059669',
                      backgroundColor: '#d1fae5',
                      padding: '4px 8px',
                      borderRadius: '4px'
                    }}>
                      Gaveta {selectedSepultamento.numero_gaveta}
                    </span>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Posição na Gaveta</DetailLabel>
                  <DetailValue>{selectedSepultamento.posicao || '1'}</DetailValue>
                </DetailItem>
              </DetailGrid>
            </DetailSection>



            {selectedSepultamento.data_exumacao && (
              <DetailSection>
                <DetailTitle>⚱️ Informações de Exumação</DetailTitle>
                <DetailGrid>
                  <DetailItem>
                    <DetailLabel>Data de Exumação</DetailLabel>
                    <DetailValue>
                      <span style={{
                        color: '#dc2626',
                        fontWeight: 'bold'
                      }}>
                        {formatDate(selectedSepultamento.data_exumacao)}
                      </span>
                    </DetailValue>
                  </DetailItem>
                  <DetailItem>
                    <DetailLabel>Horário de Exumação</DetailLabel>
                    <DetailValue>
                      {selectedSepultamento.horario_exumacao || 'Não informado'}
                    </DetailValue>
                  </DetailItem>
                  {selectedSepultamento.observacoes_exumacao && (
                    <DetailItem style={{ gridColumn: '1 / -1' }}>
                      <DetailLabel>Observações da Exumação</DetailLabel>
                      <DetailValue style={{
                        backgroundColor: '#fef2f2',
                        padding: '12px',
                        borderRadius: '6px',
                        border: '1px solid #fecaca',
                        whiteSpace: 'pre-wrap'
                      }}>
                        {selectedSepultamento.observacoes_exumacao}
                      </DetailValue>
                    </DetailItem>
                  )}
                </DetailGrid>
              </DetailSection>
            )}

            <DetailSection>
              <DetailTitle>📊 Status e Informações Temporais</DetailTitle>
              <DetailGrid>
                <DetailItem>
                  <DetailLabel>Status Atual</DetailLabel>
                  <DetailValue>
                    <StatusBadge exumado={!!selectedSepultamento.exumado_em}>
                      {selectedSepultamento.exumado_em ? '⚱️ Exumado' : '🪦 Sepultado'}
                    </StatusBadge>
                  </DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Tempo de Sepultamento</DetailLabel>
                  <DetailValue>
                    {(() => {
                      const dataSepultamento = new Date(selectedSepultamento.data_sepultamento);
                      const dataFinal = selectedSepultamento.exumado_em
                        ? new Date(selectedSepultamento.exumado_em)
                        : new Date();

                      const diffTime = Math.abs(dataFinal - dataSepultamento);
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                      const diffMonths = Math.floor(diffDays / 30);
                      const diffYears = Math.floor(diffDays / 365);

                      if (diffYears > 0) {
                        return `${diffYears} ano(s) e ${diffMonths % 12} mês(es)`;
                      } else if (diffMonths > 0) {
                        return `${diffMonths} mês(es) e ${diffDays % 30} dia(s)`;
                      } else {
                        return `${diffDays} dia(s)`;
                      }
                    })()}
                  </DetailValue>
                </DetailItem>
                {!selectedSepultamento.status_exumacao && (
                  <DetailItem>
                    <DetailLabel>Data Prevista para Exumação</DetailLabel>
                    <DetailValue>
                      {(() => {
                        const dataSepultamento = new Date(selectedSepultamento.data_sepultamento);
                        const mesesParaExumar = 36; // Padrão, pode vir do produto
                        dataSepultamento.setMonth(dataSepultamento.getMonth() + mesesParaExumar);
                        return dataSepultamento.toLocaleDateString('pt-BR');
                      })()}
                      <br />
                      <small style={{ color: '#6b7280' }}>
                        (36 meses após sepultamento)
                      </small>
                    </DetailValue>
                  </DetailItem>
                )}
              </DetailGrid>
            </DetailSection>

            <ActionButtons>
              {canEditSepultamento(selectedSepultamento) && (
                <ActionButtonModal
                  className="primary"
                  onClick={() => {
                    setShowDetailModal(false);
                    handleEditSepultamento(selectedSepultamento);
                  }}
                >
                  Editar
                </ActionButtonModal>
              )}

              {canAddEditExhumeSepultamentos() && !selectedSepultamento.status_exumacao && (
                <ActionButtonModal
                  className="danger"
                  onClick={() => handleExumar(selectedSepultamento)}
                >
                  Exumar
                </ActionButtonModal>
              )}

              {canDeleteSepultamentoItem(selectedSepultamento) && (
                <ActionButtonModal
                  className="danger"
                  onClick={() => handleDeleteSepultamento(selectedSepultamento)}
                >
                  Deletar
                </ActionButtonModal>
              )}

              <ActionButtonModal
                className="secondary"
                onClick={() => setShowDetailModal(false)}
              >
                Fechar
              </ActionButtonModal>
            </ActionButtons>
          </DetailModal>
        )}
      </Modal>

      {/* Modal de Sepultamento */}
      <SepultamentoModal
        isOpen={showSepultamentoModal}
        onClose={() => setShowSepultamentoModal(false)}
        sepultamento={selectedSepultamento}
        onSuccess={loadSepultamentos}
      />

      {/* Modal de Confirmação de Exumação */}
      <ExumacaoConfirmationModal
        open={showExumarModal}
        onClose={() => {
          setShowExumarModal(false);
          setSelectedSepultamento(null);
        }}
        sepultamento={selectedSepultamento}
        onConfirm={handleExumarConfirm}
        loading={exumarLoading}
      />
    </PageContainer>
  );
};

export default SepultamentosPage;
