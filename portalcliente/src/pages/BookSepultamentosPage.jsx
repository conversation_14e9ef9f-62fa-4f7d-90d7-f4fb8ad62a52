import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  LinearProgress,
  Skeleton,
  Alert,
} from '@mui/material';
import {
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { produtoService } from '../services/api';
import {
  StandardContainer,
  StandardCard,
  StandardButton,
  FormGrid,
  FormGridItem,
  ContentSection,
  StatusChip,
} from '../components/common';

const BookSepultamentosPage = () => {
  const [produtos, setProdutos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadProdutos();
  }, []);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      setError(null);

      // Usar API de estatísticas para obter dados completos
      const response = await produtoService.estatisticas();
      setProdutos(response.data || []);
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      setError('Erro ao carregar produtos');
    } finally {
      setLoading(false);
    }
  };

  const handleViewSepultamentos = (produto) => {
    navigate(`/dashboard/book-sepultamentos/${produto.codigo_estacao}`, {
      state: { produto }
    });
  };

  const getStatusColor = (ativo) => {
    return ativo ? 'success' : 'error';
  };

  const getStatusText = (ativo) => {
    return ativo ? 'Ativa' : 'Inativa';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Não informado';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const ProductCardSkeleton = () => (
    <StandardCard sx={{ height: '100%', minHeight: '400px', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Skeleton variant="circular" width={28} height={28} sx={{ mr: 2 }} />
        <Box sx={{ flexGrow: 1 }}>
          <Skeleton variant="text" width="70%" height={32} />
          <Skeleton variant="rectangular" width={60} height={24} sx={{ mt: 1, borderRadius: 1 }} />
        </Box>
      </Box>

      <Skeleton variant="text" width="80%" height={24} sx={{ mb: 1 }} />
      <Skeleton variant="text" width="70%" height={24} sx={{ mb: 3 }} />

      <FormGrid spacing={2} sx={{ mb: 3 }}>
        <FormGridItem xs={6}>
          <Skeleton variant="rectangular" height={60} sx={{ borderRadius: 2 }} />
        </FormGridItem>
        <FormGridItem xs={6}>
          <Skeleton variant="rectangular" height={60} sx={{ borderRadius: 2 }} />
        </FormGridItem>
      </FormGrid>

      <Box sx={{ mb: 3 }}>
        <Skeleton variant="text" width="40%" height={24} sx={{ mb: 2 }} />
        <Skeleton variant="rectangular" height={12} sx={{ borderRadius: 6, mb: 1.5 }} />
        <Skeleton variant="text" width="60%" height={20} />
      </Box>

      <Box sx={{ mt: 'auto' }}>
        <Skeleton variant="rectangular" height={48} sx={{ borderRadius: 2 }} />
      </Box>
    </StandardCard>
  );

  if (loading) {
    return (
      <StandardContainer
        title="Book de Sepultamentos"
        subtitle="Carregando produtos..."
      >
        <ContentSection>
          <FormGrid spacing={3}>
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <FormGridItem xs={12} sm={6} md={4} key={item}>
                <ProductCardSkeleton />
              </FormGridItem>
            ))}
          </FormGrid>
        </ContentSection>
      </StandardContainer>
    );
  }

  if (error) {
    return (
      <StandardContainer
        title="Book de Sepultamentos"
        subtitle="Erro ao carregar dados"
      >
        <ContentSection>
          <Alert
            severity="error"
            action={
              <StandardButton
                variant="contained"
                onClick={loadProdutos}
                size="small"
              >
                Tentar Novamente
              </StandardButton>
            }
          >
            {error}
          </Alert>
        </ContentSection>
      </StandardContainer>
    );
  }

  return (
    <StandardContainer
      title="Book de Sepultamentos"
      subtitle={
        isAdmin()
          ? 'Visualize todos os produtos e seus sepultamentos registrados no sistema'
          : 'Gerencie os sepultamentos dos seus produtos de forma organizada'
      }
    >
      {produtos.length === 0 ? (
        <ContentSection>
          <StandardCard
            sx={{
              textAlign: 'center',
              py: 6,
              backgroundColor: 'background.paper',
            }}
          >
            <BusinessIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Nenhum produto encontrado
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {isAdmin()
                ? 'Nenhum produto foi cadastrado no sistema ainda.'
                : 'Você não possui produtos associados à sua conta.'
              }
            </Typography>
          </StandardCard>
        </ContentSection>
      ) : (
        <ContentSection title="Produtos Disponíveis">
          <FormGrid spacing={3}>
            {produtos.map((produto) => (
              <FormGridItem xs={12} sm={6} md={4} key={produto.id}>
                <StandardCard
                  clickable
                  onClick={() => handleViewSepultamentos(produto)}
                  sx={{
                    height: '100%',
                    minHeight: '400px',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  {/* Header do Card */}
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                    <BusinessIcon sx={{ mr: 2, color: 'primary.main', fontSize: 28 }} />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography
                        variant="h5"
                        component="h2"
                        sx={{
                          fontWeight: 600,
                          lineHeight: 1.3,
                          wordBreak: 'break-word',
                          mb: 1,
                        }}
                      >
                        {produto.denominacao || produto.nome}
                      </Typography>
                      <StatusChip
                        status={getStatusText(produto.ativo)}
                        color={getStatusColor(produto.ativo)}
                      />
                    </Box>
                  </Box>

                  {/* Informações do Produto */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <LocationIcon sx={{ fontSize: 20, mr: 1.5, color: 'text.secondary' }} />

                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <CalendarIcon sx={{ fontSize: 20, mr: 1.5, color: 'text.secondary' }} />
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{ fontWeight: 500 }}
                      >
                        Exumação: {produto.meses_para_exumar || 24} meses
                      </Typography>
                    </Box>
                  </Box>

                  {/* Estatísticas */}
                  <FormGrid spacing={2} sx={{ mb: 3 }}>
                    <FormGridItem xs={6}>
                      <Box sx={{
                        p: 2,
                        backgroundColor: 'primary.light',
                        borderRadius: 2,
                        textAlign: 'center',
                        color: 'primary.contrastText',
                      }}>
                        <Typography variant="caption" sx={{ fontSize: '0.75rem', opacity: 0.8 }}>
                          Blocos
                        </Typography>
                        <Typography variant="h5" sx={{ fontWeight: 700, mt: 0.5 }}>
                          {produto.total_blocos || 0}
                        </Typography>
                      </Box>
                    </FormGridItem>
                    <FormGridItem xs={6}>
                      <Box sx={{
                        p: 2,
                        backgroundColor: 'secondary.light',
                        borderRadius: 2,
                        textAlign: 'center',
                        color: 'secondary.contrastText',
                      }}>
                        <Typography variant="caption" sx={{ fontSize: '0.75rem', opacity: 0.8 }}>
                          Total Gavetas
                        </Typography>
                        <Typography variant="h5" sx={{ fontWeight: 700, mt: 0.5 }}>
                          {produto.total_gavetas || 0}
                        </Typography>
                      </Box>
                    </FormGridItem>
                  </FormGrid>

                  {/* Gráfico de Ocupação */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 600 }}>
                        Taxa de Ocupação
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color="primary.main">
                        {produto.percentual_ocupacao || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={produto.percentual_ocupacao || 0}
                      sx={{
                        height: 12,
                        borderRadius: 6,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: 'success.main',
                          borderRadius: 6,
                        }
                      }}
                    />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        mt: 1.5,
                        textAlign: 'center',
                        fontWeight: 500,
                      }}
                    >
                      {produto.gavetas_ocupadas || 0} ocupadas de {produto.total_gavetas || 0} gavetas
                    </Typography>
                  </Box>

                  {/* Botão de Ação */}
                  <Box sx={{ mt: 'auto', pt: 2 }}>
                    <StandardButton
                      variant="contained"
                      fullWidth
                      startIcon={<VisibilityIcon />}
                      onClick={() => handleViewSepultamentos(produto)}
                    >
                      Ver Sepultamentos
                    </StandardButton>
                  </Box>
                </StandardCard>
              </FormGridItem>
            ))}
          </FormGrid>
        </ContentSection>
      )}
    </StandardContainer>
  );
};

export default BookSepultamentosPage;
