import React, { createContext, useContext, useState, useCallback } from 'react';
import GlobalLoading from '../components/common/GlobalLoading';

// Contexto de loading
const LoadingContext = createContext();

// Hook para usar o contexto de loading
export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading deve ser usado dentro de um LoadingProvider');
  }
  return context;
};

// Provider do contexto de loading
export const LoadingProvider = ({ children }) => {
  const [loadingState, setLoadingState] = useState({
    isLoading: false,
    message: 'Carregando...',
    variant: 'default',
  });

  // Função para mostrar loading
  const showLoading = useCallback((message = 'Carregando...', variant = 'default') => {
    setLoadingState({
      isLoading: true,
      message,
      variant,
    });
  }, []);

  // Função para esconder loading
  const hideLoading = useCallback(() => {
    setLoadingState(prev => ({
      ...prev,
      isLoading: false,
    }));
  }, []);

  // Função para loading com timeout automático
  const showLoadingWithTimeout = useCallback((message = 'Carregando...', timeout = 5000, variant = 'default') => {
    showLoading(message, variant);
    setTimeout(() => {
      hideLoading();
    }, timeout);
  }, [showLoading, hideLoading]);

  // Função para executar uma ação com loading
  const withLoading = useCallback(async (
    asyncFunction,
    loadingMessage = 'Processando...',
    variant = 'default'
  ) => {
    try {
      showLoading(loadingMessage, variant);
      const result = await asyncFunction();
      return result;
    } catch (error) {
      throw error;
    } finally {
      hideLoading();
    }
  }, [showLoading, hideLoading]);

  // Função específica para navegação
  const showNavigationLoading = useCallback((message = 'Carregando página...') => {
    showLoading(message, 'minimal');
  }, [showLoading]);

  // Função específica para operações de dados
  const showDataLoading = useCallback((message = 'Processando dados...') => {
    showLoading(message, 'default');
  }, [showLoading]);

  // Função específica para operações importantes
  const showBrandedLoading = useCallback((message = 'Processando...') => {
    showLoading(message, 'branded');
  }, [showLoading]);

  const value = {
    // Estado
    isLoading: loadingState.isLoading,
    message: loadingState.message,
    variant: loadingState.variant,
    
    // Funções básicas
    showLoading,
    hideLoading,
    showLoadingWithTimeout,
    withLoading,
    
    // Funções específicas
    showNavigationLoading,
    showDataLoading,
    showBrandedLoading,
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
      <GlobalLoading
        open={loadingState.isLoading}
        message={loadingState.message}
        variant={loadingState.variant}
      />
    </LoadingContext.Provider>
  );
};

// Hook personalizado para navegação com loading
export const useNavigationLoading = () => {
  const { showNavigationLoading, hideLoading } = useLoading();
  
  const navigateWithLoading = useCallback((navigationFunction, delay = 300) => {
    showNavigationLoading('Carregando página...');
    
    // Pequeno delay para mostrar o loading antes da navegação
    setTimeout(() => {
      navigationFunction();
      // Esconder loading após a navegação
      setTimeout(hideLoading, 100);
    }, delay);
  }, [showNavigationLoading, hideLoading]);

  return { navigateWithLoading };
};

// Hook personalizado para operações assíncronas
export const useAsyncOperation = () => {
  const { withLoading, showDataLoading, hideLoading } = useLoading();
  
  const executeAsync = useCallback(async (
    operation,
    options = {}
  ) => {
    const {
      loadingMessage = 'Processando...',
      variant = 'default',
      showError = true,
      onError = null,
    } = options;

    try {
      return await withLoading(operation, loadingMessage, variant);
    } catch (error) {
      if (showError) {
        console.error('Erro na operação:', error);
      }
      if (onError) {
        onError(error);
      }
      throw error;
    }
  }, [withLoading]);

  return { executeAsync, showDataLoading, hideLoading };
};

export default LoadingContext;
