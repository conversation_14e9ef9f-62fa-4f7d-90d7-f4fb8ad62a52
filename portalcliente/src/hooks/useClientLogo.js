import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import logoSemFundo from '../assets/logo_sem_fundo_branco.png';

// Hook para gerenciar logo do cliente
export const useClientLogo = () => {
  const { user } = useAuth();
  const [clientLogo, setClientLogo] = useState(logoSemFundo);
  const [logoError, setLogoError] = useState(false);

  useEffect(() => {
    const loadClientLogo = async () => {
      if (!user) {
        setClientLogo(logoSemFundo);
        return;
      }

      // Se for admin, sempre usar logo padrão
      if (user.tipo_usuario === 'admin') {
        setClientLogo(logoSemFundo);
        return;
      }

      // Se for cliente, verificar se tem logo própria
      const codigoCliente = user.codigo_cliente;
      if (codigoCliente) {
        // Tentar carregar logo do cliente
        const logoUrl = `/api/uploads/logos/${codigoCliente}.png?t=${Date.now()}`;

        // Verificar se a logo existe
        const img = new Image();
        img.onload = () => {
          setClientLogo(logoUrl);
          setLogoError(false);
        };
        img.onerror = () => {
          // Se não conseguir carregar, usar logo padrão
          setClientLogo(logoSemFundo);
          setLogoError(true);
        };
        img.src = logoUrl;
      } else {
        // Se não tem código de cliente, usar logo padrão
        setClientLogo(logoSemFundo);
      }
    };

    loadClientLogo();
  }, [user]);

  return { clientLogo, logoError, defaultLogo: logoSemFundo };
};
