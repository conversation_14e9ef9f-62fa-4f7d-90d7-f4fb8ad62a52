import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Chip,
  Divider,
  Grid,
  Paper,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Notes as NotesIcon,
  RemoveCircle as ExumarIcon,
  CheckCircle as CheckCircleIcon,
  PersonSearch as ExumacoesIcon,
} from '@mui/icons-material';
import { StandardButton } from './common';

// Componente para exibir informações em formato de card
const InfoCard = ({ icon, title, value, color = 'text.secondary' }) => (
  <Paper
    elevation={1}
    sx={{
      p: 2,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      textAlign: 'center',
      border: '1px solid',
      borderColor: 'divider',
      borderRadius: 2,
    }}
  >
    <Box sx={{ color, mb: 1 }}>
      {icon}
    </Box>
    <Typography variant="caption" color="text.secondary" sx={{ mb: 0.5 }}>
      {title}
    </Typography>
    <Typography variant="body2" fontWeight="medium" color="text.primary">
      {value}
    </Typography>
  </Paper>
);

// Componente para seções do modal
const ModalSection = ({ title, icon, children }) => (
  <Box sx={{ mb: 3 }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      <Box sx={{ color: 'primary.main', mr: 1 }}>
        {icon}
      </Box>
      <Typography variant="h6" component="h3" color="primary.main">
        {title}
      </Typography>
    </Box>
    {children}
  </Box>
);

const ExumacaoDetailsModal = ({ open, onClose, exumacao, readOnly = true }) => {
  if (!exumacao) return null;

  // Função para formatar datas
  const formatDate = (dateString) => {
    if (!dateString) return 'Não informado';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Função para formatar horários
  const formatTime = (timeString) => {
    if (!timeString) return 'Não informado';
    return timeString;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 1,
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ExumacoesIcon sx={{ mr: 1, color: 'error.main' }} />
          <Typography variant="h5" component="h2" color="error.main">
            Detalhes do Exumado
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {/* Status */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <Chip
            icon={<ExumarIcon fontSize="small" />}
            label="Exumado"
            color="error"
            variant="filled"
            size="large"
            sx={{ px: 2, py: 1 }}
          />
        </Box>

        {/* Informações do Sepultado */}
        <ModalSection
          title="Dados do Sepultado"
          icon={<PersonIcon />}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <InfoCard
                icon={<PersonIcon fontSize="small" />}
                title="Nome do Sepultado"
                value={exumacao.nome_sepultado || 'Não informado'}
                color="primary.main"
              />
            </Grid>
          </Grid>
        </ModalSection>

        {/* Informações de Localização */}
        <ModalSection
          title="Localização"
          icon={<LocationIcon />}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<BusinessIcon fontSize="small" />}
                title="Produto"
                value={exumacao.produto_denominacao || 'Não informado'}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<LocationIcon fontSize="small" />}
                title="Bloco"
                value={exumacao.denominacao_bloco || exumacao.bloco_nome || exumacao.codigo_bloco || 'Não informado'}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<LocationIcon fontSize="small" />}
                title="Sub-bloco"
                value={exumacao.sub_bloco_denominacao || exumacao.codigo_sub_bloco || 'Não informado'}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<LocationIcon fontSize="small" />}
                title="Gaveta"
                value={exumacao.numero_gaveta || 'Não informado'}
              />
            </Grid>
          </Grid>
        </ModalSection>

        {/* Informações do Sepultamento Original */}
        <ModalSection
          title="Dados do Sepultamento Original"
          icon={<CalendarIcon />}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<CalendarIcon fontSize="small" />}
                title="Data do Sepultamento"
                value={formatDate(exumacao.data_sepultamento)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<ScheduleIcon fontSize="small" />}
                title="Horário do Sepultamento"
                value={formatTime(exumacao.horario_sepultamento)}
              />
            </Grid>
          </Grid>
        </ModalSection>

        {/* Informações da Exumação */}
        <Divider sx={{ my: 3 }} />
        <ModalSection
          title="Dados da Exumação"
          icon={<ExumarIcon />}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<CalendarIcon fontSize="small" />}
                title="Data da Exumação"
                value={formatDate(exumacao.exumado_em || exumacao.data_exumacao)}
                color="error.main"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<ScheduleIcon fontSize="small" />}
                title="Horário da Exumação"
                value={formatTime(exumacao.hora_exumado_em || exumacao.horario_exumacao)}
                color="error.main"
              />
            </Grid>
          </Grid>
        </ModalSection>

        {/* Observações */}
        {exumacao.observacoes && (
          <ModalSection
            title="Observações"
            icon={<NotesIcon />}
          >
            <Paper
              elevation={1}
              sx={{
                p: 2,
                backgroundColor: 'grey.50',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
              }}
            >
              <Typography variant="body2" color="text.secondary">
                {exumacao.observacoes}
              </Typography>
            </Paper>
          </ModalSection>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <StandardButton
          variant="outlined"
          onClick={onClose}
          fullWidth
        >
          Fechar
        </StandardButton>
      </DialogActions>
    </Dialog>
  );
};

export default ExumacaoDetailsModal;
