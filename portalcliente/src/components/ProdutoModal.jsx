import React, { useState, useEffect } from 'react';
import {
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Alert,
  Typography,
  Paper,
} from '@mui/material';
import Modal from './Modal';
import { produtoService, clienteService } from '../services/api';
import { StandardButton } from './common';
import { formatValidationError } from '../utils/hierarchicalErrorMessages';

// Todos os styled-components removidos - usando Material-UI

const ProdutoModal = ({ isOpen, onClose, produto = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_cliente: '',
    codigo_estacao: '',
    meses_para_exumar: 24,
    denominacao: '',
    observacao: ''
  });
  const [clientes, setClientes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      loadClientes();
      if (produto) {
        setFormData({
          codigo_cliente: produto.codigo_cliente || '',
          codigo_estacao: produto.codigo_estacao || '',
          meses_para_exumar: produto.meses_para_exumar || 24,
          denominacao: produto.denominacao || '',
          observacao: produto.observacao || ''
        });
      } else {
        setFormData({
          codigo_cliente: '',
          codigo_estacao: '',
          meses_para_exumar: 24,
          denominacao: '',
          observacao: ''
        });
      }
      setError('');
    }
  }, [isOpen, produto]);

  const loadClientes = async () => {
    try {
      const response = await clienteService.listar();
      setClientes(response.data);
    } catch (error) {
      console.error('Erro ao carregar clientes:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Formatação automática para código da estação
    let formattedValue = value;
    if (name === 'codigo_estacao') {
      formattedValue = value.toUpperCase();
    }

    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    console.log('📤 Enviando dados do produto:', formData);

    // VALIDAÇÕES ANTES DO ENVIO
    if (!formData.codigo_cliente) {
      setError('Cliente é obrigatório');
      setLoading(false);
      return;
    }

    if (!formData.codigo_estacao) {
      setError('Código da estação é obrigatório');
      setLoading(false);
      return;
    }

    if (!formData.denominacao) {
      setError('Denominação é obrigatória');
      setLoading(false);
      return;
    }

    if (!formData.meses_para_exumar || formData.meses_para_exumar < 1) {
      setError('Meses para exumar deve ser um número positivo');
      setLoading(false);
      return;
    }

    try {
      let response;
      if (produto) {
        console.log('🔄 Atualizando produto existente:', {
          codigo_cliente: produto.codigo_cliente,
          codigo_estacao: produto.codigo_estacao
        });
        // CORREÇÃO: Usar os códigos corretos do produto para formar a URL
        console.log('🔄 Enviando dados para atualização:', formData);
        response = await produtoService.atualizar(produto.codigo_cliente, produto.codigo_estacao, formData);
        console.log('✅ Resposta da atualização:', response.data);
        alert('✅ Produto atualizado com sucesso! As alterações foram salvas.');
      } else {
        console.log('➕ Criando novo produto');
        response = await produtoService.criar(formData);
        alert('Produto criado com sucesso!');
      }

      console.log('✅ Produto salvo com sucesso:', response.data);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('❌ Erro ao salvar produto:', error);
      console.error('📋 Detalhes do erro:', error.response?.data);

      const errorMessage = formatValidationError(error);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={produto ? 'Editar Produto' : 'Novo Produto'}
      maxWidth="600px"
    >
      <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {produto && (
          <Paper sx={{ p: 2, bgcolor: '#fff3cd', borderLeft: '4px solid #ffc107' }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#856404', mb: 1 }}>
              ⚠️ Editando Produto Existente
            </Typography>
            <Typography variant="body2" sx={{ color: '#856404' }}>
              • <strong>Campos Editáveis:</strong> Denominação, Meses para Exumar e Observação
              <br />
              • <strong>Campos Protegidos:</strong> Cliente e Código da Estação (não podem ser alterados)
              <br />
              • <strong>Para alterar códigos:</strong> Delete este produto e todos os dados filhos, depois crie um novo
              <br />
              • <strong>Impacto da deleção:</strong> Remove blocos, sub-blocos, gavetas e sepultamentos associados
            </Typography>
          </Paper>
        )}

        {!produto && (
          <Paper sx={{ p: 2, bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#2e7d32', mb: 1 }}>
              ✨ Criando Novo Produto
            </Typography>
            <Typography variant="body2" sx={{ color: '#2e7d32' }}>
              • <strong>Defina cuidadosamente:</strong> Cliente e Código da Estação (não poderão ser alterados depois)
              <br />
              • <strong>Código da Estação:</strong> Use um padrão consistente (ex: ETEN_001, BRM_001)
              <br />
              • <strong>Após criação:</strong> Você poderá adicionar blocos, sub-blocos e gavetas
            </Typography>
          </Paper>
        )}

        {!produto && (
          <>
            <FormControl fullWidth required>
              <InputLabel>Cliente</InputLabel>
              <Select
                name="codigo_cliente"
                value={formData.codigo_cliente}
                label="Cliente"
                onChange={handleChange}
              >
                <MenuItem value="">Selecione um cliente</MenuItem>
                {clientes.map(cliente => (
                  <MenuItem key={cliente.codigo_cliente} value={cliente.codigo_cliente}>
                    {cliente.nome_fantasia} ({cliente.codigo_cliente})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              required
              label="Código da Estação"
              name="codigo_estacao"
              value={formData.codigo_estacao}
              onChange={handleChange}
              placeholder="Ex: ETEN_001"
              helperText="Defina um código único para a estação"
            />
          </>
        )}

        {produto && (
          <Paper sx={{ p: 2, bgcolor: '#e3f2fd', borderLeft: '4px solid #2196f3', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 1 }}>
              📝 Produto em Edição
            </Typography>
            <Typography variant="body2" sx={{ color: '#1976d2' }}>
              <strong>Cliente:</strong> {clientes.find(c => c.codigo_cliente === formData.codigo_cliente)?.nome_fantasia} ({formData.codigo_cliente})
              <br />
              <strong>Código da Estação:</strong> {formData.codigo_estacao}
            </Typography>
          </Paper>
        )}

        <TextField
          fullWidth
          required
          type="number"
          label="Meses para Exumar"
          name="meses_para_exumar"
          value={formData.meses_para_exumar}
          onChange={handleChange}
          inputProps={{ min: 1, max: 120 }}
          placeholder="Ex: 24"
        />

        <TextField
          fullWidth
          required
          label="Denominação"
          name="denominacao"
          value={formData.denominacao}
          onChange={handleChange}
          placeholder="Ex: ESTAÇÃO DE TRATAMENTO 001"
        />

        <TextField
          fullWidth
          multiline
          rows={3}
          label="Observação"
          name="observacao"
          value={formData.observacao}
          onChange={handleChange}
          placeholder="Detalhes gerais da estação..."
        />

        {error && (
          <Alert severity="error">
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
          <StandardButton
            variant="outlined"
            onClick={onClose}
          >
            Cancelar
          </StandardButton>
          <StandardButton
            type="submit"
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Salvando...' : (produto ? 'Atualizar' : 'Criar')}
          </StandardButton>
        </Box>
      </Box>
    </Modal>
  );
};

export default ProdutoModal;
