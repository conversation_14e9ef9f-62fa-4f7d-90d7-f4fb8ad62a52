import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import api from '../services/api';

const TransferenciaGavetasModal = ({ open, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [statusData, setStatusData] = useState(null);
  const [transferResult, setTransferResult] = useState(null);
  const [error, setError] = useState('');

  useEffect(() => {
    if (open) {
      verificarStatus();
    }
  }, [open]);

  const verificarStatus = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await api.get('/transferencia-gavetas/verificar-status-itv001');
      setStatusData(response.data);
    } catch (error) {
      console.error('Erro ao verificar status:', error);
      setError('Erro ao verificar status da transferência: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const executarTransferencia = async () => {
    try {
      setLoading(true);
      setError('');
      setTransferResult(null);
      
      const response = await api.post('/transferencia-gavetas/executar-transferencia-itv001');
      setTransferResult(response.data);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao executar transferência:', error);
      setError('Erro ao executar transferência: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setStatusData(null);
    setTransferResult(null);
    setError('');
    onClose();
  };

  const renderStatusInfo = () => {
    if (!statusData) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          <InfoIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Status Atual da Transferência
        </Typography>
        
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Cliente:</strong> ITV_001 | <strong>Estação:</strong> ETEN_002 | <strong>Bloco:</strong> BL_001
            <br />
            <strong>Origem:</strong> SUB_002 → <strong>Destino:</strong> SUB_003
            <br />
            <strong>Total de gavetas a transferir:</strong> {statusData.total_gavetas_transferir}
          </Typography>
        </Alert>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Sub-blocos ({statusData.sub_blocos?.length || 0})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Código</TableCell>
                    <TableCell>Denominação</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {statusData.sub_blocos?.map((subBloco) => (
                    <TableRow key={subBloco.codigo_sub_bloco}>
                      <TableCell>{subBloco.codigo_sub_bloco}</TableCell>
                      <TableCell>{subBloco.denominacao}</TableCell>
                      <TableCell>
                        <Chip 
                          label={subBloco.ativo ? 'Ativo' : 'Inativo'} 
                          color={subBloco.ativo ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Gavetas no SUB_002 ({statusData.gavetas_origem?.length || 0})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300 }}>
              <Table size="small" stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell>Número</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Ativo</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {statusData.gavetas_origem?.map((gaveta) => (
                    <TableRow key={gaveta.numero_gaveta}>
                      <TableCell>{gaveta.numero_gaveta}</TableCell>
                      <TableCell>
                        <Chip 
                          label={gaveta.disponivel ? 'Disponível' : 'Ocupada'} 
                          color={gaveta.disponivel ? 'success' : 'warning'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={gaveta.ativo ? 'Sim' : 'Não'} 
                          color={gaveta.ativo ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Sepultamentos Existentes ({statusData.sepultamentos?.length || 0})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            {statusData.sepultamentos?.length > 0 ? (
              <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300 }}>
                <Table size="small" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Gaveta</TableCell>
                      <TableCell>Nome</TableCell>
                      <TableCell>Data Sepultamento</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {statusData.sepultamentos.map((sep, index) => (
                      <TableRow key={index}>
                        <TableCell>{sep.numero_gaveta}</TableCell>
                        <TableCell>{sep.nome_sepultado}</TableCell>
                        <TableCell>{new Date(sep.data_sepultamento).toLocaleDateString('pt-BR')}</TableCell>
                        <TableCell>
                          <Chip 
                            label={sep.status} 
                            color={sep.status === 'ATIVO' ? 'warning' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" color="text.secondary">
                Nenhum sepultamento encontrado nas gavetas a serem transferidas.
              </Typography>
            )}
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">
              Ranges de Numeração ({statusData.ranges?.length || 0})
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Sub-bloco</TableCell>
                    <TableCell>Início</TableCell>
                    <TableCell>Fim</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {statusData.ranges?.map((range, index) => (
                    <TableRow key={index}>
                      <TableCell>{range.codigo_sub_bloco}</TableCell>
                      <TableCell>{range.numero_inicio}</TableCell>
                      <TableCell>{range.numero_fim}</TableCell>
                      <TableCell>
                        <Chip 
                          label={range.ativo ? 'Ativo' : 'Inativo'} 
                          color={range.ativo ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AccordionDetails>
        </Accordion>
      </Box>
    );
  };

  const renderTransferResult = () => {
    if (!transferResult) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <Alert severity="success" sx={{ mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Transferência Concluída com Sucesso!
          </Typography>
        </Alert>

        <Typography variant="body1" gutterBottom>
          <strong>Resultados:</strong>
        </Typography>
        <ul>
          <li>Gavetas transferidas: {transferResult.detalhes?.gavetas_transferidas || 0}</li>
          <li>Sepultamentos transferidos: {transferResult.detalhes?.sepultamentos_transferidos || 0}</li>
          <li>Ranges removidos: {transferResult.detalhes?.ranges_removidos || 0}</li>
        </ul>

        {transferResult.detalhes?.verificacao_final && (
          <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Verificação</TableCell>
                  <TableCell>Quantidade</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {transferResult.detalhes.verificacao_final.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.tipo}</TableCell>
                    <TableCell>{item.quantidade}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
    );
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center">
          <WarningIcon sx={{ mr: 1, color: 'warning.main' }} />
          Transferência de Gavetas - ITV_001
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading && (
          <Box display="flex" justifyContent="center" alignItems="center" py={3}>
            <CircularProgress />
            <Typography variant="body1" sx={{ ml: 2 }}>
              {transferResult ? 'Executando transferência...' : 'Verificando status...'}
            </Typography>
          </Box>
        )}

        {!loading && !transferResult && renderStatusInfo()}
        {!loading && transferResult && renderTransferResult()}

        {!loading && !transferResult && statusData && (
          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>ATENÇÃO:</strong> Esta operação irá transferir {statusData.total_gavetas_transferir} gavetas 
              específicas do SUB_002 para o SUB_003, preservando todos os sepultamentos existentes. 
              O range 1279-1444 será removido do SUB_002. Esta ação não pode ser desfeita facilmente.
            </Typography>
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          {transferResult ? 'Fechar' : 'Cancelar'}
        </Button>
        
        {!transferResult && statusData?.pronto_para_transferencia && (
          <Button 
            onClick={executarTransferencia} 
            variant="contained" 
            color="warning"
            disabled={loading}
          >
            Executar Transferência
          </Button>
        )}
        
        {!loading && !transferResult && (
          <Button onClick={verificarStatus} variant="outlined">
            Atualizar Status
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default TransferenciaGavetasModal;
