import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { produtoService } from '../services/api';

const Container = styled.div`
  max-height: 70vh;
  overflow-y: auto;
`;

const Section = styled.div`
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  color: #1f2937;
  margin: 0 0 16px 0;
  font-size: 1.25rem;
  font-weight: 600;
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const InfoLabel = styled.span`
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
`;

const InfoValue = styled.span`
  color: #1f2937;
  font-weight: 500;
`;

const BlocoCard = styled.div`
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
`;

const BlocoHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 12px;
`;

const BlocoTitle = styled.h4`
  color: #374151;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
`;

const SubBlocoList = styled.div`
  margin-top: 16px;
`;

const SubBlocoItem = styled.div`
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SubBlocoHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const SubBlocoName = styled.span`
  font-weight: 500;
  color: #374151;
`;

const RangeTag = styled.span`
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 48px;
  color: #6b7280;
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
`;

const EmptyMessage = styled.div`
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 16px;
`;

const ProdutoDetalheModal = ({ isOpen, onClose, produtoId }) => {
  const [produto, setProduto] = useState(null);
  const [blocos, setBlocos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen && produtoId) {
      loadProdutoCompleto();
    }
  }, [isOpen, produtoId]);

  const loadProdutoCompleto = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await produtoService.buscarCompleto(produtoId);
      setProduto(response.data.produto);
      setBlocos(response.data.blocos);
    } catch (error) {
      console.error('Erro ao carregar produto completo:', error);
      setError('Erro ao carregar informações do produto');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setProduto(null);
    setBlocos([]);
    setError('');
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={produto ? `Detalhes: ${produto.denominacao}` : 'Detalhes do Produto'}
      maxWidth="900px"
    >
      <Container>
        {loading && <LoadingMessage>Carregando informações...</LoadingMessage>}
        
        {error && <ErrorMessage>{error}</ErrorMessage>}
        
        {produto && !loading && (
          <>
            {/* Informações do Produto */}
            <Section>
              <SectionTitle>Informações da Estação</SectionTitle>
              <InfoGrid>
                <InfoItem>
                  <InfoLabel>Código da Estação</InfoLabel>
                  <InfoValue>{produto.codigo_estacao}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Cliente</InfoLabel>
                  <InfoValue>{produto.codigo_cliente}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Denominação</InfoLabel>
                  <InfoValue>{produto.denominacao}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Meses para Exumar</InfoLabel>
                  <InfoValue>{produto.meses_para_exumar} meses</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Status</InfoLabel>
                  <InfoValue>{produto.ativo ? 'Ativo' : 'Inativo'}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Data de Criação</InfoLabel>
                  <InfoValue>
                    {produto.created_at ? new Date(produto.created_at).toLocaleDateString('pt-BR') : '-'}
                  </InfoValue>
                </InfoItem>
              </InfoGrid>
              {produto.observacao && (
                <InfoItem>
                  <InfoLabel>Observações</InfoLabel>
                  <InfoValue>{produto.observacao}</InfoValue>
                </InfoItem>
              )}
            </Section>

            {/* Blocos e Sub-blocos */}
            <Section>
              <SectionTitle>Blocos Cadastrados ({blocos.length})</SectionTitle>
              
              {blocos.length === 0 ? (
                <EmptyMessage>Nenhum bloco cadastrado para esta estação.</EmptyMessage>
              ) : (
                blocos.map(bloco => (
                  <BlocoCard key={bloco.id}>
                    <BlocoHeader>
                      <BlocoTitle>{bloco.denominacao || bloco.nome} ({bloco.codigo_bloco})</BlocoTitle>
                    </BlocoHeader>
                    
                    <InfoGrid>
                      <InfoItem>
                        <InfoLabel>Sub-blocos</InfoLabel>
                        <InfoValue>{bloco.total_sub_blocos || 0}</InfoValue>
                      </InfoItem>
                      <InfoItem>
                        <InfoLabel>Total de Gavetas</InfoLabel>
                        <InfoValue>{bloco.total_gavetas || 0}</InfoValue>
                      </InfoItem>
                      <InfoItem>
                        <InfoLabel>Status</InfoLabel>
                        <InfoValue>{bloco.ativo ? 'Ativo' : 'Inativo'}</InfoValue>
                      </InfoItem>
                    </InfoGrid>

                    {bloco.descricao && (
                      <InfoItem>
                        <InfoLabel>Observações</InfoLabel>
                        <InfoValue>{bloco.descricao}</InfoValue>
                      </InfoItem>
                    )}

                    {/* Sub-blocos */}
                    {bloco.sub_blocos && bloco.sub_blocos.length > 0 && (
                      <SubBlocoList>
                        <InfoLabel style={{ marginBottom: '8px', display: 'block' }}>
                          Sub-blocos ({bloco.sub_blocos.length}):
                        </InfoLabel>
                        {bloco.sub_blocos.map(subBloco => (
                          <SubBlocoItem key={subBloco.id}>
                            <SubBlocoHeader>
                              <SubBlocoName>
                                {subBloco.nome} ({subBloco.codigo_sub_bloco})
                              </SubBlocoName>
                              {subBloco.ranges_gavetas && (
                                <RangeTag>
                                  Gavetas: {subBloco.ranges_gavetas}
                                </RangeTag>
                              )}
                            </SubBlocoHeader>
                            
                            <InfoGrid style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))' }}>
                              <InfoItem>
                                <InfoLabel>Total de Gavetas</InfoLabel>
                                <InfoValue>{subBloco.total_gavetas || 0}</InfoValue>
                              </InfoItem>
                              <InfoItem>
                                <InfoLabel>Status</InfoLabel>
                                <InfoValue>{subBloco.ativo ? 'Ativo' : 'Inativo'}</InfoValue>
                              </InfoItem>
                            </InfoGrid>

                            {subBloco.descricao && (
                              <InfoItem style={{ marginTop: '8px' }}>
                                <InfoLabel>Observações</InfoLabel>
                                <InfoValue>{subBloco.descricao}</InfoValue>
                              </InfoItem>
                            )}
                          </SubBlocoItem>
                        ))}
                      </SubBlocoList>
                    )}
                  </BlocoCard>
                ))
              )}
            </Section>
          </>
        )}
      </Container>
    </Modal>
  );
};

export default ProdutoDetalheModal;
