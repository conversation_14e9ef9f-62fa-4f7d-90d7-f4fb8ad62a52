import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Chip,
  Divider,
  Grid,
  Paper,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Notes as NotesIcon,
  RemoveCircle as ExumarIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { StandardButton } from './common';

// Componente para exibir informações em formato de card
const InfoCard = ({ icon, title, value, color = 'text.secondary' }) => (
  <Paper
    elevation={1}
    sx={{
      p: 2,
      borderRadius: 2,
      border: '1px solid',
      borderColor: 'grey.200',
      backgroundColor: 'background.paper',
    }}
  >
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
      {icon && (
        <Box sx={{ mr: 1, color, display: 'flex', alignItems: 'center' }}>
          {icon}
        </Box>
      )}
      <Typography variant="body2" color="text.secondary" fontWeight={500}>
        {title}
      </Typography>
    </Box>
    <Typography variant="body1" fontWeight={600} color="text.primary">
      {value || 'Não informado'}
    </Typography>
  </Paper>
);

// Componente para seções do modal
const ModalSection = ({ title, children, icon }) => (
  <Box sx={{ mb: 3 }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
      {icon && (
        <Box sx={{ mr: 1, color: 'primary.main', display: 'flex', alignItems: 'center' }}>
          {icon}
        </Box>
      )}
      <Typography variant="h6" fontWeight={600} color="primary.main">
        {title}
      </Typography>
    </Box>
    {children}
  </Box>
);

const SepultamentoDetailsModal = ({ open, onClose, sepultamento }) => {
  if (!sepultamento) return null;

  // Função para formatar data
  const formatDate = (dateString) => {
    if (!dateString) return 'Não informado';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('pt-BR');
    } catch (error) {
      return 'Data inválida';
    }
  };

  // Função para formatar horário
  const formatTime = (timeString) => {
    if (!timeString) return 'Não informado';
    return timeString;
  };

  // Determinar status e cor
  const getStatusInfo = () => {
    // LÓGICA CORRIGIDA - Verificar se está exumado
    // Um sepultamento está exumado se tem data real de exumação (exumado_em)
    const isExumado = sepultamento.exumado_em !== null && sepultamento.exumado_em !== undefined;

    if (isExumado) {
      return {
        label: 'Exumado',
        color: 'error',
        icon: <ExumarIcon fontSize="small" />
      };
    }
    return {
      label: 'Sepultado',
      color: 'success',
      icon: <CheckCircleIcon fontSize="small" />
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '90vh',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 1,
          borderBottom: '1px solid',
          borderColor: 'grey.200',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h5" fontWeight={600}>
            Detalhes do Sepultamento
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {/* Status do Sepultamento */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
          <Chip
            icon={statusInfo.icon}
            label={statusInfo.label}
            color={statusInfo.color}
            variant="filled"
            size="large"
            sx={{
              fontSize: '1rem',
              fontWeight: 600,
              px: 2,
              py: 1,
            }}
          />
        </Box>

        {/* Informações Principais do Sepultado */}
        <ModalSection title="Informações do Sepultado" icon={<PersonIcon />}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <InfoCard
                icon={<PersonIcon fontSize="small" />}
                title="Nome Completo"
                value={sepultamento.nome_sepultado}
              />
            </Grid>
          </Grid>
        </ModalSection>

        <Divider sx={{ my: 3 }} />

        {/* Informações de Localização */}
        <ModalSection title="Localização" icon={<LocationIcon />}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<BusinessIcon fontSize="small" />}
                title="Produto/Estação"
                value={sepultamento.produto_denominacao || `Estação ${sepultamento.codigo_estacao}` || 'Não informado'}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<LocationIcon fontSize="small" />}
                title="Bloco"
                value={sepultamento.denominacao_bloco || sepultamento.bloco_nome || sepultamento.codigo_bloco || 'Não informado'}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<LocationIcon fontSize="small" />}
                title="Número da Gaveta"
                value={sepultamento.numero_gaveta}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<LocationIcon fontSize="small" />}
                title="Localização Completa"
                value={
                  (sepultamento.denominacao_bloco || sepultamento.bloco_nome || sepultamento.codigo_bloco)
                    ? `${sepultamento.denominacao_bloco || sepultamento.bloco_nome || sepultamento.codigo_bloco} → Gaveta ${sepultamento.numero_gaveta}`
                    : `Gaveta ${sepultamento.numero_gaveta}`
                }
              />
            </Grid>
          </Grid>
        </ModalSection>

        <Divider sx={{ my: 3 }} />

        {/* Informações de Sepultamento */}
        <ModalSection title="Dados do Sepultamento" icon={<CalendarIcon />}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<CalendarIcon fontSize="small" />}
                title="Data do Sepultamento"
                value={formatDate(sepultamento.data_sepultamento)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <InfoCard
                icon={<ScheduleIcon fontSize="small" />}
                title="Horário do Sepultamento"
                value={formatTime(sepultamento.horario_sepultamento)}
              />
            </Grid>
          </Grid>
        </ModalSection>

        {/* Informações de Exumação */}
        {sepultamento.data_exumacao && (
          <>
            <Divider sx={{ my: 3 }} />
            <ModalSection
              title={statusInfo.label === 'Exumado' ? "Dados da Exumação" : "Dados de Exumação Prevista"}
              icon={<ExumarIcon />}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <InfoCard
                    icon={<CalendarIcon fontSize="small" />}
                    title={statusInfo.label === 'Exumado' ? "Data da Exumação" : "Data Prevista de Exumação"}
                    value={formatDate(sepultamento.data_exumacao)}
                    color={statusInfo.label === 'Exumado' ? "error.main" : "warning.main"}
                  />
                </Grid>
                {statusInfo.label === 'Exumado' && sepultamento.horario_exumacao && (
                  <Grid item xs={12} sm={6}>
                    <InfoCard
                      icon={<ScheduleIcon fontSize="small" />}
                      title="Horário da Exumação"
                      value={formatTime(sepultamento.horario_exumacao)}
                      color="error.main"
                    />
                  </Grid>
                )}
                {statusInfo.label === 'Exumado' && sepultamento.observacoes_exumacao && (
                  <Grid item xs={12}>
                    <InfoCard
                      icon={<NotesIcon fontSize="small" />}
                      title="Observações da Exumação"
                      value={sepultamento.observacoes_exumacao}
                      color="error.main"
                    />
                  </Grid>
                )}
              </Grid>
            </ModalSection>
          </>
        )}

        {/* Observações Gerais */}
        {sepultamento.observacoes && (
          <>
            <Divider sx={{ my: 3 }} />
            <ModalSection title="Observações" icon={<NotesIcon />}>
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'grey.200',
                  backgroundColor: 'grey.50',
                }}
              >
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {sepultamento.observacoes}
                </Typography>
              </Paper>
            </ModalSection>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, borderTop: '1px solid', borderColor: 'grey.200' }}>
        <StandardButton
          variant="contained"
          onClick={onClose}
          sx={{ minWidth: 120 }}
        >
          Fechar
        </StandardButton>
      </DialogActions>
    </Dialog>
  );
};

export default SepultamentoDetailsModal;
