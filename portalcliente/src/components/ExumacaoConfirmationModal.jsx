import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Chip,
  Divider,
  Grid,
  Paper,
  IconButton,
  Alert,
  TextField,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Schedule as ScheduleIcon,
  LocationOn as LocationIcon,
  Business as BusinessIcon,
  Warning as WarningIcon,
  RemoveCircle as ExumarIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { StandardButton } from './common';

const ExumacaoConfirmationModal = ({ 
  open, 
  onClose, 
  sepultamento, 
  onConfirm,
  loading = false 
}) => {
  const [dataExumacao, setDataExumacao] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [observacoes, setObservacoes] = useState('');

  if (!sepultamento) return null;

  // Função para formatar datas
  const formatDate = (dateString) => {
    if (!dateString) return 'Não informado';
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Função para formatar horários
  const formatTime = (timeString) => {
    if (!timeString) return 'Não informado';
    return timeString;
  };

  const handleConfirm = () => {
    onConfirm({
      data_exumacao: dataExumacao,
      observacoes_exumacao: observacoes || 'Exumação realizada via sistema'
    });
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          pb: 1,
          borderBottom: '1px solid',
          borderColor: 'divider',
          backgroundColor: 'error.main',
          color: 'white',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <ExumarIcon sx={{ mr: 1 }} />
          <Typography variant="h5" component="h2">
            Confirmação de Exumação
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small" sx={{ color: 'white' }} disabled={loading}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {/* Alerta de Aviso */}
        <Alert 
          severity="warning" 
          icon={<WarningIcon />}
          sx={{ mb: 3 }}
        >
          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
            ⚠️ ATENÇÃO: Esta ação é irreversível!
          </Typography>
          <Typography variant="body2">
            Após confirmar a exumação, o registro não poderá ser excluído do banco de dados.
            A gaveta será liberada automaticamente para novos sepultamentos.
          </Typography>
        </Alert>

        {/* Informações do Sepultado */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mb: 3,
            backgroundColor: 'grey.50',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" color="primary.main">
              Dados do Sepultado
            </Typography>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PersonIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Nome:
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                  {sepultamento.nome_sepultado || 'Não informado'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CalendarIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Data Sepultamento:
                </Typography>
                <Typography variant="body2">
                  {formatDate(sepultamento.data_sepultamento)}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Horário:
                </Typography>
                <Typography variant="body2">
                  {formatTime(sepultamento.horario_sepultamento)}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Informações de Localização */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mb: 3,
            backgroundColor: 'grey.50',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <LocationIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" color="primary.main">
              Localização
            </Typography>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <BusinessIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Produto:
                </Typography>
                <Typography variant="body2">
                  {sepultamento.produto_denominacao || sepultamento.codigo_estacao || 'Não informado'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Bloco:
                </Typography>
                <Typography variant="body2">
                  {sepultamento.denominacao_bloco || sepultamento.bloco_nome || sepultamento.codigo_bloco || 'Não informado'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Sub-bloco:
                </Typography>
                <Typography variant="body2">
                  {sepultamento.codigo_sub_bloco || 'Não informado'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Gaveta:
                </Typography>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                  {sepultamento.numero_gaveta || 'Não informado'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        <Divider sx={{ my: 3 }} />

        {/* Dados da Exumação */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ExumarIcon sx={{ mr: 1, color: 'error.main' }} />
            <Typography variant="h6" color="error.main">
              Dados da Exumação
            </Typography>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="date"
                label="Data da Exumação"
                value={dataExumacao}
                onChange={(e) => setDataExumacao(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
                required
                disabled={loading}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Observações (opcional)"
                value={observacoes}
                onChange={(e) => setObservacoes(e.target.value)}
                placeholder="Digite observações sobre a exumação..."
                disabled={loading}
              />
            </Grid>
          </Grid>
        </Box>

        {/* Informação Adicional */}
        <Alert 
          severity="info" 
          icon={<InfoIcon />}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            A gaveta será automaticamente liberada e ficará disponível para novos sepultamentos.
          </Typography>
        </Alert>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <StandardButton
              variant="outlined"
              onClick={handleClose}
              fullWidth
              disabled={loading}
            >
              Cancelar
            </StandardButton>
          </Grid>
          <Grid item xs={12} sm={6}>
            <StandardButton
              variant="contained"
              color="error"
              onClick={handleConfirm}
              fullWidth
              disabled={loading || !dataExumacao}
              startIcon={<ExumarIcon />}
            >
              {loading ? 'Processando...' : 'Confirmar Exumação'}
            </StandardButton>
          </Grid>
        </Grid>
      </DialogActions>
    </Dialog>
  );
};

export default ExumacaoConfirmationModal;
