import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Alert,
  Chip,
} from '@mui/material';
import {
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { StandardButton } from './common';

const ConfirmationModal = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  type = 'warning', // 'warning', 'success', 'info', 'error'
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  loading = false,
  details = null,
  itemName = '',
  action = '',
  consequences = [],
  dataIntegrity = null,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckIcon sx={{ color: 'success.main', fontSize: 40 }} />;
      case 'info':
        return <InfoIcon sx={{ color: 'info.main', fontSize: 40 }} />;
      case 'error':
        return <ErrorIcon sx={{ color: 'error.main', fontSize: 40 }} />;
      default:
        return <WarningIcon sx={{ color: 'warning.main', fontSize: 40 }} />;
    }
  };

  const getColor = () => {
    switch (type) {
      case 'success':
        return 'success';
      case 'info':
        return 'info';
      case 'error':
        return 'error';
      default:
        return 'warning';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3,
        }
      }}
    >
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
          {getIcon()}
          <Typography variant="h6" component="div" fontWeight="bold">
            {title}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* Mensagem principal */}
          <Typography variant="body1" sx={{ textAlign: 'center', fontSize: '1.1rem' }}>
            {message}
          </Typography>

          {/* Nome do item */}
          {itemName && (
            <Box sx={{ textAlign: 'center' }}>
              <Chip
                label={itemName}
                color="primary"
                variant="outlined"
                sx={{ fontSize: '0.9rem', fontWeight: 'bold' }}
              />
            </Box>
          )}

          {/* Consequências */}
          {consequences.length > 0 && (
            <Alert severity={getColor()} sx={{ mt: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                {action === 'inativar' ? 'O que acontecerá:' : 'Efeitos da ação:'}
              </Typography>
              <Box component="ul" sx={{ m: 0, pl: 2 }}>
                {consequences.map((consequence, index) => (
                  <Typography component="li" key={index} variant="body2">
                    {consequence}
                  </Typography>
                ))}
              </Box>
            </Alert>
          )}

          {/* Integridade de dados */}
          {dataIntegrity && (
            <Alert severity="info" sx={{ mt: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                🛡️ Garantia de Integridade:
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                Todos os dados relacionados serão <strong>mantidos intactos</strong>:
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Object.entries(dataIntegrity).map(([key, value]) => (
                  <Chip
                    key={key}
                    label={`${value} ${key}`}
                    size="small"
                    variant="outlined"
                    color="info"
                  />
                ))}
              </Box>
            </Alert>
          )}

          {/* Detalhes adicionais */}
          {details && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color="text.secondary">
                {details}
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1, gap: 1 }}>
        <StandardButton
          variant="outlined"
          onClick={onClose}
          disabled={loading}
          sx={{ minWidth: 100 }}
        >
          {cancelText}
        </StandardButton>
        <StandardButton
          variant="contained"
          color={getColor()}
          onClick={onConfirm}
          disabled={loading}
          sx={{ minWidth: 100 }}
        >
          {loading ? 'Processando...' : confirmText}
        </StandardButton>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationModal;
