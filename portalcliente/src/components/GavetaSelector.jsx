import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { produtoService, gavetaService } from '../services/api';

const SelectorContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 600;
  color: #1976d2;
  font-size: 1.1rem;
  margin-bottom: 8px;
  display: block;
`;

const Select = styled.select`
  padding: 18px 16px;
  border: 2px solid #d1d5db;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 500;
  background-color: white;
  transition: all 0.2s ease;
  min-height: 60px;
  line-height: 1.5;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.15);
    transform: translateY(-1px);
  }

  &:hover:not(:disabled) {
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  option {
    padding: 12px;
    font-size: 1rem;
    font-weight: 500;
  }
`;

const InfoText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
`;

const GavetaSelector = ({ 
  value = {}, 
  onChange, 
  codigoCliente = null,
  disabled = false 
}) => {
  const [produtos, setProdutos] = useState([]);
  const [blocos, setBlocos] = useState([]);
  const [subBlocos, setSubBlocos] = useState([]);
  const [gavetas, setGavetas] = useState([]);
  const [loading, setLoading] = useState(false);

  const {
    produto_id = '',
    bloco_id = '',
    sub_bloco_id = '',
    gaveta_id = ''
  } = value;

  useEffect(() => {
    loadProdutos();
  }, [codigoCliente]);

  useEffect(() => {
    if (produto_id) {
      loadBlocos(produto_id);
    } else {
      setBlocos([]);
      setSubBlocos([]);
      setGavetas([]);
    }
  }, [produto_id]);

  useEffect(() => {
    if (bloco_id) {
      loadGavetasPorBloco(bloco_id);
    } else {
      setGavetas([]);
    }
  }, [bloco_id]);

  useEffect(() => {
    if (bloco_id) {
      loadGavetasPorBloco(bloco_id); // Carregar gavetas de todos os sub-blocos do bloco automaticamente
    } else {
      setGavetas([]);
    }
  }, [bloco_id]);

  const loadProdutos = async () => {
    try {
      setLoading(true);
      const params = codigoCliente ? { codigo_cliente: codigoCliente } : {};
      const response = await produtoService.listar(params);
      setProdutos(response.data || []);
    } catch (error) {
      console.error('❌ Erro ao carregar produtos:', error);
      // Se houver erro de autenticação, não fazer nada para evitar redirect
      if (error.response?.status === 401) {
        console.error('❌ Erro de autenticação ao carregar produtos');
      }
      setProdutos([]);
    } finally {
      setLoading(false);
    }
  };

  const loadBlocos = async (produtoId) => {
    try {
      setLoading(true);
      console.log('🔍 GavetaSelector: Carregando blocos para produto ID:', produtoId);

      // Buscar o produto primeiro para obter os códigos
      let produto = produtos.find(p => p.id == produtoId);

      // Se não encontrou na lista local, buscar diretamente da API
      if (!produto) {
        console.log('🔄 GavetaSelector: Produto não encontrado na lista local, buscando da API...');
        const produtoResponse = await produtoService.obter(produtoId);
        produto = produtoResponse.data;
      }

      if (!produto) {
        console.error('❌ GavetaSelector: Produto não encontrado para ID:', produtoId);
        setBlocos([]);
        setSubBlocos([]);
        setGavetas([]);
        return;
      }

      console.log('✅ GavetaSelector: Produto encontrado:', produto);

      // Usar codigoCliente se disponível, senão usar do produto
      const clienteCode = codigoCliente || produto.codigo_cliente;

      const response = await produtoService.listarBlocos(clienteCode, produto.codigo_estacao);
      console.log('✅ GavetaSelector: Blocos carregados:', response.data);
      const blocosCarregados = response.data || [];
      setBlocos(blocosCarregados);

      // PRÉ-SELEÇÃO AUTOMÁTICA: Selecionar o primeiro bloco disponível se não há bloco selecionado
      if (blocosCarregados.length > 0 && !bloco_id) {
        const primeiroBloco = blocosCarregados[0];
        console.log('🎯 GavetaSelector: Pré-selecionando primeiro bloco:', primeiroBloco);
        onChange({
          ...value,
          bloco_id: primeiroBloco.id
        });
      }

      // Limpar sub-blocos e gavetas quando trocar de produto
      setSubBlocos([]);
      setGavetas([]);
    } catch (error) {
      console.error('❌ GavetaSelector: Erro ao carregar blocos:', error);
      setBlocos([]);
      setSubBlocos([]);
      setGavetas([]);
    } finally {
      setLoading(false);
    }
  };

  const loadSubBlocos = async (blocoId) => {
    try {
      // Buscar o bloco para obter os códigos
      const bloco = blocos.find(b => b.id == blocoId);
      const produto = produtos.find(p => p.id == produto_id);

      if (!bloco || !produto) {
        console.error('Bloco ou produto não encontrado');
        return;
      }

      const response = await produtoService.listarSubBlocos(
        produto.codigo_cliente,
        produto.codigo_estacao,
        bloco.codigo_bloco
      );
      setSubBlocos(response.data);
    } catch (error) {
      console.error('Erro ao carregar sub-blocos:', error);
    }
  };

  const loadGavetas = async (subBlocoId) => {
    try {
      const response = await gavetaService.listarPorSubBloco(subBlocoId, { disponivel: true });
      setGavetas(response.data);
    } catch (error) {
      console.error('Erro ao carregar gavetas:', error);
    }
  };

  const loadGavetasPorBloco = async (blocoId) => {
    try {
      console.log('🔍 GavetaSelector: Carregando gavetas para bloco ID:', blocoId);

      // Buscar o bloco para obter os códigos
      const bloco = blocos.find(b => b.id == blocoId);
      if (!bloco) {
        console.error('❌ GavetaSelector: Bloco não encontrado para ID:', blocoId);
        return;
      }

      // Buscar o produto
      let produto = produtos.find(p => p.id == produto_id);
      if (!produto && produto_id) {
        console.log('🔄 GavetaSelector: Produto não encontrado na lista local, buscando da API...');
        const produtoResponse = await produtoService.obter(produto_id);
        produto = produtoResponse.data;
      }

      if (!produto) {
        console.error('❌ GavetaSelector: Produto não encontrado');
        return;
      }

      console.log('✅ GavetaSelector: Carregando gavetas para:', { produto: produto.denominacao, bloco: bloco.denominacao });

      // Usar codigoCliente se disponível, senão usar do produto
      const clienteCode = codigoCliente || produto.codigo_cliente;

      const subBlocosResponse = await produtoService.listarSubBlocos(
        clienteCode,
        produto.codigo_estacao,
        bloco.codigo_bloco
      );

      console.log('✅ GavetaSelector: Sub-blocos encontrados:', subBlocosResponse.data?.length || 0);

      // Carregar gavetas de todos os sub-blocos do bloco
      let todasGavetas = [];
      for (const subBloco of subBlocosResponse.data || []) {
        try {
          const gavetasResponse = await produtoService.listarGavetas(
            clienteCode,
            produto.codigo_estacao,
            bloco.codigo_bloco,
            subBloco.codigo_sub_bloco,
            { disponivel: true }
          );

          // LÓGICA SIMPLIFICADA: Filtrar apenas gavetas com disponivel = true
          const gavetasDisponiveis = (gavetasResponse.data || []).filter(g => {
            return g.disponivel === true;
          });
          todasGavetas = [...todasGavetas, ...gavetasDisponiveis];

          console.log(`✅ GavetaSelector: ${gavetasDisponiveis.length} gavetas disponíveis no sub-bloco ${subBloco.codigo_sub_bloco}`);
        } catch (error) {
          console.error(`❌ Erro ao carregar gavetas do sub-bloco ${subBloco.codigo_sub_bloco}:`, error);
        }
      }

      console.log('✅ GavetaSelector: Total de gavetas disponíveis:', todasGavetas.length);
      setGavetas(todasGavetas);
    } catch (error) {
      console.error('❌ GavetaSelector: Erro ao carregar gavetas por bloco:', error);
      setGavetas([]);
    }
  };

  const handleChange = (field, newValue) => {
    const newSelection = { ...value };

    // Limpar campos dependentes quando um campo pai muda
    if (field === 'produto_id') {
      newSelection.produto_id = newValue;
      newSelection.bloco_id = '';
      newSelection.sub_bloco_id = '';
      newSelection.gaveta_id = '';
      // Limpar códigos
      delete newSelection.bloco_codigo;
      delete newSelection.sub_bloco_codigo;
      delete newSelection.gaveta_numero;
    } else if (field === 'bloco_id') {
      newSelection.bloco_id = newValue;
      newSelection.gaveta_id = '';
      // Adicionar código do bloco
      const bloco = blocos.find(b => b.id == newValue);
      if (bloco) {
        newSelection.bloco_codigo = bloco.codigo_bloco;
      }
      // Limpar códigos dependentes
      delete newSelection.sub_bloco_codigo;
      delete newSelection.gaveta_numero;
    } else if (field === 'gaveta_id') {
      newSelection.gaveta_id = newValue;
      // Adicionar número da gaveta e sub_bloco_id automaticamente
      const gaveta = gavetas.find(g => g.id == newValue);
      if (gaveta) {
        newSelection.gaveta_numero = gaveta.numero_gaveta;
        newSelection.sub_bloco_id = gaveta.sub_bloco_id;
        newSelection.sub_bloco_codigo = gaveta.codigo_sub_bloco;
      }
    }

    onChange(newSelection);
  };

  const getGavetasDisponiveis = () => {
    return gavetas.filter(gaveta => {
      // Usar status_gaveta se disponível, senão usar lógica legada
      if (gaveta.status_gaveta) {
        return gaveta.status_gaveta === 'disponivel';
      }
      // Fallback para lógica anterior
      return gaveta.disponivel && !gaveta.sepultamento_id && !gaveta.nome_sepultado;
    });
  };

  return (
    <SelectorContainer>
      <FormRow>
        <FormGroup>
          <Label>Produto/Estação *</Label>
          <Select
            value={produto_id}
            onChange={(e) => handleChange('produto_id', e.target.value)}
            disabled={disabled || loading}
            required
          >
            <option value="">Selecione um produto</option>
            {produtos.map(produto => (
              <option key={produto.id} value={produto.id}>
                {produto.denominacao}
              </option>
            ))}
          </Select>
        </FormGroup>

        <FormGroup>
          <Label>Bloco *</Label>
          <Select
            value={bloco_id}
            onChange={(e) => handleChange('bloco_id', e.target.value)}
            disabled={disabled || !produto_id || blocos.length === 0}
            required
          >
            <option value="">Selecione um bloco</option>
            {blocos.map(bloco => (
              <option key={bloco.id} value={bloco.id}>
                {bloco.denominacao}
              </option>
            ))}
          </Select>
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label>Gaveta *</Label>
          <Select
            value={gaveta_id}
            onChange={(e) => handleChange('gaveta_id', e.target.value)}
            disabled={disabled || !bloco_id || gavetas.length === 0}
            required
          >
            <option value="">Selecione uma gaveta</option>
            {getGavetasDisponiveis().map(gaveta => (
              <option key={gaveta.id} value={gaveta.id}>
                Gaveta {gaveta.numero_gaveta}
              </option>
            ))}
          </Select>
        </FormGroup>
      </FormRow>

      {bloco_id && gavetas.length === 0 && (
        <InfoText>
          ⚠️ Nenhuma gaveta disponível neste bloco.
        </InfoText>
      )}

      {bloco_id && gavetas.length > 0 && (
        <InfoText>
          ✅ {getGavetasDisponiveis().length} gaveta(s) disponível(is) de {gavetas.length} total neste bloco.
        </InfoText>
      )}
    </SelectorContainer>
  );
};

export default GavetaSelector;
