import React, { useState, useEffect } from 'react';
import { Button, Typography, Paper } from '@mui/material';
import styled from 'styled-components';
import Modal from './Modal';
import { produtoService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const StyledFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const StyledButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const StyledButton = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  &.primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }

  &.danger {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
`;

const BlocoModal = ({ isOpen, onClose, bloco, produtoId, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_bloco: '',
    nome: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (bloco) {
        setFormData({
          codigo_bloco: bloco.codigo_bloco || '',
          nome: bloco.nome || bloco.denominacao || ''
        });
      } else {
        setFormData({
          codigo_bloco: '',
          nome: ''
        });
      }
      setError('');
    }
  }, [isOpen, bloco]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Preparar dados com denominacao igual ao nome
      const dadosParaEnvio = {
        ...formData,
        denominacao: formData.nome, // denominacao = nome
        descricao: formData.nome    // descricao = nome
      };

      if (bloco) {
        await produtoService.atualizarBlocoSimples(bloco.id, dadosParaEnvio);
        alert('Bloco atualizado com sucesso!');
      } else {
        await produtoService.criarBlocoSimples({
          ...dadosParaEnvio,
          produto_id: produtoId
        });
        alert('Bloco criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar bloco:', error);

      let errorMessage = 'Erro ao salvar bloco';

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias || errorData.acao_necessaria) {
          // Erro de validação hierárquica
          errorMessage = `${errorData.error}\n\n${errorData.detalhes || ''}`;

          if (errorData.acao_necessaria) {
            errorMessage += `\n\n🔧 ${errorData.acao_necessaria}`;
          }

          if (errorData.sub_blocos_encontrados) {
            errorMessage += `\n\n📦 ${errorData.sub_blocos_encontrados} sub-bloco(s) encontrado(s)`;
          }
        } else {
          errorMessage = errorData.error || 'Dados inválidos para salvar o bloco';
        }
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado. Apenas administradores podem editar blocos.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Bloco não encontrado. Pode ter sido removido por outro usuário.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!bloco || !window.confirm('Tem certeza que deseja deletar este bloco? Esta ação não pode ser desfeita.')) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await produtoService.deletarBlocoSimples(bloco.id);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao deletar bloco:', error);

      let errorMessage = 'Erro ao deletar bloco';

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias) {
          // Erro de validação hierárquica
          errorMessage = `❌ ${errorData.error}\n\n📋 ${errorData.detalhes}\n\n🔧 ${errorData.acao_necessaria}`;

          if (errorData.passos_obrigatorios && errorData.passos_obrigatorios.length > 0) {
            errorMessage += '\n\nPassos obrigatórios:\n' +
              errorData.passos_obrigatorios.map((passo, index) => `${index + 1}. ${passo}`).join('\n');
          }

          if (errorData.sub_blocos_nomes) {
            errorMessage += `\n\n📦 Sub-blocos encontrados: ${errorData.sub_blocos_nomes}`;
          }
        } else {
          errorMessage = errorData.error || 'Não é possível deletar este bloco';
        }
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado. Apenas administradores podem deletar blocos.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Bloco não encontrado. Pode ter sido removido por outro usuário.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={bloco ? 'Editar Bloco' : 'Novo Bloco'}
      maxWidth="600px"
    >
      <Form onSubmit={handleSubmit}>
        {bloco && (
          <Paper sx={{ p: 2, bgcolor: '#fff3cd', borderLeft: '4px solid #ffc107', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#856404', mb: 1 }}>
              ⚠️ Editando Bloco Existente
            </Typography>
            <Typography variant="body2" sx={{ color: '#856404' }}>
              • <strong>Campos Editáveis:</strong> Denominação (Nome) e Observações (Descrição)
              <br />
              • <strong>Campo Protegido:</strong> Código do Bloco (não pode ser alterado)
              <br />
              • <strong>Para alterar código:</strong> Delete este bloco e todos os dados filhos, depois crie um novo
              <br />
              • <strong>Impacto da deleção:</strong> Remove sub-blocos, gavetas e sepultamentos associados
            </Typography>
          </Paper>
        )}

        {!bloco && (
          <Paper sx={{ p: 2, bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#2e7d32', mb: 1 }}>
              ✨ Criando Novo Bloco
            </Typography>
            <Typography variant="body2" sx={{ color: '#2e7d32' }}>
              • <strong>Defina cuidadosamente:</strong> Código do Bloco (não poderá ser alterado depois)
              <br />
              • <strong>Padrão recomendado:</strong> Use códigos sequenciais (ex: BL01, BL02, BL03)
              <br />
              • <strong>Após criação:</strong> Você poderá adicionar sub-blocos e definir ranges de gavetas
            </Typography>
          </Paper>
        )}

        {!bloco && (
          <StyledFormGroup>
            <Label>Código do Bloco *</Label>
            <Input
              type="text"
              name="codigo_bloco"
              value={formData.codigo_bloco}
              onChange={handleChange}
              required
              placeholder="Ex: BL01"
            />
          </StyledFormGroup>
        )}

        {bloco && (
          <Paper sx={{ p: 2, bgcolor: '#e3f2fd', borderLeft: '4px solid #2196f3', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 1 }}>
              📝 Bloco em Edição
            </Typography>
            <Typography variant="body2" sx={{ color: '#1976d2' }}>
              <strong>Código do Bloco:</strong> {formData.codigo_bloco}
            </Typography>
          </Paper>
        )}

        <StyledFormGroup>
          <Label>Nome do Bloco *</Label>
          <Input
            type="text"
            name="nome"
            value={formData.nome}
            onChange={handleChange}
            required
            placeholder="Ex: Bloco Principal"
          />
          <Typography variant="caption" sx={{ color: '#666', mt: 1 }}>
            Este nome será usado como denominação e descrição do bloco
          </Typography>
        </StyledFormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButtonGroup>
          <StyledButton type="button" className="secondary" onClick={onClose}>
            Cancelar
          </StyledButton>
          {bloco && (
            <StyledButton
              type="button"
              className="danger"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'Deletando...' : 'Deletar'}
            </StyledButton>
          )}
          <StyledButton type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (bloco ? 'Atualizar' : 'Criar')}
          </StyledButton>
        </StyledButtonGroup>
      </Form>
    </Modal>
  );
};

export default BlocoModal;
