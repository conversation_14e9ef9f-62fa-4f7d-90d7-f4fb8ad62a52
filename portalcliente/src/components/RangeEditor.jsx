import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import rangeService from '../services/rangeService';

/**
 * Componente para edição de ranges de gavetas (CONFORME INSTRUCAO.MD)
 */
const RangeEditor = ({ open, onClose, subBloco, onRangeUpdated }) => {
  const [ranges, setRanges] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [editingRange, setEditingRange] = useState(null);
  const [formData, setFormData] = useState({
    numero_inicio: '',
    numero_fim: ''
  });

  // Carregar ranges ao abrir o dialog
  useEffect(() => {
    if (open && subBloco) {
      loadRanges();
    }
  }, [open, subBloco]);

  const loadRanges = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await rangeService.listRanges(
        subBloco.codigo_cliente,
        subBloco.codigo_estacao,
        subBloco.codigo_bloco,
        subBloco.codigo_sub_bloco
      );

      // Garantir que response é um array
      if (Array.isArray(response)) {
        setRanges(response);
      } else {
        console.warn('Resposta não é um array:', response);
        setRanges([]);
        setError('Formato de dados inválido recebido do servidor');
      }
    } catch (err) {
      console.error('Erro ao carregar ranges:', err);
      setError('Erro ao carregar ranges: ' + (err.message || 'Erro desconhecido'));
      setRanges([]); // Garantir que ranges seja sempre um array
    } finally {
      setLoading(false);
    }
  };

  const handleAddRange = async () => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      const rangeData = {
        codigo_cliente: subBloco.codigo_cliente,
        codigo_estacao: subBloco.codigo_estacao,
        codigo_bloco: subBloco.codigo_bloco,
        codigo_sub_bloco: subBloco.codigo_sub_bloco,
        numero_inicio: parseInt(formData.numero_inicio),
        numero_fim: parseInt(formData.numero_fim)
      };

      // Validar range antes de criar (CONFORME INSTRUCAO.MD)
      const validation = await rangeService.validateRange(rangeData);

      if (!validation.success) {
        setError(`Erro de validação: ${validation.error}`);
        return;
      }

      const response = await rangeService.createRange(rangeData);

      if (response.success) {
        setSuccess('Range adicionado com sucesso!');
        setFormData({ numero_inicio: '', numero_fim: '' });
        loadRanges();
        if (onRangeUpdated) onRangeUpdated();
      } else {
        setError(response.error || 'Erro ao adicionar range');
      }
    } catch (err) {
      setError('Erro ao adicionar range: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRange = async (range) => {
    if (!window.confirm(`Confirma a exclusão do range ${range.numero_inicio}-${range.numero_fim}?`)) {
      return;
    }

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      const response = await rangeService.deleteRange(range.id, {
        codigo_cliente: subBloco.codigo_cliente,
        codigo_estacao: subBloco.codigo_estacao,
        codigo_bloco: subBloco.codigo_bloco,
        codigo_sub_bloco: subBloco.codigo_sub_bloco
      });
      
      if (response.success) {
        setSuccess('Range removido com sucesso!');
        loadRanges();
        if (onRangeUpdated) onRangeUpdated();
      } else {
        setError(response.error || 'Erro ao remover range');
      }
    } catch (err) {
      setError('Erro ao remover range: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = () => {
    const inicio = parseInt(formData.numero_inicio);
    const fim = parseInt(formData.numero_fim);
    return inicio > 0 && fim > 0 && inicio <= fim;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Gerenciar Ranges de Gavetas - {subBloco?.nome}
        <Typography variant="body2" color="textSecondary">
          Sub-bloco: {subBloco?.codigo_sub_bloco}
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        {/* Formulário para adicionar range */}
        <Box sx={{ mb: 3, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
          <Typography variant="h6" gutterBottom>
            Adicionar Novo Range
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              label="Número Início"
              type="number"
              value={formData.numero_inicio}
              onChange={(e) => setFormData({ ...formData, numero_inicio: e.target.value })}
              size="small"
              inputProps={{ min: 1 }}
            />
            
            <TextField
              label="Número Fim"
              type="number"
              value={formData.numero_fim}
              onChange={(e) => setFormData({ ...formData, numero_fim: e.target.value })}
              size="small"
              inputProps={{ min: 1 }}
            />
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddRange}
              disabled={!isFormValid() || loading}
            >
              Adicionar
            </Button>
          </Box>
        </Box>

        {/* Lista de ranges existentes */}
        <Typography variant="h6" gutterBottom>
          Ranges Existentes
          {loading && (
            <CircularProgress size={20} sx={{ ml: 2 }} />
          )}
        </Typography>

        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Range</TableCell>
                <TableCell>Total Gavetas</TableCell>
                <TableCell>Disponíveis</TableCell>
                <TableCell>Ocupadas</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(ranges) && ranges.length > 0 ? (
                ranges.map((range) => (
                  <TableRow key={range.id || `range-${range.numero_inicio}-${range.numero_fim}`}>
                    <TableCell>
                      <Chip
                        label={`${range.numero_inicio} - ${range.numero_fim}`}
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>{range.total_gavetas || 0}</TableCell>
                    <TableCell>
                      <Chip
                        label={range.gavetas_disponiveis || 0}
                        color="success"
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={range.gavetas_ocupadas || 0}
                        color={(range.gavetas_ocupadas || 0) > 0 ? "error" : "default"}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={range.ativo ? "Ativo" : "Inativo"}
                        color={range.ativo ? "success" : "default"}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Deletar range (só permite se não há gavetas ocupadas)">
                        <IconButton
                          color="error"
                          size="small"
                          onClick={() => handleDeleteRange(range)}
                          disabled={loading}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography color="textSecondary">
                      {loading ? 'Carregando ranges...' : 'Nenhum range definido'}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Fechar</Button>
      </DialogActions>
    </Dialog>
  );
};

export default RangeEditor;
