import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useNavigationLoading } from '../contexts/LoadingContext';

// Função para obter o título da página baseado na rota
const getPageTitle = (pathname) => {
  const routes = {
    '/dashboard': 'Dashboard',
    '/dashboard/book-sepultamentos': 'Book de Sepultamentos',
    '/dashboard/relatorios': 'Relatórios',
    '/dashboard/cadastros-produtos': 'Cadastros dos Produtos',
    '/dashboard/clientes': 'Gestão de Clientes',
    '/dashboard/usuarios': 'Gestão de Usuários',
    '/dashboard/logs': 'Logs de Auditoria',
  };

  // Verifica se é uma rota de detalhes
  if (pathname.includes('/book-sepultamentos/') && pathname !== '/dashboard/book-sepultamentos') {
    return 'Detalhes do Produto';
  }

  return routes[pathname] || 'Portal do Cliente';
};

// Função para obter as iniciais do usuário
const getUserInitials = (name) => {
  if (!name) return 'U';
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .substring(0, 2)
    .toUpperCase();
};
const Header = ({ onMenuClick }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const { user, logout } = useAuth();
  const { navigateWithLoading } = useNavigationLoading();
  const navigate = useNavigate();
  const location = useLocation();

  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    if (window.confirm('Tem certeza que deseja sair?')) {
      navigateWithLoading(() => {
        logout();
        navigate('/login');
      }, 500);
    }
    handleUserMenuClose();
  };

  const handleProfile = () => {
    // Implementar navegação para perfil
    handleUserMenuClose();
  };

  const pageTitle = getPageTitle(location.pathname);
  const userInitials = getUserInitials(user?.nome);

  return (
    <AppBar
      position="static"
      elevation={1}
      sx={{
        backgroundColor: 'background.paper',
        color: 'text.primary',
        borderBottom: '1px solid',
        borderColor: 'divider',
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between', px: 3 }}>
        {/* Menu Mobile */}
        <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
          <IconButton
            color="inherit"
            aria-label="abrir menu"
            edge="start"
            onClick={onMenuClick}
          >
            <MenuIcon />
          </IconButton>
        </Box>

        {/* Título da Página */}
        <Typography
          variant="h1"
          component="h1"
          sx={{
            fontSize: '1.5rem', // 24px
            fontWeight: 600,
            lineHeight: '2rem', // 32px
            color: 'text.primary',
            flexGrow: { xs: 1, md: 0 },
            textAlign: { xs: 'center', md: 'left' },
            ml: { xs: 0, md: 0 },
          }}
        >
          {pageTitle}
        </Typography>

        {/* Seção do Usuário */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Informações do Usuário - Desktop */}
          <Box sx={{ display: { xs: 'none', sm: 'block' }, textAlign: 'right' }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                color: 'text.primary',
                lineHeight: 1.2,
              }}
            >
              {user?.nome || 'Usuário'}
            </Typography>
            <Chip
              label={user?.tipo_usuario === 'admin' ? 'Administrador' : 'Cliente'}
              size="small"
              color={user?.tipo_usuario === 'admin' ? 'primary' : 'secondary'}
              sx={{
                fontSize: '0.75rem',
                height: '20px',
                mt: 0.5,
              }}
            />
          </Box>

          {/* Avatar do Usuário */}
          <IconButton
            onClick={handleUserMenuOpen}
            sx={{
              p: 0,
              '&:hover': {
                backgroundColor: 'action.hover',
              },
            }}
          >
            <Avatar
              sx={{
                width: 40,
                height: 40,
                bgcolor: 'primary.main',
                fontSize: '1rem',
                fontWeight: 600,
              }}
            >
              {userInitials}
            </Avatar>
          </IconButton>

          {/* Menu do Usuário */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleUserMenuClose}
            onClick={handleUserMenuClose}
            PaperProps={{
              elevation: 3,
              sx: {
                overflow: 'visible',
                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.1))',
                mt: 1.5,
                minWidth: 200,
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            {/* Informações do Usuário - Mobile */}
            <Box sx={{ display: { xs: 'block', sm: 'none' }, px: 2, py: 1 }}>
              <Typography variant="body2" fontWeight={500}>
                {user?.nome || 'Usuário'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user?.email}
              </Typography>
            </Box>
            <Divider sx={{ display: { xs: 'block', sm: 'none' } }} />

            <MenuItem onClick={handleProfile}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Meu Perfil</ListItemText>
            </MenuItem>

            <MenuItem onClick={handleProfile}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Configurações</ListItemText>
            </MenuItem>

            <Divider />

            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Sair</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
