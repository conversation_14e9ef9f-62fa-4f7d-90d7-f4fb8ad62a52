import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Modal from './Modal';
import { clienteService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const StyledFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  &:disabled {
    background: #f9fafb;
    color: #6b7280;
  }
`;

const Select = styled.select`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }
`;

const StyledButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const StyledButton = styled.button`
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &.primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }

  &.secondary {
    background: white;
    color: #374151;
    border-color: #d1d5db;

    &:hover {
      background: #f9fafb;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 4px;
`;

const LogoSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
`;

const LogoPreview = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const LogoImage = styled.img`
  width: 80px;
  height: 80px;
  object-fit: contain;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: white;
`;

const FileInput = styled.input`
  display: none;
`;

const FileInputLabel = styled.label`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.2s;

  &:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
  }
`;

const RemoveButton = styled.button`
  padding: 4px 8px;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  color: #dc2626;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #fecaca;
    border-color: #f87171;
  }
`;

const LogoInfo = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
`;

const estados = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA',
  'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN',
  'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
];

const ClienteModal = ({ isOpen, onClose, cliente = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_cliente: '',
    cnpj: '',
    nome_fantasia: '',
    razao_social: '',
    cep: '',
    logradouro: '',
    numero: '',
    complemento: '',
    bairro: '',
    cidade: '',
    estado: 'SP',
    ativo: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [removeLogo, setRemoveLogo] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (cliente) {
        setFormData({
          codigo_cliente: cliente.codigo_cliente || '',
          cnpj: cliente.cnpj || '',
          nome_fantasia: cliente.nome_fantasia || '',
          razao_social: cliente.razao_social || '',
          cep: cliente.cep || '',
          logradouro: cliente.logradouro || '',
          numero: cliente.numero || '',
          complemento: cliente.complemento || '',
          bairro: cliente.bairro || '',
          cidade: cliente.cidade || '',
          estado: cliente.estado || 'SP',
          ativo: cliente.ativo !== undefined ? cliente.ativo : true
        });

        // Configurar preview da logo existente
        if (cliente.logo_url) {
          setLogoPreview(cliente.logo_url);
        } else {
          setLogoPreview(null);
        }
      } else {
        setFormData({
          codigo_cliente: '',
          cnpj: '',
          nome_fantasia: '',
          razao_social: '',
          cep: '',
          logradouro: '',
          numero: '',
          complemento: '',
          bairro: '',
          cidade: '',
          estado: 'SP',
          ativo: true
        });
      }
      setError('');
      setLogoFile(null);
      setLogoPreview(cliente?.logo_url || null);
      setRemoveLogo(false);
    }
  }, [isOpen, cliente]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Formatação automática para campos específicos
    let formattedValue = value;
    
    if (name === 'cnpj') {
      // Formatação CNPJ: 18.384.274/0001-78
      formattedValue = value
        .replace(/\D/g, '')
        .replace(/^(\d{2})(\d)/, '$1.$2')
        .replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3')
        .replace(/\.(\d{3})(\d)/, '.$1/$2')
        .replace(/(\d{4})(\d)/, '$1-$2')
        .substring(0, 18);
    } else if (name === 'cep') {
      // Formatação CEP: 01310-100
      formattedValue = value
        .replace(/\D/g, '')
        .replace(/^(\d{5})(\d)/, '$1-$2')
        .substring(0, 9);
    } else if (name === 'codigo_cliente') {
      // Formatação código cliente: SAF_001
      formattedValue = value.toUpperCase();
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];

    if (!file) {
      setLogoFile(null);
      setLogoPreview(cliente?.logo_url || null);
      return;
    }

    // Validações do arquivo
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
    const maxSize = 2 * 1024 * 1024; // 2MB

    if (!allowedTypes.includes(file.type)) {
      setError('Apenas arquivos PNG e JPG são permitidos');
      e.target.value = '';
      return;
    }

    if (file.size > maxSize) {
      setError('Arquivo muito grande. Tamanho máximo: 2MB');
      e.target.value = '';
      return;
    }

    setLogoFile(file);
    setRemoveLogo(false);

    // Criar preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setLogoPreview(e.target.result);
    };
    reader.readAsDataURL(file);

    setError('');
  };

  const handleRemoveLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
    setRemoveLogo(true);

    // Limpar input file
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Criar FormData para incluir arquivo de logo
      const submitData = new FormData();

      // Adicionar dados do formulário
      Object.keys(formData).forEach(key => {
        submitData.append(key, formData[key]);
      });

      // Adicionar logo se fornecida
      if (logoFile) {
        submitData.append('logo', logoFile);
      }

      if (cliente) {
        // Atualizar cliente existente
        await clienteService.atualizarComLogo(cliente.codigo_cliente, submitData);

        // Se marcado para remover logo, fazer requisição separada
        if (removeLogo && !logoFile) {
          await clienteService.removerLogo(cliente.codigo_cliente);
        }

        alert('Cliente atualizado com sucesso!');
      } else {
        // Criar novo cliente
        await clienteService.criarComLogo(submitData);
        alert('Cliente criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar cliente:', error);
      setError(error.response?.data?.error || 'Erro ao salvar cliente');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={cliente ? 'Editar Cliente' : 'Novo Cliente'}
      maxWidth="800px"
    >
      <Form onSubmit={handleSubmit}>
        <FormRow>
          <StyledFormGroup>
            <Label>Código do Cliente *</Label>
            <Input
              type="text"
              name="codigo_cliente"
              value={formData.codigo_cliente}
              onChange={handleChange}
              required
              placeholder="Ex: SAF_001"
              disabled={!!cliente}
            />
          </StyledFormGroup>
          <StyledFormGroup>
            <Label>CNPJ *</Label>
            <Input
              type="text"
              name="cnpj"
              value={formData.cnpj}
              onChange={handleChange}
              required
              placeholder="18.384.274/0001-78"
            />
          </StyledFormGroup>
        </FormRow>

        <FormRow>
          <StyledFormGroup>
            <Label>Nome Fantasia *</Label>
            <Input
              type="text"
              name="nome_fantasia"
              value={formData.nome_fantasia}
              onChange={handleChange}
              required
              placeholder="Ex: Safra Cemitérios"
            />
          </StyledFormGroup>
          <StyledFormGroup>
            <Label>Razão Social *</Label>
            <Input
              type="text"
              name="razao_social"
              value={formData.razao_social}
              onChange={handleChange}
              required
              placeholder="Ex: Safra Cemitérios Ltda"
            />
          </StyledFormGroup>
        </FormRow>

        <FormRow>
          <StyledFormGroup>
            <Label>CEP</Label>
            <Input
              type="text"
              name="cep"
              value={formData.cep}
              onChange={handleChange}
              placeholder="01310-100"
            />
          </StyledFormGroup>
          <StyledFormGroup>
            <Label>Logradouro</Label>
            <Input
              type="text"
              name="logradouro"
              value={formData.logradouro}
              onChange={handleChange}
              placeholder="Ex: Av. Paulista"
            />
          </StyledFormGroup>
        </FormRow>

        <FormRow>
          <StyledFormGroup>
            <Label>Número</Label>
            <Input
              type="text"
              name="numero"
              value={formData.numero}
              onChange={handleChange}
              placeholder="Ex: 1000"
            />
          </StyledFormGroup>
          <StyledFormGroup>
            <Label>Complemento</Label>
            <Input
              type="text"
              name="complemento"
              value={formData.complemento}
              onChange={handleChange}
              placeholder="Ex: Sala 101"
            />
          </StyledFormGroup>
        </FormRow>

        <FormRow>
          <StyledFormGroup>
            <Label>Bairro</Label>
            <Input
              type="text"
              name="bairro"
              value={formData.bairro}
              onChange={handleChange}
              placeholder="Ex: Bela Vista"
            />
          </StyledFormGroup>
          <StyledFormGroup>
            <Label>Cidade</Label>
            <Input
              type="text"
              name="cidade"
              value={formData.cidade}
              onChange={handleChange}
              placeholder="Ex: São Paulo"
            />
          </StyledFormGroup>
        </FormRow>

        <StyledFormGroup>
          <Label>Estado</Label>
          <Select
            name="estado"
            value={formData.estado}
            onChange={handleChange}
          >
            {estados.map(estado => (
              <option key={estado} value={estado}>{estado}</option>
            ))}
          </Select>
        </StyledFormGroup>

        {/* Seção de Upload de Logo */}
        <StyledFormGroup>
          <Label>Logo do Cliente</Label>
          <LogoSection>
            {logoPreview && (
              <LogoPreview>
                <LogoImage src={logoPreview} alt="Logo do cliente" />
                <div>
                  <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                    Logo atual
                  </div>
                  <RemoveButton type="button" onClick={handleRemoveLogo}>
                    Remover
                  </RemoveButton>
                </div>
              </LogoPreview>
            )}

            <div>
              <FileInput
                id="logo-upload"
                type="file"
                accept=".png,.jpg,.jpeg"
                onChange={handleLogoChange}
              />
              <FileInputLabel htmlFor="logo-upload">
                📷 {logoPreview ? 'Alterar Logo' : 'Selecionar Logo'}
              </FileInputLabel>
              <LogoInfo>
                Formatos aceitos: PNG, JPG • Tamanho máximo: 2MB
              </LogoInfo>
            </div>
          </LogoSection>
        </StyledFormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButtonGroup>
          <StyledButton type="button" className="secondary" onClick={onClose}>
            Cancelar
          </StyledButton>
          <StyledButton type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (cliente ? 'Atualizar' : 'Criar')}
          </StyledButton>
        </StyledButtonGroup>
      </Form>
    </Modal>
  );
};

export default ClienteModal;
