import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Alert } from '@mui/material';

const TesteLoginDebug = () => {
  const [email, setEmail] = useState('mauricio<PERSON><PERSON><PERSON>@evolutionbr.tech');
  const [senha, setSenha] = useState('adminnbr5410!');
  const [resultado, setResultado] = useState('');
  const [loading, setLoading] = useState(false);

  const testarLogin = async () => {
    setLoading(true);
    setResultado('Testando...');
    
    try {
      console.log('🔍 Iniciando teste de login...');
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, senha }),
      });
      
      console.log('📡 Response status:', response.status);
      
      const data = await response.json();
      console.log('📦 Response data:', data);
      
      if (response.ok) {
        setResultado(`✅ Login bem-sucedido! Token: ${data.token.substring(0, 50)}...`);
        
        // Testar redirecionamento
        console.log('🔄 Testando redirecionamento...');
        window.location.href = '/dashboard';
      } else {
        setResultado(`❌ Erro no login: ${data.error || 'Erro desconhecido'}`);
      }
    } catch (error) {
      console.error('💥 Erro na requisição:', error);
      setResultado(`💥 Erro na requisição: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🔧 Teste de Login - Debug
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          sx={{ mb: 2 }}
        />
        <TextField
          fullWidth
          label="Senha"
          type="password"
          value={senha}
          onChange={(e) => setSenha(e.target.value)}
          sx={{ mb: 2 }}
        />
        <Button
          variant="contained"
          onClick={testarLogin}
          disabled={loading}
          fullWidth
        >
          {loading ? 'Testando...' : 'Testar Login'}
        </Button>
      </Box>
      
      {resultado && (
        <Alert severity={resultado.includes('✅') ? 'success' : 'error'}>
          {resultado}
        </Alert>
      )}
      
      <Box sx={{ mt: 3 }}>
        <Typography variant="h6">Informações do Sistema:</Typography>
        <Typography variant="body2">
          URL atual: {window.location.href}
        </Typography>
        <Typography variant="body2">
          User Agent: {navigator.userAgent}
        </Typography>
        <Typography variant="body2">
          Local Storage Token: {localStorage.getItem('token') ? 'Presente' : 'Ausente'}
        </Typography>
      </Box>
    </Box>
  );
};

export default TesteLoginDebug;
