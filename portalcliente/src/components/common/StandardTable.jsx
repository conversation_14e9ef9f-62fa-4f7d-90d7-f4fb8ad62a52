import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  TablePagination,
  CircularProgress,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Container de tabela padronizado
const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '8px',
  border: `1px solid ${theme.palette.grey[200]}`,
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
  backgroundColor: theme.palette.background.paper,
}));

// Cabeçalho de tabela padronizado
const StyledTableHead = styled(TableHead)(({ theme }) => ({
  backgroundColor: theme.palette.grey[50],
  '& .MuiTableCell-head': {
    backgroundColor: theme.palette.grey[50],
    fontWeight: 600,
    color: theme.palette.text.primary,
    fontSize: '0.875rem',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
    borderBottom: `2px solid ${theme.palette.grey[200]}`,
  },
}));

// Linha de tabela padronizada
const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(even)': {
    backgroundColor: theme.palette.grey[50],
  },
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    cursor: 'pointer',
  },
  '&:last-child td, &:last-child th': {
    border: 0,
  },
}));

// Célula de tabela padronizada
const StyledTableCell = styled(TableCell)(({ theme }) => ({
  fontSize: '0.875rem',
  padding: '16px',
  borderBottom: `1px solid ${theme.palette.grey[200]}`,
}));

// Componente de ações da tabela
export const TableActions = ({ actions = [], size = 'small' }) => {
  return (
    <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
      {actions.map((action, index) => (
        <Tooltip key={index} title={action.tooltip || action.label}>
          <IconButton
            size={size}
            onClick={(e) => {
              e.stopPropagation(); // Impede que o clique propague para a linha da tabela
              action.onClick(e);
            }}
            disabled={action.disabled}
            color={action.color || 'default'}
            sx={{
              '&:hover': {
                backgroundColor: action.color ? `${action.color}.light` : 'action.hover',
              },
            }}
          >
            {action.icon}
          </IconButton>
        </Tooltip>
      ))}
    </Box>
  );
};

// Componente de status com chip
export const StatusChip = ({ status, color = 'default', variant = 'filled' }) => {
  return (
    <Chip
      label={status}
      color={color}
      variant={variant}
      size="small"
      sx={{
        fontWeight: 500,
        fontSize: '0.75rem',
      }}
    />
  );
};

// Loading state para tabela
const TableLoading = ({ columns }) => {
  return (
    <StyledTableRow>
      <StyledTableCell colSpan={columns} align="center" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
          <CircularProgress size={24} />
          <Typography variant="body2" color="text.secondary">
            Carregando dados...
          </Typography>
        </Box>
      </StyledTableCell>
    </StyledTableRow>
  );
};

// Empty state para tabela
const TableEmpty = ({ columns, message = 'Nenhum dado encontrado' }) => {
  return (
    <StyledTableRow>
      <StyledTableCell colSpan={columns} align="center" sx={{ py: 4 }}>
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      </StyledTableCell>
    </StyledTableRow>
  );
};

// Componente principal da tabela padronizada
const StandardTable = ({
  columns = [],
  data = [],
  loading = false,
  emptyMessage = 'Nenhum dado encontrado',
  onRowClick,
  pagination,
  title,
  subtitle,
  headerAction,
  sx = {},
  ...props
}) => {
  return (
    <Box sx={{ width: '100%', ...sx }}>
      {/* Cabeçalho da tabela */}
      {(title || subtitle || headerAction) && (
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            {title && (
              <Typography
                variant="h5"
                sx={{
                  fontSize: '1.25rem', // 20px
                  fontWeight: 600,
                  lineHeight: '1.75rem', // 28px
                  color: 'text.primary',
                  mb: subtitle ? 0.5 : 0,
                }}
              >
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography
                variant="body2"
                sx={{
                  fontSize: '0.875rem', // 14px
                  color: 'text.secondary',
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
          {headerAction && (
            <Box sx={{ ml: 2 }}>
              {headerAction}
            </Box>
          )}
        </Box>
      )}

      {/* Tabela */}
      <StyledTableContainer component={Paper} {...props}>
        <Table>
          <StyledTableHead>
            <TableRow>
              {columns.map((column) => (
                <StyledTableCell
                  key={column.id || column.field}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                >
                  {column.label || column.headerName}
                </StyledTableCell>
              ))}
            </TableRow>
          </StyledTableHead>
          <TableBody>
            {loading ? (
              <TableLoading columns={columns.length} />
            ) : data.length === 0 ? (
              <TableEmpty columns={columns.length} message={emptyMessage} />
            ) : (
              data.map((row, index) => (
                <StyledTableRow
                  key={row.id || index}
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                  sx={{
                    cursor: onRowClick ? 'pointer' : 'default',
                  }}
                >
                  {columns.map((column) => (
                    <StyledTableCell
                      key={column.id || column.field}
                      align={column.align || 'left'}
                    >
                      {column.render
                        ? column.render(row[column.field || column.id], row)
                        : row[column.field || column.id]}
                    </StyledTableCell>
                  ))}
                </StyledTableRow>
              ))
            )}
          </TableBody>
        </Table>
      </StyledTableContainer>

      {/* Paginação */}
      {pagination && (
        <Box sx={{ mt: 2 }}>
          <TablePagination
            component="div"
            {...pagination}
            labelRowsPerPage="Linhas por página:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} de ${count !== -1 ? count : `mais de ${to}`}`
            }
          />
        </Box>
      )}
    </Box>
  );
};

export default StandardTable;
