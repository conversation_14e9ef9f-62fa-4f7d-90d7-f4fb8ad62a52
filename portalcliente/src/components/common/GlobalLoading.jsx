import React from 'react';
import {
  Backdrop,
  CircularProgress,
  Box,
  Typography,
  Fade,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Container principal do loading
const LoadingContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  gap: theme.spacing(3),
  color: theme.palette.primary.main,
}));

// Logo animado (opcional)
const AnimatedLogo = styled(Box)(({ theme }) => ({
  width: '80px',
  height: '80px',
  borderRadius: '50%',
  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  animation: 'pulse 2s ease-in-out infinite',
  '@keyframes pulse': {
    '0%': {
      transform: 'scale(1)',
      opacity: 1,
    },
    '50%': {
      transform: 'scale(1.1)',
      opacity: 0.8,
    },
    '100%': {
      transform: 'scale(1)',
      opacity: 1,
    },
  },
}));

// Texto de loading animado
const LoadingText = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
  textAlign: 'center',
  animation: 'fadeInOut 1.5s ease-in-out infinite',
  '@keyframes fadeInOut': {
    '0%': { opacity: 0.5 },
    '50%': { opacity: 1 },
    '100%': { opacity: 0.5 },
  },
}));

// Componente principal de loading global
const GlobalLoading = ({
  open = false,
  message = 'Carregando...',
  showLogo = true,
  variant = 'default', // 'default', 'minimal', 'branded'
}) => {
  const renderContent = () => {
    switch (variant) {
      case 'minimal':
        return (
          <LoadingContainer>
            <CircularProgress size={60} thickness={4} />
            <LoadingText variant="h6">{message}</LoadingText>
          </LoadingContainer>
        );
      
      case 'branded':
        return (
          <LoadingContainer>
            {showLogo && (
              <AnimatedLogo>
                <Typography
                  variant="h4"
                  sx={{
                    color: 'white',
                    fontWeight: 700,
                    textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  }}
                >
                  E
                </Typography>
              </AnimatedLogo>
            )}
            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
              <CircularProgress
                size={80}
                thickness={4}
                sx={{
                  color: 'primary.main',
                  animationDuration: '1.5s',
                }}
              />
              <Box
                sx={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <CircularProgress
                  size={60}
                  thickness={6}
                  variant="determinate"
                  value={25}
                  sx={{
                    color: 'secondary.main',
                    animationDuration: '2s',
                    transform: 'rotate(180deg)',
                  }}
                />
              </Box>
            </Box>
            <LoadingText>{message}</LoadingText>
            <Typography
              variant="caption"
              sx={{
                color: 'text.secondary',
                fontSize: '0.875rem',
                opacity: 0.7,
              }}
            >
              Portal Evolution
            </Typography>
          </LoadingContainer>
        );
      
      default:
        return (
          <LoadingContainer>
            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
              <CircularProgress
                size={60}
                thickness={4}
                sx={{
                  color: 'primary.main',
                }}
              />
              <Box
                sx={{
                  top: 0,
                  left: 0,
                  bottom: 0,
                  right: 0,
                  position: 'absolute',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <CircularProgress
                  size={40}
                  thickness={6}
                  variant="determinate"
                  value={75}
                  sx={{
                    color: 'secondary.main',
                    transform: 'rotate(-90deg)',
                  }}
                />
              </Box>
            </Box>
            <LoadingText>{message}</LoadingText>
          </LoadingContainer>
        );
    }
  };

  return (
    <Backdrop
      sx={{
        color: '#fff',
        zIndex: (theme) => theme.zIndex.drawer + 1000, // Muito alto para sobrepor tudo
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(4px)',
      }}
      open={open}
    >
      <Fade in={open} timeout={300}>
        <Box>
          {renderContent()}
        </Box>
      </Fade>
    </Backdrop>
  );
};

export default GlobalLoading;
