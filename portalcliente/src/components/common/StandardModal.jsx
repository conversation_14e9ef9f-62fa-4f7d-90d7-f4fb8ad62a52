import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Typography,
  Box,
  Slide,
} from '@mui/material';
import {
  Close as CloseIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Transição personalizada para o modal
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

// Título do modal padronizado
const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  padding: '24px 24px 16px 24px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTypography-root': {
    fontSize: '1.5rem', // 24px
    fontWeight: 600,
    lineHeight: '2rem', // 32px
    color: theme.palette.text.primary,
  },
}));

// Conteúdo do modal padronizado
const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  padding: '24px',
  '&.MuiDialogContent-dividers': {
    borderTop: 'none',
    borderBottom: 'none',
  },
}));

// Ações do modal padronizadas
const StyledDialogActions = styled(DialogActions)(({ theme }) => ({
  padding: '16px 24px 24px 24px',
  borderTop: `1px solid ${theme.palette.divider}`,
  gap: '12px',
  justifyContent: 'flex-end',
}));

// Componente principal do modal padronizado
const StandardModal = ({
  open = false,
  onClose,
  title,
  subtitle,
  children,
  actions,
  maxWidth = 'md',
  fullWidth = true,
  fullScreen = false,
  disableEscapeKeyDown = false,
  disableBackdropClick = false,
  showCloseButton = true,
  sx = {},
  ...props
}) => {
  const handleClose = (event, reason) => {
    if (disableBackdropClick && reason === 'backdropClick') {
      return;
    }
    if (disableEscapeKeyDown && reason === 'escapeKeyDown') {
      return;
    }
    if (onClose) {
      onClose(event, reason);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      fullScreen={fullScreen}
      TransitionComponent={Transition}
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '12px',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
          ...sx,
        },
      }}
      {...props}
    >
      {/* Título do Modal */}
      {(title || showCloseButton) && (
        <StyledDialogTitle>
          <Box>
            {title && (
              <Typography variant="h4" component="h2">
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  mt: 0.5,
                  fontSize: '0.875rem',
                  lineHeight: '1.25rem',
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
          {showCloseButton && (
            <IconButton
              aria-label="fechar"
              onClick={handleClose}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          )}
        </StyledDialogTitle>
      )}

      {/* Conteúdo do Modal */}
      <StyledDialogContent dividers={Boolean(title || actions)}>
        {children}
      </StyledDialogContent>

      {/* Ações do Modal */}
      {actions && (
        <StyledDialogActions>
          {actions}
        </StyledDialogActions>
      )}
    </Dialog>
  );
};

// Componente de confirmação padronizado
export const ConfirmationModal = ({
  open = false,
  onClose,
  onConfirm,
  title = 'Confirmar Ação',
  message = 'Tem certeza que deseja continuar?',
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  confirmColor = 'primary',
  severity = 'warning', // 'warning', 'error', 'info', 'success'
  loading = false,
  ...props
}) => {
  const getSeverityIcon = () => {
    switch (severity) {
      case 'error':
        return '⚠️';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'success':
        return '✅';
      default:
        return '❓';
    }
  };

  return (
    <StandardModal
      open={open}
      onClose={onClose}
      title={title}
      maxWidth="sm"
      actions={
        <>
          <StandardButton
            variant="outlined"
            onClick={onClose}
            disabled={loading}
          >
            {cancelText}
          </StandardButton>
          <StandardButton
            variant="contained"
            color={confirmColor}
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? 'Processando...' : confirmText}
          </StandardButton>
        </>
      }
      {...props}
    >
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
        <Typography variant="h2" sx={{ fontSize: '2rem', lineHeight: 1 }}>
          {getSeverityIcon()}
        </Typography>
        <Typography variant="body1" sx={{ flex: 1, pt: 0.5 }}>
          {message}
        </Typography>
      </Box>
    </StandardModal>
  );
};

export default StandardModal;
