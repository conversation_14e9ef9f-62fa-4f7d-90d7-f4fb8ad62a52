import React from 'react';
import { Container, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

// Container principal padronizado
const StyledContainer = styled(Container)(({ theme }) => ({
  paddingTop: '24px', // 3 * 8px
  paddingBottom: '24px',
  paddingLeft: '32px', // Desktop
  paddingRight: '32px',

  [theme.breakpoints.down('md')]: {
    paddingLeft: '16px', // Mobile
    paddingRight: '16px',
  },
}));

// Cabeçalho da página padronizado
const PageHeader = styled(Box)(({ theme }) => ({
  marginBottom: '32px', // 4 * 8px
  paddingBottom: '16px', // 2 * 8px
  borderBottom: `1px solid ${theme.palette.grey[200]}`,
}));

// Título da página seguindo especificação (32px)
const PageTitle = styled(Typography)(({ theme }) => ({
  fontSize: '2rem', // 32px - conforme especificação
  fontWeight: 700,
  lineHeight: '2.5rem', // 40px
  color: theme.palette.text.primary,
  marginBottom: '8px',

  [theme.breakpoints.down('md')]: {
    fontSize: '1.5rem', // 24px no mobile
    lineHeight: '2rem', // 32px
  },
}));

// Subtítulo padronizado (16px)
const PageSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '1rem', // 16px - texto base
  fontWeight: 400,
  lineHeight: '1.5rem', // 24px
  color: theme.palette.text.secondary,
}));

// Seção de conteúdo padronizada
const StyledContentSection = styled(Box)(({ theme }) => ({
  marginBottom: '24px', // 3 * 8px - espaçamento padrão entre seções

  '&:last-child': {
    marginBottom: 0,
  },
}));

const StandardContainer = ({
  children,
  title,
  subtitle,
  maxWidth = 'xl',
  disableGutters = false,
  headerAction,
  sx = {},
  ...props
}) => {
  return (
    <StyledContainer
      maxWidth={maxWidth}
      disableGutters={disableGutters}
      sx={{
        backgroundColor: 'background.default',
        minHeight: '100vh',
        ...sx,
      }}
      {...props}
    >
      {(title || subtitle || headerAction) && (
        <PageHeader>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              {title && (
                <PageTitle variant="h1">
                  {title}
                </PageTitle>
              )}
              {subtitle && (
                <PageSubtitle>
                  {subtitle}
                </PageSubtitle>
              )}
            </Box>
            {headerAction && (
              <Box sx={{ ml: 2 }}>
                {headerAction}
              </Box>
            )}
          </Box>
        </PageHeader>
      )}
      
      <Box>
        {children}
      </Box>
    </StyledContainer>
  );
};

// Componente para seções de conteúdo padronizado
export const ContentSection = ({ children, title, subtitle, sx = {}, ...props }) => {
  return (
    <StyledContentSection sx={sx} {...props}>
      {title && (
        <Typography
          variant="h3"
          sx={{
            fontSize: '1.25rem', // 20px - seções conforme especificação
            fontWeight: 600,
            lineHeight: '1.75rem', // 28px
            color: 'text.primary',
            mb: subtitle ? 1 : 2,
          }}
        >
          {title}
        </Typography>
      )}
      {subtitle && (
        <Typography
          variant="body1"
          sx={{
            fontSize: '1rem', // 16px
            fontWeight: 400,
            lineHeight: '1.5rem', // 24px
            color: 'text.secondary',
            mb: 2,
          }}
        >
          {subtitle}
        </Typography>
      )}
      {children}
    </StyledContentSection>
  );
};

export default StandardContainer;
