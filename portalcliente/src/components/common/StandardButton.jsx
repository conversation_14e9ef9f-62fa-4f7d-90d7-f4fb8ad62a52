import React from 'react';
import { Button } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme, variant, color }) => ({
  height: '48px',
  borderRadius: '8px',
  paddingLeft: '24px',
  paddingRight: '24px',
  fontSize: '16px',
  fontWeight: 600,
  textTransform: 'uppercase',
  transition: 'all 0.3s ease',
  minWidth: '120px',
  
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[4],
  },
  
  '&:active': {
    transform: 'translateY(0px)',
  },
  
  '&:disabled': {
    opacity: 0.6,
    cursor: 'not-allowed',
    transform: 'none',
  },
  
  // Variações específicas
  ...(variant === 'contained' && {
    boxShadow: theme.shadows[2],
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[6],
    },
  }),
  
  ...(variant === 'outlined' && {
    borderWidth: '2px',
    '&:hover': {
      borderWidth: '2px',
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[3],
    },
  }),
  
  // Tamanhos específicos
  '&.size-small': {
    height: '36px',
    paddingLeft: '16px',
    paddingRight: '16px',
    fontSize: '14px',
    minWidth: '80px',
  },
  
  '&.size-large': {
    height: '56px',
    paddingLeft: '32px',
    paddingRight: '32px',
    fontSize: '18px',
    minWidth: '160px',
  },
}));

const StandardButton = ({
  children,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  startIcon,
  endIcon,
  onClick,
  type = 'button',
  sx = {},
  ...props
}) => {
  const sizeClass = size !== 'medium' ? `size-${size}` : '';
  
  return (
    <StyledButton
      variant={variant}
      color={color}
      fullWidth={fullWidth}
      disabled={disabled}
      startIcon={startIcon}
      endIcon={endIcon}
      onClick={onClick}
      type={type}
      className={sizeClass}
      sx={sx}
      {...props}
    >
      {children}
    </StyledButton>
  );
};

export default StandardButton;
