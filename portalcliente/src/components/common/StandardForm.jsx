import React from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  Typography,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Container de formulário padronizado
const FormContainer = styled(Box)(({ theme }) => ({
  '& .MuiFormControl-root': {
    marginBottom: '16px', // Espaçamento padrão entre campos
  },
  '& .MuiTextField-root': {
    marginBottom: '16px',
  },
}));

// Campo de texto padronizado
export const StandardTextField = ({
  label,
  required = false,
  error = false,
  helperText = '',
  fullWidth = true,
  size = 'medium',
  ...props
}) => {
  return (
    <TextField
      label={label}
      required={required}
      error={error}
      helperText={helperText}
      fullWidth={fullWidth}
      size={size}
      variant="outlined"
      sx={{
        '& .MuiOutlinedInput-root': {
          minHeight: '48px', // Altura padrão
        },
        '& .MuiInputLabel-root': {
          fontSize: '1rem',
          fontWeight: 500,
        },
      }}
      {...props}
    />
  );
};

// Select/Dropdown padronizado
export const StandardSelect = ({
  label,
  value,
  onChange,
  options = [],
  required = false,
  error = false,
  helperText = '',
  fullWidth = true,
  placeholder = 'Selecione...',
  ...props
}) => {
  return (
    <FormControl
      fullWidth={fullWidth}
      required={required}
      error={error}
      variant="outlined"
      sx={{
        '& .MuiOutlinedInput-root': {
          minHeight: '48px', // Altura padrão
        },
        '& .MuiInputLabel-root': {
          fontSize: '1rem',
          fontWeight: 500,
        },
      }}
      {...props}
    >
      <InputLabel>{label}</InputLabel>
      <Select
        value={value}
        onChange={onChange}
        label={label}
        sx={{
          '& .MuiSelect-select': {
            padding: '12px 14px',
            fontSize: '1rem',
          },
        }}
      >
        {placeholder && (
          <MenuItem value="" disabled>
            <em>{placeholder}</em>
          </MenuItem>
        )}
        {options.map((option) => (
          <MenuItem
            key={option.value || option.id}
            value={option.value || option.id}
          >
            {option.label || option.name || option.denominacao}
          </MenuItem>
        ))}
      </Select>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
};

// Grid de formulário padronizado
export const FormGrid = ({ children, spacing = 2, ...props }) => {
  return (
    <Grid container spacing={spacing} {...props}>
      {children}
    </Grid>
  );
};

// Item de grid de formulário
export const FormGridItem = ({ children, xs = 12, sm, md, lg, xl, ...props }) => {
  return (
    <Grid item xs={xs} sm={sm} md={md} lg={lg} xl={xl} {...props}>
      {children}
    </Grid>
  );
};

// Seção de formulário com título
export const FormSection = ({ title, subtitle, children, sx = {}, ...props }) => {
  return (
    <Box sx={{ mb: 3, ...sx }} {...props}>
      {title && (
        <Typography
          variant="h5"
          sx={{
            fontSize: '1.125rem', // 18px
            fontWeight: 600,
            lineHeight: '1.5rem', // 24px
            color: 'text.primary',
            mb: subtitle ? 0.5 : 2,
          }}
        >
          {title}
        </Typography>
      )}
      {subtitle && (
        <Typography
          variant="body2"
          sx={{
            fontSize: '0.875rem', // 14px
            fontWeight: 400,
            lineHeight: '1.25rem', // 20px
            color: 'text.secondary',
            mb: 2,
          }}
        >
          {subtitle}
        </Typography>
      )}
      {children}
    </Box>
  );
};

// Container principal de formulário
const StandardForm = ({
  children,
  title,
  subtitle,
  onSubmit,
  sx = {},
  ...props
}) => {
  return (
    <FormContainer
      component="form"
      onSubmit={onSubmit}
      sx={{
        backgroundColor: 'background.paper',
        borderRadius: '12px',
        padding: '24px',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)',
        border: '1px solid',
        borderColor: 'grey.200',
        ...sx,
      }}
      {...props}
    >
      {(title || subtitle) && (
        <Box sx={{ mb: 3 }}>
          {title && (
            <Typography
              variant="h4"
              sx={{
                fontSize: '1.5rem', // 24px
                fontWeight: 600,
                lineHeight: '2rem', // 32px
                color: 'text.primary',
                mb: subtitle ? 1 : 0,
              }}
            >
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography
              variant="body1"
              sx={{
                fontSize: '1rem', // 16px
                fontWeight: 400,
                lineHeight: '1.5rem', // 24px
                color: 'text.secondary',
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      )}
      {children}
    </FormContainer>
  );
};

export default StandardForm;
