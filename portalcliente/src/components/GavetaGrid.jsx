import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { gavetaService } from '../services/api';

const GridContainer = styled.div`
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
`;

const GridHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const GridTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
`;

const GridControls = styled.div`
  display: flex;
  gap: 12px;
  align-items: center;
`;

const Select = styled.select`
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  max-width: 600px;
  margin: 0 auto;
`;

const Gaveta = styled.div`
  aspect-ratio: 1;
  border: 2px solid ${props => props.ocupada ? '#dc2626' : '#059669'};
  border-radius: 8px;
  background: ${props => props.ocupada ? '#fef2f2' : '#f0fdf4'};
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 80px;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
  }

  ${props => props.alturaEspecial > 1 && `
    grid-row: span 2;
    aspect-ratio: 1/1.5;
  `}
`;

const GavetaNumero = styled.div`
  font-weight: bold;
  font-size: 0.875rem;
  color: ${props => props.ocupada ? '#dc2626' : '#059669'};
  margin-bottom: 4px;
`;

const GavetaStatus = styled.div`
  font-size: 0.75rem;
  color: ${props => props.ocupada ? '#dc2626' : '#059669'};
  text-align: center;
`;

const SepultadoNome = styled.div`
  font-size: 0.625rem;
  color: #6b7280;
  text-align: center;
  margin-top: 2px;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const Tooltip = styled.div`
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  background: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  opacity: ${props => props.show ? 1 : 0};
  visibility: ${props => props.show ? 'visible' : 'hidden'};
  transition: all 0.2s ease;
  pointer-events: none;

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #1f2937;
  }
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  
  &::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #059669;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
`;

const Legend = styled.div`
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
`;

const LegendColor = styled.div`
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 2px solid ${props => props.color};
  background: ${props => props.bg};
`;

const GavetaGrid = ({ subBlocoId, onGavetaClick }) => {
  const [gavetas, setGavetas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hoveredGaveta, setHoveredGaveta] = useState(null);

  useEffect(() => {
    if (subBlocoId) {
      loadGavetas();
    }
  }, [subBlocoId]);

  const loadGavetas = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await gavetaService.listar({ sub_bloco_id: subBlocoId });
      setGavetas(response.data);
    } catch (error) {
      console.error('Erro ao carregar gavetas:', error);
      setError('Erro ao carregar gavetas');
    } finally {
      setLoading(false);
    }
  };

  const handleGavetaClick = (gaveta) => {
    if (onGavetaClick) {
      onGavetaClick(gaveta);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (loading) {
    return (
      <GridContainer>
        <LoadingSpinner />
      </GridContainer>
    );
  }

  if (error) {
    return (
      <GridContainer>
        <EmptyState>
          <div style={{ fontSize: '2rem', marginBottom: '16px' }}>❌</div>
          <div>{error}</div>
        </EmptyState>
      </GridContainer>
    );
  }

  if (gavetas.length === 0) {
    return (
      <GridContainer>
        <EmptyState>
          <div style={{ fontSize: '2rem', marginBottom: '16px' }}>🗃️</div>
          <div>Nenhuma gaveta encontrada</div>
        </EmptyState>
      </GridContainer>
    );
  }

  // Organizar gavetas em grid baseado na posição
  const maxX = Math.max(...gavetas.map(g => g.posicao_x));
  const maxY = Math.max(...gavetas.map(g => g.posicao_y));

  return (
    <GridContainer>
      <GridHeader>
        <GridTitle>Gavetas - Sub-bloco</GridTitle>
        <GridControls>
          <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            {gavetas.filter(g => !g.disponivel).length} ocupadas / {gavetas.length} total
          </span>
        </GridControls>
      </GridHeader>

      <Grid style={{ gridTemplateColumns: `repeat(${maxX}, 1fr)` }}>
        {gavetas
          .sort((a, b) => a.posicao_y - b.posicao_y || a.posicao_x - b.posicao_x)
          .map((gaveta) => (
            <Gaveta
              key={gaveta.id}
              ocupada={!gaveta.disponivel}
              alturaEspecial={gaveta.altura_especial}
              onClick={() => handleGavetaClick(gaveta)}
              onMouseEnter={() => setHoveredGaveta(gaveta.id)}
              onMouseLeave={() => setHoveredGaveta(null)}
            >
              <GavetaNumero ocupada={!gaveta.disponivel}>
                {gaveta.numero_gaveta}
              </GavetaNumero>
              
              <GavetaStatus ocupada={!gaveta.disponivel}>
                {gaveta.disponivel ? 'Livre' : 'Ocupada'}
              </GavetaStatus>

              {gaveta.nome_sepultado && (
                <SepultadoNome>
                  {gaveta.nome_sepultado}
                </SepultadoNome>
              )}

              {hoveredGaveta === gaveta.id && gaveta.nome_sepultado && (
                <Tooltip show={true}>
                  <div><strong>{gaveta.nome_sepultado}</strong></div>
                  <div>Sepultamento: {formatDate(gaveta.data_sepultamento)}</div>
                  {gaveta.data_exumacao && (
                    <div>Exumação: {formatDate(gaveta.data_exumacao)}</div>
                  )}
                  <div>Posição: {gaveta.posicao}</div>
                </Tooltip>
              )}
            </Gaveta>
          ))}
      </Grid>

      <Legend>
        <LegendItem>
          <LegendColor color="#059669" bg="#f0fdf4" />
          <span>Gaveta Disponível</span>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#dc2626" bg="#fef2f2" />
          <span>Gaveta Ocupada</span>
        </LegendItem>
      </Legend>
    </GridContainer>
  );
};

export default GavetaGrid;
