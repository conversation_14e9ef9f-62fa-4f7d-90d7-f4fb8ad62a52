import React, { useState } from 'react';
import { Box, Button, TextField, Typography, Alert, Paper } from '@mui/material';

const TesteSenhas = () => {
  const [resultados, setResultados] = useState([]);
  const [testando, setTestando] = useState(false);
  const [emailTeste, setEmailTeste] = useState('<EMAIL>');
  const [senhaOriginal, setSenhaOriginal] = useState('senha123');
  const [senha<PERSON><PERSON>, setSenhaNova] = useState('nova456');

  const adicionarResultado = (teste, sucesso, detalhes) => {
    const resultado = {
      teste,
      sucesso,
      detalhes,
      timestamp: new Date().toLocaleTimeString()
    };
    setResultados(prev => [...prev, resultado]);
    console.log(`${sucesso ? '✅' : '❌'} ${teste}:`, detalhes);
  };

  const executarTesteSenhas = async () => {
    setTestando(true);
    setResultados([]);

    try {
      // Verificar se está autenticado
      const token = localStorage.getItem('token');
      if (!token) {
        adicionarResultado('Erro de autenticação', false, 'Você precisa estar logado como admin para executar este teste');
        return;
      }

      adicionarResultado('Iniciando teste de senhas', true, 'Começando teste completo');

      // Passo 1: Criar usuário de teste
      const dadosUsuario = {
        nome: 'Usuário Teste Senha',
        email: emailTeste,
        senha: senhaOriginal,
        tipo_usuario: 'admin',
        ativo: true
      };

      let usuarioId;
      try {
        console.log('📤 Criando usuário via fetch...');
        const responseCreate = await fetch('http://localhost:3001/api/usuarios', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(dadosUsuario)
        });

        if (responseCreate.ok) {
          const data = await responseCreate.json();
          usuarioId = data.id;
          adicionarResultado('1. Criar usuário', true, `Usuário criado com ID: ${usuarioId}`);
        } else {
          const errorData = await responseCreate.json();
          adicionarResultado('1. Criar usuário', false, errorData.error || 'Erro ao criar usuário');
          return;
        }
      } catch (error) {
        adicionarResultado('1. Criar usuário', false, error.message);
        return;
      }

      // Passo 2: Testar login com senha original (apenas verificar credenciais)
      try {
        console.log('🔐 Testando login com senha original...');
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: emailTeste, senha: senhaOriginal }),
        });

        if (response.ok) {
          adicionarResultado('2. Login senha original', true, 'Login funcionou com senha original');
        } else {
          adicionarResultado('2. Login senha original', false, 'Falha no login com senha original');
        }
      } catch (error) {
        adicionarResultado('2. Login senha original', false, 'Falha no login com senha original');
      }

      // Passo 3: Alterar senha
      try {
        const dadosAtualizacao = {
          nome: 'Usuário Teste Senha',
          email: emailTeste,
          senha: senhaNova,
          tipo_usuario: 'admin',
          ativo: true
        };

        console.log('🔄 Atualizando senha do usuário:', usuarioId);
        console.log('📤 Dados de atualização:', {
          ...dadosAtualizacao,
          senha: `*** (${dadosAtualizacao.senha.length} caracteres)`
        });

        const responseUpdate = await fetch(`http://localhost:3001/api/usuarios/${usuarioId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(dadosAtualizacao)
        });

        if (responseUpdate.ok) {
          adicionarResultado('3. Alterar senha', true, `Senha alterada de "${senhaOriginal}" para "${senhaNova}"`);
        } else {
          const errorData = await responseUpdate.json();
          adicionarResultado('3. Alterar senha', false, errorData.error || 'Erro ao alterar senha');
        }
      } catch (error) {
        adicionarResultado('3. Alterar senha', false, error.message);
      }

      // Passo 4: Testar login com senha antiga (deve falhar)
      try {
        console.log('🔐 Testando login com senha antiga...');
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: emailTeste, senha: senhaOriginal }),
        });

        if (response.ok) {
          adicionarResultado('4. Login senha antiga', false, 'ERRO: Login funcionou com senha antiga (deveria falhar)');
        } else {
          adicionarResultado('4. Login senha antiga', true, 'Correto: Login falhou com senha antiga');
        }
      } catch (error) {
        adicionarResultado('4. Login senha antiga', true, 'Correto: Login falhou com senha antiga');
      }

      // Passo 5: Testar login com senha nova (deve funcionar)
      try {
        console.log('🔐 Testando login com senha nova...');
        const response = await fetch('http://localhost:3001/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email: emailTeste, senha: senhaNova }),
        });

        if (response.ok) {
          adicionarResultado('5. Login senha nova', true, 'Login funcionou com senha nova');
        } else {
          adicionarResultado('5. Login senha nova', false, 'ERRO: Login falhou com senha nova (deveria funcionar)');
        }
      } catch (error) {
        adicionarResultado('5. Login senha nova', false, 'ERRO: Login falhou com senha nova (deveria funcionar)');
      }

      // Passo 6: Buscar usuário para verificar dados
      try {
        const responseBuscar = await fetch(`http://localhost:3001/api/usuarios/${usuarioId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (responseBuscar.ok) {
          const usuarioBuscado = await responseBuscar.json();
          adicionarResultado('6. Verificar dados', true, `Usuário encontrado: ${usuarioBuscado.nome}`);
        } else {
          adicionarResultado('6. Verificar dados', false, 'Usuário não encontrado');
        }
      } catch (error) {
        adicionarResultado('6. Verificar dados', false, error.message);
      }

      // Passo 7: Limpar - deletar usuário de teste
      try {
        const responseDelete = await fetch(`http://localhost:3001/api/usuarios/${usuarioId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (responseDelete.ok) {
          adicionarResultado('7. Limpeza', true, 'Usuário de teste deletado');
        } else {
          adicionarResultado('7. Limpeza', false, 'Erro ao deletar usuário de teste');
        }
      } catch (error) {
        adicionarResultado('7. Limpeza', false, 'Erro ao deletar usuário de teste');
      }

    } catch (error) {
      adicionarResultado('Erro geral', false, error.message);
    } finally {
      setTestando(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, margin: '0 auto' }}>
      <Typography variant="h4" sx={{ mb: 3 }}>
        🔐 Teste de Alteração de Senhas
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Configurações do Teste
        </Typography>
        
        <TextField
          fullWidth
          label="Email de Teste"
          value={emailTeste}
          onChange={(e) => setEmailTeste(e.target.value)}
          sx={{ mb: 2 }}
          disabled={testando}
        />
        
        <TextField
          fullWidth
          label="Senha Original"
          value={senhaOriginal}
          onChange={(e) => setSenhaOriginal(e.target.value)}
          sx={{ mb: 2 }}
          disabled={testando}
        />
        
        <TextField
          fullWidth
          label="Senha Nova"
          value={senhaNova}
          onChange={(e) => setSenhaNova(e.target.value)}
          sx={{ mb: 2 }}
          disabled={testando}
        />

        <Button
          variant="contained"
          onClick={executarTesteSenhas}
          disabled={testando}
          size="large"
          sx={{ mt: 2 }}
        >
          {testando ? 'Executando Teste...' : 'Executar Teste de Senhas'}
        </Button>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          📋 Resultados do Teste
        </Typography>

        {resultados.length === 0 && !testando && (
          <Typography color="text.secondary">
            Configure os parâmetros acima e clique em "Executar Teste"
          </Typography>
        )}

        {resultados.map((resultado, index) => (
          <Alert
            key={index}
            severity={resultado.sucesso ? 'success' : 'error'}
            sx={{ mb: 1 }}
          >
            <Typography variant="body2">
              <strong>{resultado.teste}</strong> - {resultado.timestamp}
            </Typography>
            <Typography variant="body2">
              {resultado.detalhes}
            </Typography>
          </Alert>
        ))}
      </Paper>

      <Paper sx={{ p: 3, mt: 3, backgroundColor: 'grey.50' }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          📝 O que este teste faz:
        </Typography>
        <Typography variant="body2" component="div">
          <ol>
            <li>Cria um usuário de teste com senha original</li>
            <li>Testa login com senha original (deve funcionar)</li>
            <li>Altera a senha do usuário</li>
            <li>Testa login com senha antiga (deve falhar)</li>
            <li>Testa login com senha nova (deve funcionar)</li>
            <li>Verifica dados do usuário</li>
            <li>Remove usuário de teste</li>
          </ol>
        </Typography>

        <Typography variant="body2" sx={{ mt: 2, fontWeight: 'bold' }}>
          ✅ Se todos os passos passarem: Sistema funcionando corretamente
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'error.main' }}>
          ❌ Se passo 5 falhar: Problema na atualização de senhas
        </Typography>

        <Typography variant="body2" sx={{ mt: 2, p: 2, backgroundColor: 'warning.light', borderRadius: 1 }}>
          ⚠️ <strong>IMPORTANTE:</strong> Você deve estar logado como administrador para executar este teste.
          Se não estiver logado, faça login primeiro em: <a href="/login" target="_blank">http://localhost:5173/login</a>
        </Typography>
      </Paper>
    </Box>
  );
};

export default TesteSenhas;
