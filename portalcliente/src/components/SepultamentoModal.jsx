import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Button } from '@mui/material';
import Modal from './Modal';
import GavetaSelector from './GavetaSelector';
import { sepultamentoService } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  min-height: 80px;
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const StyledButton = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;

  &.primary {
    background: linear-gradient(135deg, #1e3a8a 0%, #059669 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 4px;
`;

const SectionTitle = styled.h3`
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.125rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 8px;
`;

const SepultamentoModal = ({ isOpen, onClose, sepultamento = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    nome_sepultado: '',
    data_sepultamento: '',
    horario_sepultamento: '',
    observacoes: '',
    gaveta_selection: {}
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { user, getCodigoCliente } = useAuth();

  useEffect(() => {
    if (isOpen) {
      if (sepultamento) {
        setFormData({
          nome_sepultado: sepultamento.nome_sepultado || '',
          data_sepultamento: sepultamento.data_sepultamento ? sepultamento.data_sepultamento.split('T')[0] : '',
          horario_sepultamento: sepultamento.horario_sepultamento || '',
          observacoes: sepultamento.observacoes || '',
          gaveta_selection: {
            produto_id: sepultamento.produto_id || '',
            bloco_id: sepultamento.bloco_id || '',
            sub_bloco_id: sepultamento.sub_bloco_id || '',
            gaveta_id: sepultamento.gaveta_id || ''
          }
        });
      } else {
        setFormData({
          nome_sepultado: '',
          data_sepultamento: new Date().toISOString().split('T')[0],
          horario_sepultamento: '',
          observacoes: '',
          gaveta_selection: {}
        });
      }
      setError('');
    }
  }, [isOpen, sepultamento]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Formatação para horário
    let formattedValue = value;
    if (name === 'horario_sepultamento') {
      // Aplicar máscara de horário (HH:MM)
      const numbers = value.replace(/\D/g, '');
      if (numbers.length <= 4) {
        if (numbers.length >= 3) {
          formattedValue = numbers.replace(/(\d{2})(\d{1,2})/, '$1:$2');
        } else {
          formattedValue = numbers;
        }
      }
    }

    setFormData(prev => ({
      ...prev,
      [name]: formattedValue
    }));
  };

  const handleGavetaChange = (gavetaSelection) => {
    setFormData(prev => ({
      ...prev,
      gaveta_selection: gavetaSelection
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validar seleção de gaveta
      if (!formData.gaveta_selection.gaveta_numero) {
        setError('Selecione uma gaveta para o sepultamento');
        setLoading(false);
        return;
      }

      const dadosParaEnvio = {
        nome_sepultado: formData.nome_sepultado,
        data_sepultamento: formData.data_sepultamento,
        horario_sepultamento: formData.horario_sepultamento,
        observacoes: formData.observacoes,
        codigo_cliente: getCodigoCliente(),
        codigo_bloco: formData.gaveta_selection.bloco_codigo,
        codigo_sub_bloco: formData.gaveta_selection.sub_bloco_codigo,
        numero_gaveta: formData.gaveta_selection.gaveta_numero,
        localizacao: `${formData.gaveta_selection.bloco_codigo}-${formData.gaveta_selection.sub_bloco_codigo}-${formData.gaveta_selection.gaveta_numero}`
      };

      if (sepultamento) {
        await sepultamentoService.atualizar(sepultamento.id, dadosParaEnvio);
        alert('Sepultamento atualizado com sucesso!');
      } else {
        await sepultamentoService.criar(dadosParaEnvio);
        alert('Sepultamento criado com sucesso!');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar sepultamento:', error);
      setError(error.response?.data?.error || 'Erro ao salvar sepultamento');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={sepultamento ? 'Editar Sepultamento' : 'Novo Sepultamento'}
      maxWidth="900px"
    >
      <Form onSubmit={handleSubmit}>
        <SectionTitle>Informações do Sepultado</SectionTitle>
        
        <FormGroup>
          <Label>Nome do Sepultado *</Label>
          <Input
            type="text"
            name="nome_sepultado"
            value={formData.nome_sepultado}
            onChange={handleChange}
            required
            placeholder="Nome completo do sepultado"
          />
        </FormGroup>

        <FormRow>
          <FormGroup>
            <Label>Data de Sepultamento *</Label>
            <Input
              type="date"
              name="data_sepultamento"
              value={formData.data_sepultamento}
              onChange={handleChange}
              required
            />
          </FormGroup>
          <FormGroup>
            <Label>Horário do Sepultamento *</Label>
            <Input
              type="text"
              name="horario_sepultamento"
              value={formData.horario_sepultamento}
              onChange={handleChange}
              placeholder="Ex: 14:30, 09:15"
              maxLength="5"
              required
            />
          </FormGroup>
        </FormRow>

        <SectionTitle>Localização</SectionTitle>
        
        <GavetaSelector
          value={formData.gaveta_selection}
          onChange={handleGavetaChange}
          codigoCliente={getCodigoCliente()}
          disabled={loading}
        />

        <FormGroup>
          <Label>Observações</Label>
          <TextArea
            name="observacoes"
            value={formData.observacoes}
            onChange={handleChange}
            placeholder="Observações adicionais sobre o sepultamento..."
          />
        </FormGroup>

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <ButtonGroup>
          <StyledButton type="button" className="secondary" onClick={onClose}>
            Cancelar
          </StyledButton>
          <StyledButton type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (sepultamento ? 'Atualizar' : 'Cadastrar')}
          </StyledButton>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

export default SepultamentoModal;
