import React, { useState, useEffect } from 'react';
import { Button, Typography, Paper } from '@mui/material';
import styled from 'styled-components';
import Modal from './Modal';
import { produtoService } from '../services/api';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const StyledFormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
  }
`;

const TextArea = styled.textarea`
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  transition: border-color 0.2s;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const StyledRangeContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
`;

const RangeHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const RangeTitle = styled.h4`
  margin: 0;
  color: #374151;
  font-size: 0.875rem;
`;

const RemoveButton = styled.button`
  background: #ef4444;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.75rem;

  &:hover {
    background: #dc2626;
  }
`;

const RangeInputs = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
`;

const AddRangeButton = styled.button`
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  margin-bottom: 16px;

  &:hover {
    background: #059669;
  }
`;

const StyledButtonGroup = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
`;

const StyledButton = styled.button`
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;

  &.primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }

  &.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover {
      background: #e5e7eb;
    }
  }

  &.danger {
    background: #ef4444;
    color: white;

    &:hover {
      background: #dc2626;
    }

    &:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
  }
`;

const ErrorMessage = styled.div`
  background: #fef2f2;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
`;

const InfoText = styled.div`
  background: #f0f9ff;
  color: #0369a1;
  padding: 12px;
  border-radius: 8px;
  font-size: 0.875rem;
  margin-bottom: 16px;
`;

const SubBlocoModal = ({ isOpen, onClose, subBloco, blocoId, onSuccess }) => {
  const [formData, setFormData] = useState({
    codigo_sub_bloco: '',
    nome: ''
  });
  const [ranges, setRanges] = useState([{ inicio: '', fim: '' }]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      if (subBloco) {
        setFormData({
          codigo_sub_bloco: subBloco.codigo_sub_bloco || '',
          nome: subBloco.nome || subBloco.denominacao || ''
        });
        // Para edição, carregar ranges existentes se disponíveis
        if (subBloco.ranges_gavetas) {
          const rangesExistentes = subBloco.ranges_gavetas.split(', ').map(range => {
            if (range.includes('-')) {
              const [inicio, fim] = range.split('-');
              return { inicio, fim };
            } else {
              return { inicio: range, fim: range };
            }
          });
          setRanges(rangesExistentes);
        } else {
          setRanges([{ inicio: '', fim: '' }]);
        }
      } else {
        // Para novos sub-blocos, gerar código automático
        const generateCodigo = async () => {
          try {
            // Buscar sub-blocos existentes para gerar próximo código
            const response = await produtoService.listarSubBlocos({ bloco_id: blocoId });
            const subBlocosExistentes = response.data;

            // Extrair números dos códigos existentes
            const numeros = subBlocosExistentes
              .map(sb => {
                const match = sb.codigo_sub_bloco.match(/SUB_(\d+)/);
                return match ? parseInt(match[1]) : 0;
              })
              .filter(num => num > 0);

            // Encontrar próximo número disponível
            const proximoNumero = numeros.length > 0 ? Math.max(...numeros) + 1 : 1;
            const novoCodigo = `SUB_${proximoNumero.toString().padStart(3, '0')}`;

            setFormData({
              codigo_sub_bloco: novoCodigo,
              nome: ''
            });
          } catch (error) {
            console.error('Erro ao gerar código automático:', error);
            // Fallback para código baseado em timestamp
            const timestamp = Date.now().toString().slice(-3);
            setFormData({
              codigo_sub_bloco: `SUB_${timestamp}`,
              nome: ''
            });
          }
        };

        generateCodigo();
        setRanges([{ inicio: '', fim: '' }]);
      }
      setError('');
    }
  }, [isOpen, subBloco]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRangeChange = (index, field, value) => {
    const newRanges = [...ranges];
    newRanges[index][field] = value;
    setRanges(newRanges);
  };

  const addRange = () => {
    setRanges([...ranges, { inicio: '', fim: '' }]);
  };

  const removeRange = (index) => {
    if (ranges.length > 1) {
      setRanges(ranges.filter((_, i) => i !== index));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Preparar dados com denominacao igual ao nome
      const dadosParaEnvio = {
        ...formData,
        denominacao: formData.nome, // denominacao = nome
        descricao: formData.nome    // descricao = nome
      };

      if (subBloco) {
        // EDIÇÃO: Incluir ranges
        const dados = {
          ...dadosParaEnvio,
          numeracao_gavetas: ranges.filter(r => r.inicio && r.fim).map(r => ({
            inicio: parseInt(r.inicio),
            fim: parseInt(r.fim)
          }))
        };
        await produtoService.atualizarSubBlocoSimples(subBloco.id, dados);
        alert('Sub-bloco atualizado com sucesso!');
      } else {
        // CRIAÇÃO: SEM ranges (serão gerenciados depois)
        const dados = {
          ...dadosParaEnvio,
          bloco_id: blocoId
          // NÃO incluir numeracao_gavetas na criação
        };
        await produtoService.criarSubBlocoSimples(dados);
        alert('Sub-bloco criado com sucesso!\n\nUse o botão "Gerenciar Ranges" para definir as numerações das gavetas.');
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao salvar sub-bloco:', error);

      let errorMessage = 'Erro ao salvar sub-bloco';

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias || errorData.acao_necessaria) {
          // Erro de validação hierárquica
          errorMessage = `${errorData.error}\n\n${errorData.detalhes || ''}`;

          if (errorData.acao_necessaria) {
            errorMessage += `\n\n🔧 ${errorData.acao_necessaria}`;
          }

          if (errorData.ranges_encontrados) {
            errorMessage += `\n\n📦 ${errorData.ranges_encontrados} range(s) encontrado(s)`;
          }
        } else {
          errorMessage = errorData.error || 'Dados inválidos para salvar o sub-bloco';
        }
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado. Apenas administradores podem editar sub-blocos.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Sub-bloco não encontrado. Pode ter sido removido por outro usuário.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!subBloco || !window.confirm('Tem certeza que deseja deletar este sub-bloco? Esta ação não pode ser desfeita.')) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await produtoService.deletarSubBlocoSimples(subBloco.id);
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Erro ao deletar sub-bloco:', error);

      let errorMessage = 'Erro ao deletar sub-bloco';

      if (error.response?.status === 400) {
        const errorData = error.response.data;

        if (errorData.dependencias) {
          // Erro de validação hierárquica
          errorMessage = `❌ ${errorData.error}\n\n📋 ${errorData.detalhes}\n\n🔧 ${errorData.acao_necessaria}`;

          if (errorData.passos_obrigatorios && errorData.passos_obrigatorios.length > 0) {
            errorMessage += '\n\nPassos obrigatórios:\n' +
              errorData.passos_obrigatorios.map((passo, index) => `${index + 1}. ${passo}`).join('\n');
          }

          if (errorData.ranges_detalhes) {
            errorMessage += `\n\n📦 Ranges encontrados: ${errorData.ranges_detalhes}`;
          }
        } else {
          errorMessage = errorData.error || 'Não é possível deletar este sub-bloco';
        }
      } else if (error.response?.status === 401) {
        errorMessage = 'Sessão expirada. Faça login novamente para continuar.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado. Apenas administradores podem deletar sub-blocos.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Sub-bloco não encontrado. Pode ter sido removido por outro usuário.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={subBloco ? 'Editar Sub-bloco' : 'Novo Sub-bloco'}
      maxWidth="700px"
    >
      <Form onSubmit={handleSubmit}>
        {subBloco && (
          <Paper sx={{ p: 2, bgcolor: '#fff3cd', borderLeft: '4px solid #ffc107', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#856404', mb: 1 }}>
              ⚠️ Editando Sub-bloco Existente
            </Typography>
            <Typography variant="body2" sx={{ color: '#856404' }}>
              • <strong>Campos Editáveis:</strong> Nome do Sub-bloco
              <br />
              • <strong>Campo Protegido:</strong> Código do Sub-bloco (não pode ser alterado)
              <br />
              • <strong>Para alterar código:</strong> Delete este sub-bloco e todos os dados filhos, depois crie um novo
              <br />
              • <strong>Impacto da deleção:</strong> Remove gavetas e sepultamentos associados
              <br />
              • <strong>Ranges de gavetas:</strong> Use "Gerenciar Ranges" para definir numerações
            </Typography>
          </Paper>
        )}

        {!subBloco && (
          <Paper sx={{ p: 2, bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#2e7d32', mb: 1 }}>
              ✨ Criando Novo Sub-bloco
            </Typography>
            <Typography variant="body2" sx={{ color: '#2e7d32' }}>
              • <strong>Defina cuidadosamente:</strong> Código do Sub-bloco (não poderá ser alterado depois)
              <br />
              • <strong>Padrão recomendado:</strong> Use códigos sequenciais (ex: SB01, SB02, SB03)
              <br />
              • <strong>Após criação:</strong> Use "Gerenciar Ranges" para definir as numerações das gavetas
              <br />
              • <strong>Ranges flexíveis:</strong> Você pode criar múltiplos ranges conforme necessário
            </Typography>
          </Paper>
        )}

        {!subBloco && (
          <StyledFormGroup>
            <Label>Código do Sub-bloco *</Label>
            <Input
              type="text"
              name="codigo_sub_bloco"
              value={formData.codigo_sub_bloco}
              onChange={handleChange}
              required
              placeholder="Ex: SB01"
            />
          </StyledFormGroup>
        )}

        {subBloco && (
          <Paper sx={{ p: 2, bgcolor: '#e3f2fd', borderLeft: '4px solid #2196f3', mb: 2 }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 1 }}>
              📝 Sub-bloco em Edição
            </Typography>
            <Typography variant="body2" sx={{ color: '#1976d2' }}>
              <strong>Código do Sub-bloco:</strong> {formData.codigo_sub_bloco}
            </Typography>
          </Paper>
        )}

        <StyledFormGroup>
          <Label>Nome do Sub-bloco *</Label>
          <Input
            type="text"
            name="nome"
            value={formData.nome}
            onChange={handleChange}
            required
            placeholder="Ex: Sub-bloco Principal"
          />
          <Typography variant="caption" sx={{ color: '#666', mt: 1 }}>
            Este nome será usado como denominação e descrição do sub-bloco
          </Typography>
        </StyledFormGroup>

        {/* RANGES APENAS PARA EDIÇÃO - NÃO PARA CRIAÇÃO */}
        {subBloco && (
          <StyledFormGroup>
            <Label>Numeração das Gavetas</Label>
            <InfoText>
              Edite os ranges de numeração das gavetas. ATENÇÃO: Alterar ranges irá recriar todas as gavetas. Certifique-se de que não há sepultamentos ativos.
            </InfoText>

              {ranges.map((range, index) => (
                <StyledRangeContainer key={index}>
                  <RangeHeader>
                    <RangeTitle>Range {index + 1}</RangeTitle>
                    {ranges.length > 1 && (
                      <RemoveButton onClick={() => removeRange(index)}>
                        Remover
                      </RemoveButton>
                    )}
                  </RangeHeader>
                  <RangeInputs>
                    <div>
                      <Label>Gaveta Inicial</Label>
                      <Input
                        type="number"
                        min="1"
                        value={range.inicio}
                        onChange={(e) => handleRangeChange(index, 'inicio', e.target.value)}
                        placeholder="Ex: 1"
                      />
                    </div>
                    <div>
                      <Label>Gaveta Final</Label>
                      <Input
                        type="number"
                        min="1"
                        value={range.fim}
                        onChange={(e) => handleRangeChange(index, 'fim', e.target.value)}
                        placeholder="Ex: 20"
                      />
                    </div>
                  </RangeInputs>
                </StyledRangeContainer>
              ))}

              <AddRangeButton type="button" onClick={addRange}>
                + Adicionar Range
              </AddRangeButton>
            </StyledFormGroup>
        )}

        {/* INFORMAÇÃO PARA NOVOS SUB-BLOCOS */}
        {!subBloco && (
          <InfoText>
            📝 Após criar o sub-bloco, use o botão "Gerenciar Ranges" para definir as numerações das gavetas.
          </InfoText>
        )}

        {error && <ErrorMessage>{error}</ErrorMessage>}

        <StyledButtonGroup>
          <StyledButton type="button" className="secondary" onClick={onClose}>
            Cancelar
          </StyledButton>
          {subBloco && (
            <StyledButton
              type="button"
              className="danger"
              onClick={handleDelete}
              disabled={loading}
            >
              {loading ? 'Deletando...' : 'Deletar'}
            </StyledButton>
          )}
          <StyledButton type="submit" className="primary" disabled={loading}>
            {loading ? 'Salvando...' : (subBloco ? 'Atualizar' : 'Criar')}
          </StyledButton>
        </StyledButtonGroup>
      </Form>
    </Modal>
  );
};

export default SubBlocoModal;
