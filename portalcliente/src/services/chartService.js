/**
 * SERVIÇO: GERAÇÃO DE GRÁFICOS PARA PDF
 * Gera gráficos de temperatura e umidade usando Chart.js
 * Autor: Sistema Multi-Agente
 * Data: 2025-01-25
 */

import Chart from 'chart.js/auto';

/**
 * Configurações padrão para gráficos otimizadas para PDF
 * Dimensões ajustadas para máxima qualidade em folha A4
 * Baseado em 170mm x 70mm disponível respeitando rodapé
 * Fontes aumentadas em 3x para melhor legibilidade
 */
const CHART_CONFIG = {
  width: 2400, // Largura aumentada para acomodar fontes maiores
  height: 1000, // Altura aumentada para acomodar fontes maiores
  backgroundColor: '#ffffff',
  fontFamily: 'Arial, sans-serif',
  fontSize: 48, // 3x maior (16 * 3)
  // Configurações de performance e qualidade máxima
  devicePixelRatio: 3, // Máxima qualidade para PDF
  animation: false,
  responsive: false
};

/**
 * Cria um canvas virtual otimizado para renderização do gráfico
 * @param {number} width - Largura do canvas
 * @param {number} height - Altura do canvas
 * @returns {HTMLCanvasElement} Canvas element
 */
function createCanvas(width = CHART_CONFIG.width, height = CHART_CONFIG.height) {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  canvas.style.backgroundColor = CHART_CONFIG.backgroundColor;

  // Otimizações de performance
  const ctx = canvas.getContext('2d');
  ctx.imageSmoothingEnabled = false; // Desabilitar suavização para melhor performance

  return canvas;
}

/**
 * Reorganiza dados para 6 linhas separadas (3 temp + 3 umid) com datas simples
 * @param {Object} dadosGraficos - Dados originais do gráfico
 * @returns {Object} Dados reorganizados para 6 linhas
 */
function calcularMediasPorDia(dadosGraficos) {
  console.log('🔍 Dados de entrada para cálculo de médias:', {
    labels: dadosGraficos.labels,
    temperatura: dadosGraficos.temperatura,
    umidade: dadosGraficos.umidade
  });

  const dadosReorganizados = {
    labels: [], // Apenas datas: 01/07, 02/07, etc.
    temperaturaMedia: [],
    umidadeMedia: []
  };

  // Agrupar dados por data
  const dadosPorData = {};

  for (let i = 0; i < dadosGraficos.labels.length; i++) {
    const label = dadosGraficos.labels[i];
    const temperatura = dadosGraficos.temperatura[i];
    const umidade = dadosGraficos.umidade[i];

    console.log(`🔍 Processando item ${i}:`, { label, temperatura, umidade });

    // Extrair data no formato DD/MM e período
    let data, periodo;

    // Verificar se é formato "01/07 M" ou "01/07/2025 - Manhã"
    if (label.includes(' M')) {
      data = label.replace(' M', '');
      periodo = 'Manha';
    } else if (label.includes(' T')) {
      data = label.replace(' T', '');
      periodo = 'Tarde';
    } else if (label.includes(' N')) {
      data = label.replace(' N', '');
      periodo = 'Noite';
    } else if (label.includes('Manhã')) {
      const dataMatch = label.match(/(\d{2}\/\d{2})/);
      data = dataMatch ? dataMatch[1] : label;
      periodo = 'Manha';
    } else if (label.includes('Tarde')) {
      const dataMatch = label.match(/(\d{2}\/\d{2})/);
      data = dataMatch ? dataMatch[1] : label;
      periodo = 'Tarde';
    } else {
      const dataMatch = label.match(/(\d{2}\/\d{2})/);
      data = dataMatch ? dataMatch[1] : label;
      periodo = 'Noite';
    }

    console.log(`🔍 Extraído: data=${data}, periodo=${periodo}`);

    if (!dadosPorData[data]) {
      dadosPorData[data] = {
        temperaturas: [],
        umidades: []
      };
    }

    // Adicionar valores aos arrays para cálculo da média
    if (temperatura !== undefined && temperatura !== null) {
      dadosPorData[data].temperaturas.push(parseFloat(temperatura));
    }
    if (umidade !== undefined && umidade !== null) {
      dadosPorData[data].umidades.push(parseFloat(umidade));
    }
  }

  console.log('🔍 Dados agrupados por data:', dadosPorData);

  // Ordenar datas e calcular médias
  const datasOrdenadas = Object.keys(dadosPorData).sort();

  datasOrdenadas.forEach(data => {
    const temperaturas = dadosPorData[data].temperaturas;
    const umidades = dadosPorData[data].umidades;

    // Calcular média da temperatura (manhã + tarde + noite) / 3
    let temperaturaMedia = null;
    if (temperaturas.length > 0) {
      const soma = temperaturas.reduce((acc, val) => acc + val, 0);
      temperaturaMedia = soma / temperaturas.length;
    }

    // Calcular média da umidade (manhã + tarde + noite) / 3
    let umidadeMedia = null;
    if (umidades.length > 0) {
      const soma = umidades.reduce((acc, val) => acc + val, 0);
      umidadeMedia = soma / umidades.length;
    }

    dadosReorganizados.labels.push(data);
    dadosReorganizados.temperaturaMedia.push(temperaturaMedia);
    dadosReorganizados.umidadeMedia.push(umidadeMedia);
  });

  console.log('🔍 Médias calculadas:', {
    labels: dadosReorganizados.labels,
    temperaturaMedia: dadosReorganizados.temperaturaMedia,
    umidadeMedia: dadosReorganizados.umidadeMedia
  });

  return dadosReorganizados;
}

/**
 * Valida os dados do gráfico
 * @param {Object} dadosGraficos - Dados para validação
 * @param {string} nomeBloco - Nome do bloco
 * @throws {Error} Se dados inválidos
 */
function validarDadosGrafico(dadosGraficos, nomeBloco) {
  if (!dadosGraficos) {
    throw new Error(`Dados do gráfico não fornecidos para o bloco ${nomeBloco}`);
  }

  if (!dadosGraficos.labels || !Array.isArray(dadosGraficos.labels) || dadosGraficos.labels.length === 0) {
    throw new Error(`Labels do gráfico ausentes ou vazios para o bloco ${nomeBloco}`);
  }

  if (!dadosGraficos.temperatura || !Array.isArray(dadosGraficos.temperatura)) {
    throw new Error(`Dados de temperatura ausentes para o bloco ${nomeBloco}`);
  }

  if (!dadosGraficos.umidade || !Array.isArray(dadosGraficos.umidade)) {
    throw new Error(`Dados de umidade ausentes para o bloco ${nomeBloco}`);
  }

  // Verificar se todos os arrays têm o mesmo tamanho
  const tamanhoLabels = dadosGraficos.labels.length;
  if (dadosGraficos.temperatura.length !== tamanhoLabels || dadosGraficos.umidade.length !== tamanhoLabels) {
    throw new Error(`Inconsistência no tamanho dos dados para o bloco ${nomeBloco}`);
  }
}

/**
 * Gera gráfico de temperatura e umidade com 6 linhas separadas (3 temp + 3 umid)
 * @param {Object} dadosGraficos - Dados organizados para gráfico
 * @param {string} nomeBloco - Nome do bloco
 * @returns {Promise<string>} Base64 da imagem do gráfico
 */
export async function gerarGraficoTemperaturaUmidade(dadosGraficos, nomeBloco) {
  try {
    // Validar dados antes de prosseguir
    validarDadosGrafico(dadosGraficos, nomeBloco);

    const canvas = createCanvas();
    const ctx = canvas.getContext('2d');

    // Calcular médias por dia (manhã + tarde + noite) / 3
    const dadosComMedias = calcularMediasPorDia(dadosGraficos);

    // Configuração do gráfico com 2 linhas (médias)
    const config = {
      type: 'line',
      data: {
        labels: dadosComMedias.labels, // Apenas datas: 01/07, 02/07, etc.
        datasets: [
          // LINHA 1 - Temperatura Média - Vermelho
          {
            label: 'Temperatura Média (°C)',
            data: dadosComMedias.temperaturaMedia,
            borderColor: '#E74C3C', // Vermelho
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            borderWidth: 3,
            fill: false,
            tension: 0.4,
            yAxisID: 'y',
            pointRadius: 4,
            pointHoverRadius: 6
          },
          // LINHA 2 - Umidade Média - Azul
          {
            label: 'Umidade Média (%)',
            data: dadosComMedias.umidadeMedia,
            borderColor: '#3498DB', // Azul
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            borderWidth: 3,
            fill: false,
            tension: 0.4,
            yAxisID: 'y1',
            pointRadius: 4,
            pointHoverRadius: 6
          }
        ]
      },
      options: {
        responsive: false,
        animation: false,
        plugins: {
          title: {
            display: false // Removido título intrínseco conforme solicitado
          },
          legend: {
            display: true,
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20,
              font: {
                size: 36 // 3x maior (12 * 3)
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Data',
              font: {
                size: 36, // 3x maior (12 * 3)
                weight: 'bold'
              }
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45,
              font: {
                size: 30 // 3x maior (10 * 3)
              }
            },
            grid: {
              display: true,
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            min: 0,
            max: 50,
            title: {
              display: true,
              text: 'Temperatura (°C)',
              font: {
                size: 36, // 3x maior (12 * 3)
                weight: 'bold'
              },
              color: '#FF6384'
            },
            ticks: {
              color: '#FF6384',
              font: {
                size: 30 // 3x maior (10 * 3)
              },
              stepSize: 10
            },
            grid: {
              drawOnChartArea: false,
              color: '#FF6384'
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            min: 0,
            max: 100,
            title: {
              display: true,
              text: 'Umidade (%)',
              font: {
                size: 36, // 3x maior (12 * 3)
                weight: 'bold'
              },
              color: '#36A2EB'
            },
            ticks: {
              color: '#36A2EB',
              font: {
                size: 30 // 3x maior (10 * 3)
              },
              stepSize: 20
            },
            grid: {
              drawOnChartArea: false,
              color: '#36A2EB'
            }
          }
        },
        elements: {
          point: {
            radius: 4,
            hoverRadius: 6,
            borderWidth: 2
          },
          line: {
            borderWidth: 3,
            tension: 0.4 // Interpolação suave
          }
        },
        interaction: {
          intersect: false,
          mode: 'index'
        }
      }
    };

    // Criar e renderizar o gráfico
    const chart = new Chart(ctx, config);
    
    // Aguardar renderização
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Converter para base64
    const imageData = canvas.toDataURL('image/png');
    
    // Limpar recursos
    chart.destroy();
    
    return imageData;
    
  } catch (error) {
    console.error('❌ Erro ao gerar gráfico:', error);
    throw error;
  }
}

/**
 * Gera gráfico apenas de temperatura para um bloco
 * @param {Object} dadosGraficos - Dados organizados para gráfico
 * @param {string} nomeBloco - Nome do bloco
 * @returns {Promise<string>} Base64 da imagem do gráfico
 */
export async function gerarGraficoTemperatura(dadosGraficos, nomeBloco) {
  try {
    const canvas = createCanvas();
    const ctx = canvas.getContext('2d');

    const config = {
      type: 'line',
      data: {
        labels: dadosGraficos.labels,
        datasets: [
          {
            label: 'Temperatura (°C)',
            data: dadosGraficos.temperatura,
            borderColor: '#FF6384',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderWidth: 2,
            fill: true,
            tension: 0.1
          }
        ]
      },
      options: {
        responsive: false,
        animation: false,
        plugins: {
          title: {
            display: true,
            text: `Temperatura - ${nomeBloco}`,
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            display: true,
            position: 'top'
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Período'
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45,
              font: {
                size: 10
              }
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Temperatura (°C)'
            }
          }
        },
        elements: {
          point: {
            radius: 3,
            hoverRadius: 5
          }
        }
      }
    };

    const chart = new Chart(ctx, config);
    await new Promise(resolve => setTimeout(resolve, 100));
    const imageData = canvas.toDataURL('image/png');
    chart.destroy();
    
    return imageData;
    
  } catch (error) {
    console.error('❌ Erro ao gerar gráfico de temperatura:', error);
    throw error;
  }
}

/**
 * Gera gráfico apenas de umidade para um bloco
 * @param {Object} dadosGraficos - Dados organizados para gráfico
 * @param {string} nomeBloco - Nome do bloco
 * @returns {Promise<string>} Base64 da imagem do gráfico
 */
export async function gerarGraficoUmidade(dadosGraficos, nomeBloco) {
  try {
    const canvas = createCanvas();
    const ctx = canvas.getContext('2d');

    const config = {
      type: 'line',
      data: {
        labels: dadosGraficos.labels,
        datasets: [
          {
            label: 'Umidade (%)',
            data: dadosGraficos.umidade,
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderWidth: 2,
            fill: true,
            tension: 0.1
          }
        ]
      },
      options: {
        responsive: false,
        animation: false,
        plugins: {
          title: {
            display: true,
            text: `Umidade Relativa - ${nomeBloco}`,
            font: {
              size: 16,
              weight: 'bold'
            }
          },
          legend: {
            display: true,
            position: 'top'
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Período'
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45,
              font: {
                size: 10
              }
            }
          },
          y: {
            display: true,
            title: {
              display: true,
              text: 'Umidade (%)'
            }
          }
        },
        elements: {
          point: {
            radius: 3,
            hoverRadius: 5
          }
        }
      }
    };

    const chart = new Chart(ctx, config);
    await new Promise(resolve => setTimeout(resolve, 100));
    const imageData = canvas.toDataURL('image/png');
    chart.destroy();

    return imageData;

  } catch (error) {
    console.error('❌ Erro ao gerar gráfico de umidade:', error);
    throw error;
  }
}

/**
 * Gera gráfico de Volume de Ar e Pressão de Estanque para um bloco
 * @param {Array} dadosVolumeArPressao - Dados organizados para gráfico
 * @param {string} nomeBloco - Nome do bloco
 * @returns {Promise<string>} Base64 da imagem do gráfico
 */
export async function gerarGraficoVolumeArPressao(dadosVolumeArPressao, nomeBloco) {
  try {
    // Validar dados
    if (!dadosVolumeArPressao || dadosVolumeArPressao.length === 0) {
      throw new Error('Dados de volume de ar e pressão não fornecidos');
    }

    const canvas = createCanvas();
    const ctx = canvas.getContext('2d');

    // Preparar dados para o gráfico
    const labels = dadosVolumeArPressao.map(item => item.data);
    const volumeArData = dadosVolumeArPressao.map(item => item.volumeAr || 0);
    const pressaoData = dadosVolumeArPressao.map(item => item.pressaoEstanque || 0);

    console.log('🔍 DEBUG - Dados do gráfico Volume/Pressão:', {
      labels,
      volumeArData,
      pressaoData,
      dadosOriginais: dadosVolumeArPressao
    });

    const config = {
      type: 'line',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'Volume de Ar (m³)',
            data: volumeArData,
            borderColor: '#FF9F40',
            backgroundColor: 'rgba(255, 159, 64, 0.1)',
            borderWidth: 3,
            fill: false,
            tension: 0.4, // Interpolação suave conforme solicitado
            yAxisID: 'y',
            pointRadius: 4,
            pointHoverRadius: 6
          },
          {
            label: 'Pressão de Estanque (mmH2O)',
            data: pressaoData,
            borderColor: '#4BC0C0',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            borderWidth: 3,
            fill: false,
            tension: 0.4, // Interpolação suave conforme solicitado
            yAxisID: 'y1',
            pointRadius: 4,
            pointHoverRadius: 6
          }
        ]
      },
      options: {
        responsive: false,
        animation: false,
        plugins: {
          title: {
            display: false // Removido título intrínseco conforme solicitado
          },
          legend: {
            display: true,
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 25,
              font: {
                size: 36 // 3x maior (12 * 3)
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            title: {
              display: true,
              text: 'Data',
              font: {
                size: 36, // 3x maior (12 * 3)
                weight: 'bold'
              }
            },
            ticks: {
              maxRotation: 45,
              minRotation: 45,
              font: {
                size: 30 // 3x maior (10 * 3)
              }
            },
            grid: {
              display: true,
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            min: 0,
            title: {
              display: true,
              text: 'Volume de Ar (m³)',
              font: {
                size: 36, // 3x maior (12 * 3)
                weight: 'bold'
              },
              color: '#FF9F40'
            },
            ticks: {
              color: '#FF9F40',
              font: {
                size: 30 // 3x maior (10 * 3)
              }
            },
            grid: {
              drawOnChartArea: false,
              color: '#FF9F40'
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            min: -150, // Valor mínimo (mais negativo)
            max: 0,    // Valor máximo (menos negativo)
            reverse: true, // Inverter para que 0 fique na base e -150 no topo
            title: {
              display: true,
              text: 'Pressão de Estanque (mmH2O)',
              font: {
                size: 36, // 3x maior (12 * 3)
                weight: 'bold'
              },
              color: '#4BC0C0'
            },
            ticks: {
              color: '#4BC0C0',
              font: {
                size: 30 // 3x maior (10 * 3)
              },
              stepSize: 30, // Positivo, o reverse cuidará da inversão
              callback: function(value) {
                return value; // Mostrar valores negativos normalmente
              }
            },
            grid: {
              drawOnChartArea: false,
              color: '#4BC0C0'
            }
          }
        },
        elements: {
          point: {
            radius: 4,
            hoverRadius: 6,
            borderWidth: 2
          },
          line: {
            borderWidth: 3,
            tension: 0.4 // Interpolação suave
          }
        },
        interaction: {
          intersect: false,
          mode: 'index'
        }
      }
    };

    // Criar e renderizar o gráfico
    const chart = new Chart(ctx, config);

    // Aguardar renderização
    await new Promise(resolve => setTimeout(resolve, 100));

    // Converter para base64
    const imageData = canvas.toDataURL('image/png');

    // Limpar recursos
    chart.destroy();

    return imageData;

  } catch (error) {
    console.error('❌ Erro ao gerar gráfico de volume de ar e pressão:', error);
    throw error;
  }
}
