/**
 * SERVIÇO: GERAÇÃO DE PDF RELATÓRIO MENSAL
 * Gera PDF com layout A4, marca d'água e estrutura conforme especificação
 * Baseado no instrucao.md
 * Autor: Sistema Multi-Agente
 * Data: 2025-01-21
 */

import jsPDF from 'jspdf';
import 'jspdf-autotable';
import logoSemFundo from '../assets/logo_sem_fundo_branco.png';
import { gerarGraficoTemperaturaUmidade, gerarGraficoVolumeArPressao } from './chartService.js';

/**
 * Converte formato numérico americano para brasileiro
 * @param {string|number} value - Valor a ser convertido
 * @returns {string} Valor formatado no padrão brasileiro
 */
function formatarNumericoBrasileiro(value) {
  if (value === null || value === undefined || value === '') {
    return '0,00';
  }

  // Converter para string e depois para número
  const numero = parseFloat(value.toString().replace(',', '.'));

  if (isNaN(numero)) {
    return '0,00';
  }

  // Formatar com 2 casas decimais e trocar ponto por vírgula
  return numero.toFixed(2).replace('.', ',');
}

/**
 * Formata números inteiros sem casas decimais no padrão brasileiro
 * @param {string|number} value - Valor a ser convertido
 * @returns {string} Valor formatado como inteiro no padrão brasileiro
 */
function formatarInteiroSemDecimal(value) {
  if (value === null || value === undefined || value === '') {
    return '0';
  }

  // Converter para string e depois para número inteiro
  const numero = parseInt(value.toString().replace(',', '.'));

  if (isNaN(numero)) {
    return '0';
  }

  // Formatar como inteiro com separadores de milhares no padrão brasileiro
  return numero.toLocaleString('pt-BR');
}

/**
 * Aplica formatação padrão para títulos de seção
 * @param {jsPDF} doc - Documento PDF
 * @param {string} titulo - Texto do título
 * @param {number} x - Posição X
 * @param {number} y - Posição Y
 */
function aplicarTituloSecao(doc, titulo, x, y) {
  doc.setFontSize(PDF_CONFIG.tituloSecao.fontSize);
  doc.setFont(PDF_CONFIG.tituloSecao.font, PDF_CONFIG.tituloSecao.style);
  doc.setTextColor(...CORES.textoTitulo);
  doc.text(titulo, x, y);
}

// Imagem da margem em base64 (completa)
const margemEvolutionImageBase64 = 'data:image/jpeg;base64,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';

// Função para carregar a imagem da margem do arquivo
async function carregarImagemMargem() {
  try {
    console.log('📄 Carregando imagem da margem do arquivo...');

    // Carregar imagem do arquivo público
    const response = await fetch('/margem_evolution.jpg');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.warn('⚠️ Erro ao carregar imagem da margem, usando base64 de fallback:', error);
    return margemEvolutionImageBase64;
  }
}

// Configurações do PDF - Otimizado para A4 com margens adequadas
const PDF_CONFIG = {
  format: 'a4',
  unit: 'mm',
  orientation: 'portrait',
  margins: {
    top: 20,
    right: 20,
    bottom: 15,
    left: 20 // Ajustado para 20mm para acomodar a margem da imagem (10mm) + espaço (10mm)
  },
  pageWidth: 210,
  pageHeight: 297,
  contentWidth: 170, // 210 - 40 (margens esquerda + direita: 20 + 20)
  contentHeight: 262, // 297 - 35 (margens superior + inferior)
  margemImagemWidth: 10, // Largura da margem da imagem
  // Padrão de espaçamento uniforme
  spacing: {
    afterHeader: 35, // Espaço após cabeçalho para início do conteúdo
    beforeFooter: 25, // Espaço antes do rodapé
    betweenSections: 15, // Espaço entre seções
    betweenTables: 20, // Espaço entre tabelas
    titleToTable: 8, // Espaço entre título e tabela
    blockNameToTitle: 10 // Espaço entre nome do bloco e título da tabela
  },
  // Configuração padrão para títulos de seção
  tituloSecao: {
    fontSize: 16,
    font: 'helvetica',
    style: 'bold',
    spacing: 20
  }
};

/**
 * Formata data para DD/MM/YYYY com verificações robustas
 * CORREÇÃO: Usar UTC para evitar problemas de timezone
 * @param {string|Date} dataInput - Data em qualquer formato
 * @returns {string} Data formatada DD/MM/YYYY
 */
function formatarDataDDMMYYYY(dataInput) {
  // CORREÇÃO: Log detalhado para debug
  console.log('🔍 DEBUG formatarDataDDMMYYYY - Input recebido:', { dataInput, tipo: typeof dataInput });

  // CORREÇÃO: Retornar 'Não informado' em vez de data padrão quando não há dados
  if (!dataInput || dataInput === null || dataInput === undefined || dataInput === '') {
    console.warn('Data não informada:', dataInput);
    return 'Não informado';
  }

  try {
    let data;

    // Se já é uma string no formato DD/MM/YYYY, retorna como está
    if (typeof dataInput === 'string' && /^\d{2}\/\d{2}\/\d{4}$/.test(dataInput)) {
      console.log('🔍 DEBUG: Data já no formato DD/MM/YYYY:', dataInput);
      return dataInput;
    }

    // CORREÇÃO: Tratamento simplificado e mais robusto
    // Converter qualquer input para Date object
    data = new Date(dataInput);

    // Verificar se a data é válida
    if (isNaN(data.getTime())) {
      console.warn('Data inválida após conversão:', { dataInput, data });
      return 'Não informado';
    }

    // CORREÇÃO: Usar getDate(), getMonth(), getFullYear() em vez de UTC
    // para manter consistência com o resto do sistema
    const dia = data.getDate().toString().padStart(2, '0');
    const mes = (data.getMonth() + 1).toString().padStart(2, '0');
    const ano = data.getFullYear();

    console.log('🔍 DEBUG: Valores extraídos:', { dia, mes, ano });

    // Verificar se os valores são válidos
    if (isNaN(parseInt(dia)) || isNaN(parseInt(mes)) || isNaN(parseInt(ano))) {
      console.warn('Valores de data inválidos:', { dia, mes, ano, original: dataInput });
      return 'Não informado';
    }

    const resultado = `${dia}/${mes}/${ano}`;
    console.log('🔍 DEBUG: Data formatada final:', resultado);
    return resultado;
  } catch (error) {
    console.warn('Erro ao formatar data:', dataInput, error);
    return 'Não informado';
  }
}

/**
 * Formatar data para DD/MM/YYYY com offset de dias
 * @param {string|Date} dataInput - Data de entrada
 * @param {number} offsetDias - Número de dias para adicionar (+) ou subtrair (-)
 * @returns {string} Data formatada DD/MM/YYYY
 */
function formatarDataDDMMYYYYComOffset(dataInput, offsetDias = 0) {
  console.log('🔍 DEBUG formatarDataDDMMYYYYComOffset - Input:', { dataInput, offsetDias, tipo: typeof dataInput });

  if (!dataInput || dataInput === null || dataInput === undefined || dataInput === '') {
    console.warn('Data não informada para offset:', dataInput);
    return 'Não informado';
  }

  try {
    // Converter para Date object
    let data = new Date(dataInput);

    // Verificar se a data é válida
    if (isNaN(data.getTime())) {
      console.warn('Data inválida para offset:', { dataInput, data });
      return 'Não informado';
    }

    // Aplicar offset de dias
    data.setDate(data.getDate() + offsetDias);
    console.log('🔍 DEBUG: Data após offset:', { original: dataInput, offset: offsetDias, resultado: data });

    // Formatar para DD/MM/YYYY
    const dia = data.getDate().toString().padStart(2, '0');
    const mes = (data.getMonth() + 1).toString().padStart(2, '0');
    const ano = data.getFullYear();

    const resultado = `${dia}/${mes}/${ano}`;
    console.log('🔍 DEBUG: Data formatada com offset:', resultado);
    return resultado;
  } catch (error) {
    console.warn('Erro ao formatar data com offset:', dataInput, error);
    return 'Não informado';
  }
}

// Paleta de Cores baseada no resumo_relatorio.md
const CORES = {
  // Cor Principal (Azul)
  principal: [25, 118, 210],

  // Cores de Texto
  textoTitulo: [25, 118, 210],    // Azul para títulos
  textoNormal: [0, 0, 0],         // Preto para texto normal
  textoSecundario: [100, 100, 100], // Cinza para cabeçalho
  textoRodape: [150, 150, 150],   // Cinza claro para rodapé

  // Cores de Fundo
  fundoCabecalho: [25, 118, 210], // Azul para cabeçalho de tabelas
  fundoZebra: [249, 249, 249],    // Cinza claro para linhas alternadas
  fundoBranco: [255, 255, 255],   // Branco para células normais
  fundoCards: [240, 248, 255],    // Azul claro para cards

  // Cores de Borda
  bordaPrincipal: [25, 118, 210], // Azul para linhas principais
  bordaTabela: [221, 221, 221]    // Cinza para bordas de tabelas
};

// Meses para formatação
const MESES = [
  '', 'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
  'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
];

/**
 * Adiciona imagem da margem esquerda em todas as páginas
 * @param {jsPDF} doc - Documento PDF
 * @param {string} imagemMargem - Imagem da margem em base64
 */
function adicionarImagemMargem(doc, imagemMargem) {
  if (!imagemMargem) {
    console.warn('⚠️ Imagem da margem não disponível');
    return;
  }

  const totalPages = doc.internal.getNumberOfPages();
  console.log('📄 Adicionando imagem da margem em', totalPages, 'páginas');

  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    console.log('📄 Processando página', i, 'de', totalPages);

    try {
      // Adicionar imagem da margem ocupando toda a folha A4
      // Posição: do início (0,0) até o final da página
      // Largura: 10mm conforme especificado
      // Altura: 297mm (altura total A4)
      doc.addImage(
        imagemMargem,
        'JPEG',
        0, // Posição X: início absoluto da página
        0, // Posição Y: início absoluto da página
        PDF_CONFIG.margemImagemWidth, // Largura: 10mm (dentro do tamanho real A4)
        PDF_CONFIG.pageHeight, // Altura: 297mm (altura total A4)
        undefined,
        'NONE'
      );
      console.log('✅ Imagem da margem adicionada na página', i);
    } catch (error) {
      console.warn('⚠️ Erro ao adicionar imagem da margem na página', i, ':', error);
    }
  }
  console.log('✅ Processo de adição da imagem da margem concluído');
}

/**
 * Adiciona marca d'água em todas as páginas
 * @param {jsPDF} doc - Documento PDF
 */
function adicionarMarcaDagua(doc) {
  const totalPages = doc.internal.getNumberOfPages();

  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    // Adicionar logo como marca d'água transparente no fundo
    try {
      // Configurar transparência usando setGState
      doc.setGState(new doc.GState({opacity: 0.15})); // 15% de opacidade

      // Ajustar marca d'água para proporções corretas conforme logo original
      // Logo original: 1024x1024px, proporção 1:1 (quadrada)
      // Tamanho otimizado para marca d'água: 100x100mm
      const logoWidth = 100; // Largura mantendo proporção quadrada
      const logoHeight = 100; // Altura mantendo proporção quadrada (1:1)

      // Posicionar ligeiramente acima do centro para melhor distribuição visual
      const posX = PDF_CONFIG.pageWidth / 2 - (logoWidth / 2); // Centralizar horizontalmente
      const posY = (PDF_CONFIG.pageHeight / 2 - (logoHeight / 2)) - 20; // Ligeiramente acima do centro

      doc.addImage(
        logoSemFundo,
        'PNG',
        posX,
        posY,
        logoWidth, // Largura respeitando escala original
        logoHeight, // Altura respeitando escala original
        undefined,
        'NONE'
      );

      // Restaurar opacidade normal para o resto do conteúdo
      doc.setGState(new doc.GState({opacity: 1.0}));
    } catch (error) {
      console.warn('Erro ao adicionar marca d\'água:', error);
    }
  }
}

/**
 * Adiciona cabeçalho e rodapé em todas as páginas
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 */
/**
 * Adiciona cabeçalho em formato tabela para páginas 2+
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 * @param {number} pageNumber - Número da página
 */
function adicionarCabecalhoTabela(doc, relatorioData, pageNumber) {
  // Só adicionar cabeçalho a partir da segunda página
  if (pageNumber < 2) return;

  const { produto, parametros } = relatorioData;
  const mesNome = MESES[parametros.mes];
  const mesFormatado = parametros.mes.toString().padStart(2, '0');

  // Cabeçalho em linha única (estilo rodapé)
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(...CORES.textoRodape);

  // Linha inferior do cabeçalho
  doc.setDrawColor(...CORES.bordaTabela);
  doc.setLineWidth(0.3);
  doc.line(PDF_CONFIG.margins.left, 25, PDF_CONFIG.pageWidth - PDF_CONFIG.margins.right, 25);

  // Produto e período
  doc.text(
    produto.denominacao,
    PDF_CONFIG.margins.left,
    20
  );

  doc.text(
    `Mês de Referência: ${mesFormatado}/${parametros.ano}`,
    PDF_CONFIG.pageWidth - PDF_CONFIG.margins.right - 60,
    20
  );
}

function adicionarCabecalhoRodape(doc, relatorioData) {
  const totalPages = doc.internal.getNumberOfPages();
  const agora = new Date();
  const dataHoraEmissao = `${agora.toLocaleDateString('pt-BR')} às ${agora.toLocaleTimeString('pt-BR')}`;

  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);

    // Adicionar cabeçalho em formato tabela (páginas 2+)
    adicionarCabecalhoTabela(doc, relatorioData, i);

    // Rodapé com cores padronizadas
    doc.setFontSize(8);
    doc.setTextColor(...CORES.textoRodape);

    // Linha superior do rodapé
    doc.setDrawColor(...CORES.bordaTabela);
    doc.setLineWidth(0.3);
    doc.line(PDF_CONFIG.margins.left, PDF_CONFIG.pageHeight - 15, PDF_CONFIG.pageWidth - PDF_CONFIG.margins.right, PDF_CONFIG.pageHeight - 15);

    // Página e data/hora
    doc.text(
      `Página ${i} de ${totalPages}`,
      PDF_CONFIG.margins.left,
      PDF_CONFIG.pageHeight - 8
    );

    doc.text(
      `Emitido em: ${dataHoraEmissao}`,
      PDF_CONFIG.pageWidth - PDF_CONFIG.margins.right - 50,
      PDF_CONFIG.pageHeight - 8
    );
  }
}

/**
 * Adiciona página inicial do relatório
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 */
function adicionarPaginaInicial(doc, relatorioData) {
  console.log('📄 [VERSÃO 2025-07-29-16:25] Adicionando página inicial do relatório...');
  console.log('📄 Dados recebidos:', relatorioData);

  const { cliente, produto, parametros } = relatorioData;
  const mesNome = MESES[parametros.mes];

  // Centralizar conteúdo na página
  const centerX = PDF_CONFIG.pageWidth / 2;
  const centerY = PDF_CONFIG.pageHeight / 2;

  console.log('📄 Configurações da página inicial:', {
    centerX,
    centerY,
    produto: produto.denominacao,
    periodo: `${mesNome}/${parametros.ano}`
  });

  // Título principal - RELATÓRIO DE MONITORAMENTO
  doc.setFontSize(28);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(...CORES.textoTitulo);
  const tituloWidth = doc.getTextWidth('RELATÓRIO DE MONITORAMENTO');
  const tituloY = centerY - 40;
  doc.text('RELATÓRIO DE MONITORAMENTO', centerX - (tituloWidth / 2), tituloY);
  console.log('📄 Título "RELATÓRIO DE MONITORAMENTO" adicionado na posição:', centerX - (tituloWidth / 2), tituloY);

  // Nome do Produto completo
  doc.setFontSize(18);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(...CORES.textoNormal);
  const produtoWidth = doc.getTextWidth(produto.denominacao);
  const produtoY = centerY - 15;
  doc.text(produto.denominacao, centerX - (produtoWidth / 2), produtoY);
  console.log('📄 Nome do produto adicionado:', produto.denominacao, 'na posição:', centerX - (produtoWidth / 2), produtoY);

  // Referente à + mês e ano
  doc.setFontSize(16);
  doc.setFont('helvetica', 'normal');
  const referenteTexto = `Referente à ${mesNome}/${parametros.ano}`;
  const referenteWidth = doc.getTextWidth(referenteTexto);
  const referenteY = centerY + 10;
  doc.text(referenteTexto, centerX - (referenteWidth / 2), referenteY);
  console.log('📄 Período adicionado:', referenteTexto, 'na posição:', centerX - (referenteWidth / 2), referenteY);

  console.log('✅ Página inicial adicionada com sucesso');
  console.log('📄 Total de páginas antes de addPage:', doc.internal.getNumberOfPages());

  // Adicionar nova página para o conteúdo
  doc.addPage();
  console.log('✅ Nova página criada para o conteúdo');
  console.log('📄 Total de páginas após addPage:', doc.internal.getNumberOfPages());
}

/**
 * Adiciona título da segunda página do relatório
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 * @returns {number} Posição Y após o título
 */
function adicionarTituloSegundaPagina(doc, relatorioData) {
  const { cliente, produto, parametros } = relatorioData;
  const mesNome = MESES[parametros.mes];

  console.log('📄 Adicionando título da segunda página...');

  // Título principal com cor azul
  doc.setFontSize(20);
  doc.setTextColor(...CORES.textoTitulo);
  doc.setFont('helvetica', 'bold');

  const titulo = `Relatório Mensal de Monitoramento – ${cliente.razao_social}`;
  const subtitulo = `${produto.denominacao}`;
  const periodo = `Referente a: ${mesNome}/${parametros.ano}`;

  // Centralizar título
  const tituloWidth = doc.getTextWidth(titulo);
  const tituloX = (PDF_CONFIG.pageWidth - tituloWidth) / 2;

  doc.text(titulo, tituloX, 30);

  // Subtítulo
  doc.setFontSize(16);
  doc.setFont('helvetica', 'normal');
  const subtituloWidth = doc.getTextWidth(subtitulo);
  const subtituloX = (PDF_CONFIG.pageWidth - subtituloWidth) / 2;
  doc.text(subtitulo, subtituloX, 45);

  // Período
  doc.setFontSize(12);
  const periodoWidth = doc.getTextWidth(periodo);
  const periodoX = (PDF_CONFIG.pageWidth - periodoWidth) / 2;
  doc.text(periodo, periodoX, 55);

  console.log('✅ Título da segunda página adicionado');
  return 65; // Retorna posição Y após o título
}

/**
 * Adiciona título principal do relatório (FUNÇÃO ORIGINAL - MANTIDA PARA COMPATIBILIDADE)
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 */
function adicionarTitulo(doc, relatorioData) {
  const { cliente, produto, parametros } = relatorioData;
  const mesNome = MESES[parametros.mes];

  // Título principal com cor azul
  doc.setFontSize(20);
  doc.setTextColor(...CORES.textoTitulo);
  doc.setFont('helvetica', 'bold');

  const titulo = `Relatório Mensal de Monitoramento – ${cliente.razao_social}`;
  const subtitulo = `${produto.denominacao}`;
  const periodo = `Referente a: ${mesNome}/${parametros.ano}`;

  // Centralizar título
  const tituloWidth = doc.getTextWidth(titulo);
  const tituloX = (PDF_CONFIG.pageWidth - tituloWidth) / 2;

  doc.text(titulo, tituloX, 35);

  // Subtítulo
  doc.setFontSize(16);
  const subtituloWidth = doc.getTextWidth(subtitulo);
  const subtituloX = (PDF_CONFIG.pageWidth - subtituloWidth) / 2;
  doc.text(subtitulo, subtituloX, 45);

  // Período
  doc.setFontSize(12);
  doc.setTextColor(...CORES.textoNormal);
  doc.setFont('helvetica', 'normal');
  const periodoWidth = doc.getTextWidth(periodo);
  const periodoX = (PDF_CONFIG.pageWidth - periodoWidth) / 2;
  doc.text(periodo, periodoX, 55);

  // Linha separadora principal
  doc.setDrawColor(...CORES.bordaPrincipal);
  doc.setLineWidth(1);
  doc.line(PDF_CONFIG.margins.left, 65, PDF_CONFIG.pageWidth - PDF_CONFIG.margins.right, 65);

  return 75; // Retorna posição Y para próximo conteúdo
}

/**
 * Adiciona informações do cliente
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 * @param {number} startY - Posição Y inicial
 */
function adicionarInformacoesCliente(doc, relatorioData, startY) {
  const { cliente, produto, parametros, dadosAdicionais } = relatorioData;

  // Título da seção
  aplicarTituloSecao(doc, 'Informações do Cliente', PDF_CONFIG.margins.left, startY);

  const informacoes = [
    ['Razão Social', cliente.razao_social],
    ['CNPJ', cliente.cnpj],
    ['Endereço', `${cliente.logradouro || ''}, ${cliente.numero || ''} - ${cliente.cidade || ''} - ${cliente.estado || ''}`],
    ['Estação/Produto', produto.denominacao],
    ['Plano de Cobertura do Sistema', dadosAdicionais?.planoCobertura || 'Não informado'],
    ['Período', `01/${parametros.mes.toString().padStart(2, '0')}/${parametros.ano} a ${new Date(parametros.ano, parametros.mes, 0).getDate()}/${parametros.mes.toString().padStart(2, '0')}/${parametros.ano}`]
  ];

  doc.autoTable({
    startY: startY + 10,
    body: informacoes,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 3,
      textColor: CORES.textoNormal
    },
    columnStyles: {
      0: {
        fontStyle: 'bold',
        fillColor: CORES.fundoZebra,
        cellWidth: 60
      },
      1: {
        fillColor: CORES.fundoBranco,
        cellWidth: 110
      }
    },
    tableWidth: PDF_CONFIG.contentWidth,
    margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
  });

  return doc.lastAutoTable.finalY + PDF_CONFIG.spacing.betweenSections;
}

/**
 * Adiciona resumo executivo unificado
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 * @param {number} startY - Posição Y inicial
 */
function adicionarResumoExecutivo(doc, relatorioData, startY) {
  const { dadosUniversais, valoresMistos, parametros, dadosAdicionais } = relatorioData;

  // Título da seção
  aplicarTituloSecao(doc, 'Resumo Executivo', PDF_CONFIG.margins.left, startY);

  // Combinar todos os dados em uma única tabela
  const resumoExecutivoTable = [
    ['Consumo Elétrico Total (kWh)', formatarNumericoBrasileiro(dadosUniversais.consumoEletricoTotal || '0.00')],
    ['Custo Médio da Energia kWh (R$/kWh)', `R$ ${formatarNumericoBrasileiro(parametros.custoKwh || 0)}`],
    ['Custo de Operação (R$)', `R$ ${formatarNumericoBrasileiro(dadosUniversais.custoOperacao || '0.00')}`],
    ['Volume de Ar Total realizado Troca Gasosa (m³)', formatarNumericoBrasileiro(valoresMistos.volumeArTotal || '0.00')],
    ['Fluxo Médio de Ar (m³/h)', formatarNumericoBrasileiro(valoresMistos.fluxoMedioAr || '0.00')],
    ['Temperatura Média do Sistema (ºC)', formatarNumericoBrasileiro(valoresMistos.temperaturaMediaSistema || '0.00')],
    ['Umidade Relativa Média do Sistema (%)', formatarNumericoBrasileiro(valoresMistos.umidadeRelativaMediaSistema || '0.00')],
    ['Pressão Média Obtida de Estanqueidade (mmH2O)', formatarNumericoBrasileiro(valoresMistos.pressaoMediaObtidaEstanqueidade || '0.00')],
    ['Total de Espaço para Gavetas no Sistema', formatarInteiroSemDecimal(dadosAdicionais?.totalGavetas || '0')],
    ['Total de Sepultamentos Realizados no Mês', formatarInteiroSemDecimal(dadosAdicionais?.totalSepultamentosMes || '0')],
    ['Total de Exumações no Mês', formatarInteiroSemDecimal(dadosAdicionais?.totalExumacoesMes || '0')],
    ['Total de Atendimentos no Período', formatarInteiroSemDecimal(dadosAdicionais?.totalAtendimentos || '0')]
  ];

  doc.autoTable({
    startY: startY + 10,
    body: resumoExecutivoTable,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 3,
      textColor: CORES.textoNormal
    },
    columnStyles: {
      0: {
        fontStyle: 'bold',
        fillColor: CORES.fundoCards,
        cellWidth: 120
      },
      1: {
        fillColor: CORES.fundoBranco,
        cellWidth: 50
      }
    },
    tableWidth: PDF_CONFIG.contentWidth,
    margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
  });

  return doc.lastAutoTable.finalY + PDF_CONFIG.spacing.betweenSections;
}

// Função removida - Dados Operacionais Diários não serão mais exibidos no PDF

/**
 * Adiciona página de gráficos para um bloco específico
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} bloco - Dados do bloco
 * @param {Object} indicadores - Indicadores calculados do bloco
 * @param {Object} parametros - Parâmetros do relatório
 */
async function adicionarPaginaGraficos(doc, bloco, indicadores, parametros) {
  console.log('📊 Adicionando página de gráficos para bloco:', bloco.denominacao);

  // Nova página para gráficos
  doc.addPage();
  let currentY = PDF_CONFIG.spacing.afterHeader;

  // Título da página
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(...CORES.textoTitulo);
  doc.text('Resumo Gráfico de Operação', PDF_CONFIG.margins.left, currentY);

  // Nome do bloco
  currentY += 15;
  doc.setFontSize(14);
  doc.text(`Bloco: ${bloco.denominacao || bloco.nome_bloco || bloco.codigo_bloco}`, PDF_CONFIG.margins.left, currentY);

  currentY += 25;

  // GRÁFICO 1: Temperatura e Umidade
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Gráfico da Temperatura e Umidade no Período', PDF_CONFIG.margins.left, currentY);

  currentY += 15;

  try {
    // Gerar gráfico real usando Chart.js
    if (indicadores.dadosGraficos && indicadores.dadosGraficos.labels && indicadores.dadosGraficos.labels.length > 0) {
      console.log('📊 Gerando gráfico de temperatura e umidade...');

      // Preparar dados para Chart.js com labels simplificados (apenas datas)
      const dadosGraficoChart = {
        labels: indicadores.dadosGraficos.labels.map(label => {
          // Extrair apenas a data e período: "01/07 M", "01/07 T", "01/07 N"
          const dataMatch = label.match(/(\d{2}\/\d{2})/);
          const periodo = label.includes('Manhã') ? 'M' : label.includes('Tarde') ? 'T' : 'N';
          return dataMatch ? `${dataMatch[1]} ${periodo}` : label;
        }),
        temperatura: indicadores.dadosGraficos.temperatura,
        umidade: indicadores.dadosGraficos.umidade
      };

      const nomeBloco = bloco.denominacao || bloco.nome_bloco || bloco.codigo_bloco;
      const imagemGrafico1 = await gerarGraficoTemperaturaUmidade(dadosGraficoChart, nomeBloco);

      // Calcular dimensões respeitando margens e rodapé
      const larguraDisponivel = PDF_CONFIG.contentWidth; // 170mm (largura total - margens)
      const alturaGrafico = 70; // Altura reduzida para respeitar rodapé e manter qualidade

      // Adicionar gráfico ao PDF
      doc.addImage(
        imagemGrafico1,
        'PNG',
        PDF_CONFIG.margins.left,
        currentY,
        larguraDisponivel,
        alturaGrafico,
        undefined,
        'FAST'
      );

      currentY += alturaGrafico + 20;
      console.log('✅ Gráfico de temperatura e umidade adicionado');

    } else {
      // Fallback se não houver dados
      doc.setFontSize(10);
      doc.text('⚠️ Dados de temperatura e umidade não disponíveis', PDF_CONFIG.margins.left, currentY);
      currentY += 20;
    }
  } catch (error) {
    console.error('❌ Erro ao gerar gráfico de temperatura e umidade:', error);
    doc.setFontSize(10);
    doc.text('❌ Erro ao gerar gráfico de temperatura e umidade', PDF_CONFIG.margins.left, currentY);
    currentY += 20;
  }

  // GRÁFICO 2: Volume de Ar e Pressão de Estanque
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Gráfico do Volume Total de Ar e Pressão de Estanque no Período', PDF_CONFIG.margins.left, currentY);

  currentY += 15;

  try {
    // Preparar dados para gráfico de volume de ar e pressão
    const dadosVolumeArPressao = [];

    if (indicadores.dadosTrocasGasosas && indicadores.dadosEstanqueidade) {
      console.log('🔍 DEBUG - Dados de trocas gasosas:', indicadores.dadosTrocasGasosas);
      console.log('🔍 DEBUG - Dados de estanqueidade:', indicadores.dadosEstanqueidade);

      // Combinar dados por data
      indicadores.dadosTrocasGasosas.forEach(trocas => {
        const estanqueidade = indicadores.dadosEstanqueidade.find(est => est.data === trocas.data);

        const pressaoValor = estanqueidade ? estanqueidade.pressaoObtidaMaxima || 0 : 0;
        console.log(`🔍 DEBUG - Data: ${trocas.data}, Volume: ${trocas.volumeArTotal}, Pressão: ${pressaoValor}`);

        dadosVolumeArPressao.push({
          data: formatarDataDDMMYYYY(trocas.data),
          volumeAr: trocas.volumeArTotal || 0,
          pressaoEstanque: pressaoValor
        });
      });

      console.log('🔍 DEBUG - Dados finais para gráfico:', dadosVolumeArPressao);
    }

    // Gerar gráfico real usando Chart.js
    if (dadosVolumeArPressao.length > 0) {
      console.log('📊 Gerando gráfico de volume de ar e pressão...');

      const nomeBloco = bloco.denominacao || bloco.nome_bloco || bloco.codigo_bloco;
      const imagemGrafico2 = await gerarGraficoVolumeArPressao(dadosVolumeArPressao, nomeBloco);

      // Calcular dimensões respeitando margens e rodapé
      const larguraDisponivel = PDF_CONFIG.contentWidth; // 170mm (largura total - margens)
      const alturaGrafico = 70; // Altura reduzida para respeitar rodapé e manter qualidade

      // Adicionar gráfico ao PDF
      doc.addImage(
        imagemGrafico2,
        'PNG',
        PDF_CONFIG.margins.left,
        currentY,
        larguraDisponivel,
        alturaGrafico,
        undefined,
        'FAST'
      );

      console.log('✅ Gráfico de volume de ar e pressão adicionado');

    } else {
      // Fallback se não houver dados
      doc.setFontSize(10);
      doc.text('⚠️ Dados de volume de ar e pressão não disponíveis', PDF_CONFIG.margins.left, currentY);
    }
  } catch (error) {
    console.error('❌ Erro ao gerar gráfico de volume de ar e pressão:', error);
    doc.setFontSize(10);
    doc.text('❌ Erro ao gerar gráfico de volume de ar e pressão', PDF_CONFIG.margins.left, currentY);
  }

  console.log('✅ Página de gráficos adicionada para bloco:', bloco.denominacao);
}

/**
 * Adiciona páginas individuais por bloco com gráficos
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 */
async function adicionarPaginasPorBloco(doc, relatorioData) {
  const { blocos, dadosPorBloco, indicadoresPorBloco, cliente, produto, parametros } = relatorioData;
  const mesNome = MESES[parametros.mes];

  for (const bloco of blocos) {
    const codigoBloco = bloco.codigo_bloco;
    const dadosBloco = dadosPorBloco[codigoBloco] || [];
    const indicadores = indicadoresPorBloco[codigoBloco] || {};

    // 1. PÁGINA DE GRÁFICOS (nova página)
    await adicionarPaginaGraficos(doc, bloco, indicadores, parametros);

    // 2. PÁGINA DE TABELAS (nova página)
    doc.addPage();

    let currentY = PDF_CONFIG.spacing.afterHeader;

    // 3. Tabela de Dados de Temperatura e Umidade registrados para o Bloco
    if (indicadores.tabelaDados && indicadores.tabelaDados.length > 0) {
      // Nome do bloco
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text(bloco.denominacao || bloco.nome_bloco || codigoBloco, PDF_CONFIG.margins.left, currentY);

      // Título da tabela
      currentY += PDF_CONFIG.spacing.blockNameToTitle;
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text('Temperatura e Umidade registrados para o Bloco', PDF_CONFIG.margins.left, currentY);

      const tabelaData = indicadores.tabelaDados.map(row => [
        formatarDataDDMMYYYY(row.data), // Formatação DD/MM/YYYY
        `${row.tempManha || '0'}°C`,
        `${row.tempTarde || '0'}°C`,
        `${row.tempNoite || '0'}°C`,
        `${row.umidManha || '0'}%`,
        `${row.umidTarde || '0'}%`,
        `${row.umidNoite || '0'}%`
      ]);

      doc.autoTable({
        startY: currentY + PDF_CONFIG.spacing.titleToTable,
        head: [['Data', 'Temp Manhã', 'Temp Tarde', 'Temp Noite', 'Umid Manhã', 'Umid Tarde', 'Umid Noite']],
        body: tabelaData,
        theme: 'grid',
        styles: {
          fontSize: 6, // Reduzido de 8 para 6 (25% menor)
          cellPadding: 1.5, // Reduzido de 2 para 1.5
          textColor: CORES.textoNormal
        },
        headStyles: {
          fillColor: CORES.fundoCabecalho,
          textColor: CORES.fundoBranco,
          fontStyle: 'bold',
          fontSize: 6 // Reduzido para manter consistência
        },
        columnStyles: {
          0: { cellWidth: 24 },
          1: { cellWidth: 24 },
          2: { cellWidth: 24 },
          3: { cellWidth: 24 },
          4: { cellWidth: 24 },
          5: { cellWidth: 24 },
          6: { cellWidth: 26 }
        },
        tableWidth: PDF_CONFIG.contentWidth,
        margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
      });

      currentY = doc.lastAutoTable.finalY + PDF_CONFIG.spacing.betweenTables;
    }

    // 4. Tabela Unificada: Dados de Trocas Gasosas e Estanqueidade no Bloco
    console.log('📄 DEBUG: Verificando condições para tabela unificada...');
    console.log('📄 DEBUG: indicadores.dadosTrocasGasosas existe?', !!indicadores.dadosTrocasGasosas);
    console.log('📄 DEBUG: indicadores.dadosTrocasGasosas.length:', indicadores.dadosTrocasGasosas?.length);
    console.log('📄 DEBUG: indicadores.dadosEstanqueidade existe?', !!indicadores.dadosEstanqueidade);
    console.log('📄 DEBUG: indicadores.dadosEstanqueidade.length:', indicadores.dadosEstanqueidade?.length);

    if ((indicadores.dadosTrocasGasosas && indicadores.dadosTrocasGasosas.length > 0) ||
        (indicadores.dadosEstanqueidade && indicadores.dadosEstanqueidade.length > 0)) {
      // Verificar se precisa de nova página
      if (currentY > 190) {
        doc.addPage();
        currentY = PDF_CONFIG.spacing.afterHeader;
      }

      // Nome do bloco
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text(bloco.denominacao || bloco.nome_bloco || codigoBloco, PDF_CONFIG.margins.left, currentY);

      // Título da tabela unificada
      currentY += PDF_CONFIG.spacing.blockNameToTitle;
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text('Dados de Trocas Gasosas e Estanqueidade no Bloco', PDF_CONFIG.margins.left, currentY);

      console.log('📄 DEBUG: Dados de trocas gasosas:', indicadores.dadosTrocasGasosas);
      console.log('📄 DEBUG: Dados de estanqueidade:', indicadores.dadosEstanqueidade);
      console.log('📄 DEBUG: Estrutura completa dos indicadores:', Object.keys(indicadores));

      // Combinar dados de trocas gasosas e estanqueidade por data
      const dadosUnificados = [];
      const datasProcessadas = new Set();

      // Processar dados de trocas gasosas
      if (indicadores.dadosTrocasGasosas && indicadores.dadosTrocasGasosas.length > 0) {
        console.log('📄 DEBUG: Processando dados de trocas gasosas...');
        indicadores.dadosTrocasGasosas.forEach((trocas, index) => {
          console.log(`📄 DEBUG: Processando troca ${index}:`, trocas);

          const data = trocas.data;
          const potencia = trocas.potenciaMediaPercentual || trocas.potencia_media_percentual || trocas.potenciaMedia || trocas.potencia_media || 0;

          // Buscar dados de estanqueidade correspondentes
          const estanqueidade = indicadores.dadosEstanqueidade?.find(est => est.data === data);

          dadosUnificados.push([
            formatarDataDDMMYYYY(data),
            `${formatarNumericoBrasileiro(trocas.volumeArTotal || 0)} m³`,
            `${formatarNumericoBrasileiro(potencia)} %`,
            estanqueidade ? `${formatarNumericoBrasileiro(estanqueidade.pressaoObtidaMaxima || '0.00')} mmH2O` : '-'
          ]);

          datasProcessadas.add(data);
        });
      } else {
        console.log('📄 DEBUG: Nenhum dado de trocas gasosas encontrado');
      }

      // Processar dados de estanqueidade que não foram incluídos
      if (indicadores.dadosEstanqueidade && indicadores.dadosEstanqueidade.length > 0) {
        console.log('📄 DEBUG: Processando dados de estanqueidade...');
        indicadores.dadosEstanqueidade.forEach((estanqueidade, index) => {
          if (!datasProcessadas.has(estanqueidade.data)) {
            console.log(`📄 DEBUG: Processando estanqueidade ${index}:`, estanqueidade);

            dadosUnificados.push([
              formatarDataDDMMYYYY(estanqueidade.data),
              '-',
              '-',
              `${formatarNumericoBrasileiro(estanqueidade.pressaoObtidaMaxima || '0.00')} mmH2O`
            ]);
          }
        });
      } else {
        console.log('📄 DEBUG: Nenhum dado de estanqueidade encontrado');
      }

      console.log('📄 DEBUG: Total de dados unificados:', dadosUnificados.length);
      console.log('📄 DEBUG: Dados unificados:', dadosUnificados);

      // Se não há dados, criar uma linha de exemplo para mostrar a estrutura
      if (dadosUnificados.length === 0) {
        console.log('📄 DEBUG: Criando linha de exemplo para tabela vazia');
        dadosUnificados.push([
          'Sem dados',
          '-',
          '-',
          '-'
        ]);
      }

      // Ordenar por data (exceto linha de exemplo)
      if (dadosUnificados.length > 1 || (dadosUnificados.length === 1 && dadosUnificados[0][0] !== 'Sem dados')) {
        dadosUnificados.sort((a, b) => {
          if (a[0] === 'Sem dados' || b[0] === 'Sem dados') return 0;
          const dataA = new Date(a[0].split('/').reverse().join('-'));
          const dataB = new Date(b[0].split('/').reverse().join('-'));
          return dataA - dataB;
        });
      }

      doc.autoTable({
        startY: currentY + PDF_CONFIG.spacing.titleToTable,
        head: [['Data', 'Volume Total de Ar', 'Potência Média do Compressor', 'Pressão de Estanque']],
        body: dadosUnificados,
        theme: 'grid',
        styles: {
          fontSize: 7, // Reduzido de 9 para 7 (22% menor)
          cellPadding: 1.5, // Reduzido de 2 para 1.5
          textColor: CORES.textoNormal
        },
        headStyles: {
          fillColor: CORES.fundoCabecalho,
          textColor: CORES.fundoBranco,
          fontStyle: 'bold',
          fontSize: 7 // Reduzido para manter consistência
        },
        columnStyles: {
          0: { cellWidth: 35 },
          1: { cellWidth: 45 },
          2: { cellWidth: 40 },
          3: { cellWidth: 50 }
        },
        margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
      });

      currentY = doc.lastAutoTable.finalY + PDF_CONFIG.spacing.betweenSections;
    }

    // 5. Tabela de Estanqueidade removida - dados agora estão na tabela unificada
    if (false) {
      // Verificar se precisa de nova página
      if (currentY > 190) {
        doc.addPage();
        currentY = PDF_CONFIG.spacing.afterHeader;
      }

      // Nome do bloco
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text(bloco.denominacao || bloco.nome_bloco || codigoBloco, PDF_CONFIG.margins.left, currentY);

      // Título da tabela
      currentY += PDF_CONFIG.spacing.blockNameToTitle;
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text('Testes de Estanqueidade Realizados no Sistema', PDF_CONFIG.margins.left, currentY);

      console.log('📄 DEBUG: Dados de estanqueidade:', indicadores.dadosEstanqueidade);

      // Preparar dados de estanqueidade
      const tabelaEstanqueidade = indicadores.dadosEstanqueidade.map((estanqueidade, index) => {
        console.log(`📄 DEBUG: Processando estanqueidade ${index}:`, estanqueidade);

        // Converter duração de segundos para minutos
        const duracaoSegundos = estanqueidade.duracaoMaxima || estanqueidade.duracao_maxima || 0;
        const duracaoMinutos = duracaoSegundos / 60;
        console.log(`📄 DEBUG: Duração - Segundos: ${duracaoSegundos}, Minutos: ${duracaoMinutos}`);

        return [
          formatarDataDDMMYYYY(estanqueidade.data), // Formatação DD/MM/YYYY
          `${formatarNumericoBrasileiro(duracaoMinutos)} min`,
          `${formatarNumericoBrasileiro(estanqueidade.pressaoObtidaMaxima || '0.00')} mmH2O`
        ];
      });

      doc.autoTable({
        startY: currentY + PDF_CONFIG.spacing.titleToTable,
        head: [['Data', 'Duração Máxima', 'Pressão Obtida Máxima']],
        body: tabelaEstanqueidade,
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 2,
          textColor: CORES.textoNormal
        },
        headStyles: {
          fillColor: CORES.fundoCabecalho,
          textColor: CORES.fundoBranco,
          fontStyle: 'bold'
        },
        columnStyles: {
          0: { cellWidth: 50 },
          1: { cellWidth: 60 },
          2: { cellWidth: 60 }
        },
        tableWidth: PDF_CONFIG.contentWidth,
        margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
      });
    }
  }
}

/**
 * Adiciona página de atendimentos no período
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 */
function adicionarPaginaAtendimentos(doc, relatorioData) {
  console.log('📋 Adicionando página de atendimentos...');

  const { dadosAdicionais, parametros } = relatorioData;
  const listaAtendimentos = dadosAdicionais?.listaAtendimentos || [];

  // Debug: verificar dados dos atendimentos
  console.log('🔍 Debug - Total de atendimentos:', listaAtendimentos.length);
  if (listaAtendimentos.length > 0) {
    console.log('🔍 Debug - Primeiro atendimento:', listaAtendimentos[0]);
  }

  // Nova página para atendimentos
  doc.addPage();
  let currentY = PDF_CONFIG.spacing.afterHeader;

  // Título da seção
  aplicarTituloSecao(doc, 'Atendimentos no Período', PDF_CONFIG.margins.left, currentY);

  currentY += 20;

  // Verificar se há atendimentos
  if (listaAtendimentos.length === 0) {
    // Mensagem quando não há atendimentos
    const dataInicio = `01/${parametros.mes.toString().padStart(2, '0')}/${parametros.ano}`;
    const ultimoDiaDoMes = new Date(parametros.ano, parametros.mes, 0).getDate();
    const dataFim = `${ultimoDiaDoMes}/${parametros.mes.toString().padStart(2, '0')}/${parametros.ano}`;

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(...CORES.textoNormal);
    doc.text(
      `Não houve nenhum atendimento para este Produto no período ${dataInicio} a ${dataFim}`,
      PDF_CONFIG.margins.left,
      currentY
    );
  } else {
    // Sistema de layout otimizado
    let currentY = PDF_CONFIG.spacing.afterHeader;

    listaAtendimentos.forEach((atendimento) => {

      // Preparar dados de descrição para calcular altura necessária
      let descricaoCompleta = '';



      // Adicionar descrição do atendimento
      if (atendimento.descricao_atendimento && atendimento.descricao_atendimento !== '-') {
        descricaoCompleta += `Descrição - ${atendimento.descricao_atendimento}`;
        console.log(`✅ Descrição adicionada: "${atendimento.descricao_atendimento}"`);
      }

      // Adicionar solução (sempre tentar incluir se existir)
      if (atendimento.solucao && atendimento.solucao !== '-' && atendimento.solucao.trim() !== '') {
        if (descricaoCompleta) descricaoCompleta += '\n\n';
        descricaoCompleta += `Solução - ${atendimento.solucao}`;
        console.log(`✅ Solução adicionada: "${atendimento.solucao}"`);
      } else {
        console.log(`❌ Solução NÃO adicionada. Valor:`, atendimento.solucao);
      }

      // Se não há descrição nem solução, mostrar "Não informado"
      if (!descricaoCompleta) descricaoCompleta = 'Não informado';



      // Configurações do card otimizadas
      const cardMargin = PDF_CONFIG.margins.left;
      const cardWidth = PDF_CONFIG.contentWidth;
      const cardPadding = 5;
      const descricaoWidth = cardWidth - (cardPadding * 2);

      // Calcular altura necessária para a descrição
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      const linhasDescricao = doc.splitTextToSize(descricaoCompleta, descricaoWidth);
      const alturaDescricao = linhasDescricao.length * 5;



      // Altura dinâmica do card baseada no conteúdo real
      const cardBaseHeight = 58; // Altura base ajustada para novo espaçamento
      const cardHeight = cardBaseHeight + alturaDescricao + 5; // Pequena margem de segurança



      // Verificar se precisa de nova página
      if (currentY + cardHeight > 270) { // Aumentado limite para melhor aproveitamento
        doc.addPage();
        currentY = PDF_CONFIG.spacing.afterHeader;

        // Repetir título da seção na nova página
        aplicarTituloSecao(doc, 'Atendimentos no Período (continuação)', PDF_CONFIG.margins.left, currentY);
        currentY += PDF_CONFIG.tituloSecao.spacing;
      }

      // Desenhar fundo do card
      doc.setFillColor(248, 249, 250); // Cinza muito claro
      doc.rect(cardMargin, currentY - 5, cardWidth, cardHeight, 'F');

      // Desenhar borda do card
      doc.setDrawColor(100, 150, 200); // Azul suave
      doc.setLineWidth(1);
      doc.rect(cardMargin, currentY - 5, cardWidth, cardHeight);

      // Conteúdo do card
      let cardY = currentY + 2;

      // PROTOCOLO DE ATENDIMENTO centralizado e destacado
      const protocoloTexto = `PROTOCOLO DE ATENDIMENTO: ${atendimento.numero_chamado || '-'}`;
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 80, 160); // Azul escuro para máximo destaque

      // Centralizar o protocolo dentro do card
      const protocoloWidth = doc.getTextWidth(protocoloTexto);
      const protocoloX = cardMargin + (cardWidth - protocoloWidth) / 2;
      doc.text(protocoloTexto, protocoloX, cardY + 8);

      // Linha separadora abaixo do protocolo
      doc.setDrawColor(100, 150, 200);
      doc.setLineWidth(0.5);
      doc.line(cardMargin + 10, cardY + 12, cardMargin + cardWidth - 10, cardY + 12);

      cardY += 18; // Espaçamento aumentado para melhor separação visual

      // Linha 1: Status e Técnico Responsável
      doc.setFontSize(11);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text('Status:', cardMargin + cardPadding, cardY);

      doc.setFont('helvetica', 'normal');
      doc.setTextColor(0, 120, 0); // Verde para status
      const statusWidth = doc.getTextWidth('Status: ');
      const statusTexto = atendimento.status_atendimento || '-';
      doc.text(statusTexto, cardMargin + cardPadding + statusWidth, cardY);

      // Técnico na mesma linha, à direita (com verificação de espaço)
      const tecnicoLabel = 'Técnico Responsável:';
      const tecnicoTexto = atendimento.tecnico_responsavel || '-';
      const tecnicoX = cardMargin + cardWidth / 2;

      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text(tecnicoLabel, tecnicoX, cardY);

      doc.setFont('helvetica', 'normal');
      doc.setTextColor(...CORES.textoNormal);
      const tecnicoLabelWidth = doc.getTextWidth(tecnicoLabel);

      // Truncar texto do técnico se necessário
      const espacoDisponivel = cardWidth - (tecnicoX - cardMargin) - tecnicoLabelWidth - cardPadding - 3;
      const tecnicoTruncado = doc.splitTextToSize(tecnicoTexto, espacoDisponivel)[0] || tecnicoTexto;
      doc.text(tecnicoTruncado, tecnicoX + tecnicoLabelWidth + 3, cardY);

      cardY += 12; // Espaçamento consistente

      // Linha 2: Apenas Data de Fechamento
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      const dataFechamento = formatarDataDDMMYYYY(atendimento.data_fechamento) || '-';

      doc.text('Data Fechamento:', cardMargin + cardPadding, cardY);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(...CORES.textoNormal);
      const fechamentoWidth = doc.getTextWidth('Data Fechamento: ');
      doc.text(dataFechamento, cardMargin + cardPadding + fechamentoWidth + 3, cardY); // +3px para evitar sobreposição

      cardY += 12; // Espaçamento consistente com outros elementos

      // Linha 3: Descrição completa (dentro do card)
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(...CORES.textoTitulo);
      doc.text('Descrição Completa:', cardMargin + cardPadding, cardY);

      cardY += 6; // Espaço reduzido antes da descrição

      // Renderizar descrição dentro dos limites do card
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(...CORES.textoNormal);

      // Garantir que a descrição fique dentro do card
      const maxY = currentY - 5 + cardHeight - 3; // Limite otimizado
      let linhaAtual = 0;



      for (let linha of linhasDescricao) {
        const yPosition = cardY + (linhaAtual * 5);
        if (yPosition >= maxY) {
          break; // Parar se ultrapassar o card
        }

        doc.text(linha, cardMargin + cardPadding, yPosition);
        linhaAtual++;
      }

      // Atualizar posição Y para próximo card
      currentY += cardHeight + 8; // Espaço otimizado entre cards
    });
  }


}

/**
 * Adiciona página conclusiva do relatório
 * @param {jsPDF} doc - Documento PDF
 * @param {Object} relatorioData - Dados do relatório
 */
function adicionarPaginaConclusiva(doc, relatorioData) {
  console.log('📄 Adicionando página conclusiva...');

  const { dadosAdicionais, produto, parametros } = relatorioData;
  const dadosManutencao = dadosAdicionais?.dadosManutencao || {};

  // Nova página para conclusão
  doc.addPage();
  let currentY = PDF_CONFIG.spacing.afterHeader;

  // Título da seção
  aplicarTituloSecao(doc, 'Conclusão do Relatório', PDF_CONFIG.margins.left, currentY);

  currentY += 25;

  // Texto conclusivo
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(...CORES.textoNormal);

  const mesNome = MESES[parametros.mes];
  const textoIntroducao = `Este relatório apresenta um resumo completo das operações do sistema ${produto.denominacao} durante o mês de ${mesNome} de ${parametros.ano}.`;

  // Quebrar texto em linhas
  const linhasIntroducao = doc.splitTextToSize(textoIntroducao, PDF_CONFIG.contentWidth);
  doc.text(linhasIntroducao, PDF_CONFIG.margins.left, currentY);
  currentY += linhasIntroducao.length * 6 + 15;

  // Resumo dos principais indicadores
  const textoResumo = `Os dados apresentados incluem informações sobre o total de gavetas disponíveis no sistema, sepultamentos realizados no período, exumações efetuadas e atendimentos técnicos prestados. Estas informações fornecem uma visão abrangente do desempenho operacional e da utilização do sistema.`;

  const linhasResumo = doc.splitTextToSize(textoResumo, PDF_CONFIG.contentWidth);
  doc.text(linhasResumo, PDF_CONFIG.margins.left, currentY);
  currentY += linhasResumo.length * 6 + 20;

  // Informações de Manutenção integradas
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(...CORES.textoTitulo);
  doc.text('Informações de Manutenção:', PDF_CONFIG.margins.left, currentY);

  currentY += 10;

  // Preparar dados de manutenção com OFFSET de +1 dia
  const ultimaManutencao = dadosManutencao.ultimaManutencao ?
    formatarDataDDMMYYYYComOffset(dadosManutencao.ultimaManutencao, 1) :
    'Não informado';

  const proximaManutencao = dadosManutencao.proximaManutencao ?
    formatarDataDDMMYYYYComOffset(dadosManutencao.proximaManutencao, 1) :
    'Não informado';

  // Tabela de manutenção
  const dadosManutencaoTabela = [
    ['Última Manutenção Realizada', ultimaManutencao],
    ['Próxima Manutenção Prevista', proximaManutencao]
  ];

  doc.autoTable({
    startY: currentY,
    body: dadosManutencaoTabela,
    theme: 'grid',
    headStyles: {
      fillColor: CORES.cabecalhoTabela,
      textColor: CORES.textoBranco,
      fontStyle: 'bold'
    },
    bodyStyles: {
      fontSize: 11,
      textColor: CORES.textoNormal
    },
    columnStyles: {
      0: {
        fillColor: CORES.fundoCinza,
        fontStyle: 'bold',
        cellWidth: 80
      },
      1: {
        fillColor: CORES.fundoBranco,
        cellWidth: 90
      }
    },
    tableWidth: PDF_CONFIG.contentWidth,
    margin: { left: PDF_CONFIG.margins.left, right: PDF_CONFIG.margins.right }
  });

  currentY = doc.lastAutoTable.finalY + 20;

  // Texto final com contato do suporte
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(...CORES.textoNormal);

  const textoFinal = `Para maiores informações ou esclarecimentos sobre os dados apresentados neste relatório, entre em contato com a equipe técnica responsável pelo sistema.`;

  const linhasFinal = doc.splitTextToSize(textoFinal, PDF_CONFIG.contentWidth);
  doc.text(linhasFinal, PDF_CONFIG.margins.left, currentY);

  currentY += linhasFinal.length * 6 + 15;

  // Informações de contato do suporte técnico
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(...CORES.textoTitulo);
  doc.text('Suporte Técnico:', PDF_CONFIG.margins.left, currentY);

  currentY += 8;

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(...CORES.textoNormal);
  doc.text('Telefone: (81) 99999-6376', PDF_CONFIG.margins.left, currentY);

  console.log('✅ Página conclusiva adicionada');
}

/**
 * Valida os dados do relatório antes da geração
 * @param {Object} relatorioData - Dados do relatório
 * @throws {Error} Se dados obrigatórios estiverem ausentes
 */
function validarDadosRelatorio(relatorioData) {
  if (!relatorioData) {
    throw new Error('Dados do relatório não fornecidos');
  }

  // Validar dados obrigatórios
  const camposObrigatorios = ['cliente', 'produto', 'parametros'];
  for (const campo of camposObrigatorios) {
    if (!relatorioData[campo]) {
      throw new Error(`Campo obrigatório ausente: ${campo}`);
    }
  }

  // Validar dados do cliente
  if (!relatorioData.cliente.razao_social || !relatorioData.cliente.cnpj) {
    throw new Error('Dados do cliente incompletos (razão social ou CNPJ ausentes)');
  }

  // Validar dados do produto
  if (!relatorioData.produto.denominacao) {
    throw new Error('Denominação do produto ausente');
  }

  // Validar parâmetros
  const { parametros } = relatorioData;
  if (!parametros.mes || !parametros.ano || !parametros.custoKwh) {
    throw new Error('Parâmetros do relatório incompletos (mês, ano ou custo kWh ausentes)');
  }

  // Validar dados universais e mistos
  if (!relatorioData.dadosUniversais || !relatorioData.valoresMistos) {
    throw new Error('Dados universais ou valores mistos ausentes');
  }

  // Validar blocos
  if (!relatorioData.blocos || !Array.isArray(relatorioData.blocos) || relatorioData.blocos.length === 0) {
    console.warn('⚠️ Nenhum bloco encontrado para o relatório');
  }

  console.log('✅ Validação dos dados concluída com sucesso');
}

/**
 * Gera PDF do relatório mensal
 * @param {Object} relatorioData - Dados completos do relatório
 * @returns {Promise<jsPDF>} Documento PDF gerado
 */
export async function gerarPDFRelatorioMensal(relatorioData) {
  try {
    console.log('📄 [VERSÃO 2025-07-29-21:30] Gerando PDF do relatório mensal - 15 AGENTES ORQUESTRADOS...');
    console.log('📄 [CORREÇÕES DESTA VERSÃO - 10 AGENTES]:');
    console.log('📄 ✅ 1. Margem esquerda ajustada para 20mm (Agente 1)');
    console.log('📄 ✅ 2. Padrão de espaçamento uniforme implementado (Agente 2)');
    console.log('📄 ✅ 3. Coluna "Potência Média" corrigida e dados validados (Agente 3)');
    console.log('📄 ✅ 4. Duração convertida segundos→minutos, coluna analógica removida (Agente 4)');
    console.log('📄 ✅ 5. Marca d\'água redimensionada sem distorção (Agente 5)');
    console.log('📄 [MELHORIAS ANTERIORES]:');
    console.log('📄 ✅ Margem evolution carregada do arquivo público');
    console.log('📄 ✅ Cabeçalho reformatado: linha única com "Mês de Referência: MM/YYYY"');
    console.log('📄 ✅ Datas NaN/NaN/NaN corrigidas com validação robusta');
    console.log('📄 ✅ Títulos das tabelas padronizados (mesmo estilo e cor)');
    console.log('📄 ✅ Nome do bloco padronizado em todas as páginas (sem "Bloco:")');
    console.log('📄 ✅ Separação tabelas Trocas/Estanqueidade');

    // Validar dados antes de prosseguir
    validarDadosRelatorio(relatorioData);

    // Carregar imagem da margem
    console.log('📄 Carregando imagem da margem...');
    const imagemMargem = await carregarImagemMargem();
    console.log('📄 Imagem da margem carregada:', imagemMargem ? 'Sucesso' : 'Falhou');

    const doc = new jsPDF(PDF_CONFIG.orientation, PDF_CONFIG.unit, PDF_CONFIG.format);
    console.log('📄 Documento PDF criado');

    // Adicionar página inicial
    console.log('📄 Iniciando adição da página inicial...');
    adicionarPaginaInicial(doc, relatorioData);
    console.log('📄 Página inicial concluída');

    // Adicionar conteúdo (agora na segunda página) - sem texto introdutório
    console.log('📄 Iniciando segunda página com tabelas...');
    // Começar direto com as tabelas, sem texto introdutório
    // O cabeçalho em formato tabela já será adicionado automaticamente
    let currentY = 50; // Posição inicial após o cabeçalho tabela
    currentY = adicionarInformacoesCliente(doc, relatorioData, currentY);

    // Nova página para resumo executivo se necessário
    if (currentY > 180) {
      doc.addPage();
      currentY = PDF_CONFIG.spacing.afterHeader;
    }

    currentY = adicionarResumoExecutivo(doc, relatorioData, currentY);

    // REMOVIDO: Páginas individuais por bloco (gráficos e tabelas de temperatura/umidade/volume/pressão)
    // await adicionarPaginasPorBloco(doc, relatorioData);

    // Adicionar página de atendimentos
    adicionarPaginaAtendimentos(doc, relatorioData);

    // Adicionar página conclusiva
    adicionarPaginaConclusiva(doc, relatorioData);

    // Adicionar imagem da margem, marca d'água e cabeçalho/rodapé
    adicionarImagemMargem(doc, imagemMargem);
    adicionarMarcaDagua(doc);
    adicionarCabecalhoRodape(doc, relatorioData);

    console.log('✅ PDF gerado com sucesso');
    return doc;

  } catch (error) {
    console.error('❌ Erro ao gerar PDF:', error);
    throw error;
  }
}

/**
 * Baixa o PDF do relatório mensal
 * @param {Object} relatorioData - Dados completos do relatório
 */
export async function baixarPDFRelatorioMensal(relatorioData) {
  try {
    console.log('📄 [VERSÃO 2025-07-29-16:25] Iniciando download do PDF...');
    console.log('📄 Dados para PDF:', relatorioData);

    const doc = await gerarPDFRelatorioMensal(relatorioData);
    const { cliente, parametros } = relatorioData;
    const mesNome = MESES[parametros.mes];

    const nomeArquivo = `Relatorio_Mensal_${cliente.codigo_cliente}_${mesNome}_${parametros.ano}.pdf`;
    console.log('📄 Salvando arquivo:', nomeArquivo);
    doc.save(nomeArquivo);

  } catch (error) {
    console.error('❌ Erro ao baixar PDF:', error);
    throw error;
  }
}
