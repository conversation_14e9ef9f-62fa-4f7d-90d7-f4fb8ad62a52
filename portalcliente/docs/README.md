# 📚 Documentação Portal Evolution

Bem-vindo à documentação completa do Portal Evolution - Sistema de Gestão de Cemitérios.

---

## 📖 **Manuais do Usuário**

### 👤 **Para Usuários Cliente**
📄 **[Manual do Usuário - Perfil Cliente](Manual-Usuario-Perfil-Cliente.md)**
- Como fazer login e recuperar senha
- Navegação no dashboard
- Cadastro e gestão de sepultamentos
- Geração de relatórios
- Configurações de perfil

### 👨‍💼 **Para Administradores**
📄 **[Manual do Usuário - Perfil Administrador](Manual-Usuario-Perfil-Administrador.md)**
- Gestão de usuários e permissões
- Configuração de estrutura (blocos, gavetas)
- Gestão de múltiplos clientes
- Relatórios avançados e analytics
- Configurações do sistema
- Monitoramento e logs

---

## 🔧 **Documentação Técnica**

### 🏗️ **Arquitetura e Desenvolvimento**
📄 **[Documentação Técnica Completa](Documentacao-Tecnica-Portal.md)**
- Visão geral da arquitetura
- Stack tecnológico
- Estrutura do banco de dados
- APIs e endpoints
- Autenticação e segurança
- Deploy e infraestrutura
- Monitoramento e troubleshooting

---

## 🚀 **Guias de Deploy**

### 📋 **Deploy Manual**
📄 **[Manual de Deploy](deploy-manual.md)**
- Configuração de ambiente
- Deploy com Docker
- Configuração de proxy reverso

### 🔄 **Planos de Reorganização**
📄 **[Plano de Reorganização para Produção](PLANO_REORGANIZACAO_PRODUCAO.md)**
- Estrutura otimizada
- Migração de dados
- Boas práticas

---

## 🛠️ **Relatórios Técnicos**

### 🔐 **Sistema de Autenticação**
📄 **[Correção do Sistema "Esqueci Senha"](RELATORIO_CORRECAO_ESQUECI_SENHA.md)**
- Implementação de recuperação de senha
- Validações de segurança
- Testes realizados

---

## 📁 **Estrutura da Documentação**

```
docs/
├── 📄 Manual-Usuario-Perfil-Cliente.md          # Manual para clientes
├── 📄 Manual-Usuario-Perfil-Administrador.md    # Manual para admins
├── 📄 Documentacao-Tecnica-Portal.md            # Documentação técnica
├── 📄 deploy-manual.md                          # Guia de deploy
├── 📄 PLANO_REORGANIZACAO_PRODUCAO.md          # Plano de reorganização
├── 📄 RELATORIO_CORRECAO_ESQUECI_SENHA.md      # Relatório técnico
├── 📂 api/                                      # Documentação de APIs
└── 📄 README.md                                 # Este arquivo
```

---

## 🎯 **Como Usar Esta Documentação**

### **Para Usuários Finais:**
1. Comece com o manual do seu perfil (Cliente ou Administrador)
2. Consulte as seções específicas conforme necessário
3. Use a função de busca do navegador (Ctrl+F) para encontrar tópicos específicos

### **Para Desenvolvedores:**
1. Leia a documentação técnica completa
2. Consulte a estrutura do banco de dados
3. Revise os endpoints de API
4. Siga os guias de deploy para configuração

### **Para Administradores de Sistema:**
1. Consulte o manual do administrador
2. Revise os guias de deploy
3. Configure monitoramento conforme documentado
4. Implemente as boas práticas de segurança

---

## 📝 **Convenções da Documentação**

### **Formatação:**
- **Negrito**: Elementos importantes da interface
- `Código`: Comandos, códigos e nomes de arquivos
- 📄 **Links**: Referências para outros documentos
- ✅ **Checkmarks**: Funcionalidades disponíveis
- ⚠️ **Avisos**: Informações importantes
- 🔴 **Problemas**: Soluções para problemas comuns

### **Estrutura dos Manuais:**
- **Introdução**: Visão geral e objetivos
- **Passo a passo**: Instruções detalhadas
- **Exemplos**: Casos práticos de uso
- **Troubleshooting**: Solução de problemas
- **Referências**: Links e recursos adicionais

---

## 🔄 **Atualizações da Documentação**

### **Histórico de Versões:**
- **v1.0** (Agosto 2025): Documentação inicial completa
  - Manuais de usuário criados
  - Documentação técnica implementada
  - Guias de deploy atualizados

### **Como Contribuir:**
1. **Mantenha atualizado**: Documente todas as mudanças
2. **Use exemplos**: Inclua casos práticos
3. **Seja claro**: Linguagem simples e objetiva
4. **Teste instruções**: Verifique se os passos funcionam
5. **Revise regularmente**: Mantenha informações atuais

---

## 📞 **Suporte à Documentação**

### **Dúvidas sobre a Documentação:**
- **Email**: <EMAIL>
- **Sugestões**: Use o sistema de feedback
- **Correções**: Reporte erros encontrados

### **Atualizações Necessárias:**
Se você encontrar informações desatualizadas ou incorretas, por favor:
1. Documente o problema encontrado
2. Sugira a correção necessária
3. Informe o contexto de uso
4. Entre em contato com a equipe técnica

---

**📅 Última Atualização:** Agosto 2025
**🔄 Versão da Documentação:** 1.0
**💻 Versão do Sistema:** Portal Evolution v2025.08

---

**🎯 Esta documentação é um recurso vivo - mantenha-a atualizada e útil para todos os usuários do Portal Evolution!**
- Rede `redeinterna` criada
- Domínio `portal.evo-eden.site` apontando para o servidor

## ⚙️ Configuração

1. **Configure o arquivo de ambiente:**
```bash
cp configuracao.env.example configuracao.env
nano configuracao.env
```

2. **Principais configurações:**
```env
DOMAIN=portal.evo-eden.site
SSL_EMAIL=<EMAIL>
DB_NAME=portal_evolution
DB_USER=portal_user
DB_PASSWORD=sua_senha_segura
JWT_SECRET=sua_chave_jwt_segura
```

## 🚀 Deploy

### Deploy Completo
```bash
bash deploy-traefik.sh
```

### Monitoramento
```bash
bash monitor.sh
```

### Remover Stack
```bash
bash remove-stack.sh
```

## 📊 Monitoramento

### Verificar Status
```bash
docker stack services portal-evolution
docker stack ps portal-evolution
```

### Logs
```bash
# Backend
docker service logs -f portal-evolution_portal_backend

# Frontend
docker service logs -f portal-evolution_portal_frontend

# Database
docker service logs -f portal-evolution_portal_database
```

### Escalar Serviços
```bash
docker service scale portal-evolution_portal_backend=2
docker service scale portal-evolution_portal_frontend=2
```

## 🌐 Acesso

- **Portal**: https://portal.evo-eden.site
- **API**: https://portal.evo-eden.site/api/health
- **Health Check**: https://portal.evo-eden.site/health

## 👥 Credenciais Padrão

### Administrador
- **Email**: <EMAIL>
- **Senha**: adminnbr5410!

### Cliente Teste
- **Email**: <EMAIL>
- **Senha**: 54321

## 🔧 Desenvolvimento

### Executar Localmente
```bash
# Backend
cd server
npm install
npm start

# Frontend
npm install
npm run dev
```

### Build Manual
```bash
# Backend
docker build -f Dockerfile.backend -t portal-evolution-backend .

# Frontend
docker build -f Dockerfile.frontend -t portal-evolution-frontend .
```

## 📁 Estrutura do Projeto

```
portalcliente/
├── src/                    # Frontend React
├── server/                 # Backend Node.js
├── public/                 # Arquivos estáticos
├── docs/                   # Documentação
├── scripts/                # Scripts auxiliares
├── backup/                 # Configurações antigas
├── docker-compose.prod.yml # Configuração Docker Swarm
├── Dockerfile.backend      # Build do backend
├── Dockerfile.frontend     # Build do frontend
├── nginx-traefik.conf      # Configuração Nginx para Traefik
├── configuracao.env        # Variáveis de ambiente
├── deploy-traefik.sh       # Script de deploy
├── monitor.sh              # Script de monitoramento
└── remove-stack.sh         # Script para remover stack
```

## 🔒 Segurança

- SSL/TLS automático via Let's Encrypt
- Headers de segurança configurados
- Rate limiting implementado
- Autenticação JWT
- Validação de entrada
- Logs de auditoria

## 🐛 Troubleshooting

### Stack não inicia
```bash
# Verificar logs
docker service logs portal-evolution_portal_backend
docker service logs portal-evolution_portal_frontend

# Verificar rede
docker network ls | grep redeinterna

# Verificar Traefik
docker service ls | grep traefik
```

### SSL não funciona
```bash
# Verificar certificados
docker exec $(docker ps -q -f name=traefik) ls -la /etc/traefik/letsencrypt/

# Verificar logs do Traefik
docker service logs traefik_traefik
```

### Banco não conecta
```bash
# Verificar se PostgreSQL está rodando
docker service ls | grep postgres

# Testar conexão
docker exec -it $(docker ps -q -f name=portal_database) psql -U portal_user -d portal_evolution
```

## 📞 Suporte

Para suporte técnico, entre em contato:
- **Email**: <EMAIL>
- **Logs**: Use `bash monitor.sh` para diagnóstico
