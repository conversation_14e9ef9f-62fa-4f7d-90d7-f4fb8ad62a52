A aba "Logs" será responsável por mostrar um histórico completo de tudo o que os usuários fizeram no sistema.

Esta aba deve trazer uma lista de até 50 registros por página, com paginação. Também deve ter a opção de filtrar os dados por:
- Cliente (codigo_cliente)
- Estação/Produto (codigo_estacao)
- Usuário
- Período (Data Inicial e Data Final)
- Tipo de Ação (Cadastro, Edição, Deleção, Exumação)

Sempre os primeiros registros da lista devem ser os mais recentes.

Toda e qualquer ação executado pelo administrador ou usuário cliente devem ser armazenadas considerando o nome da Data e Hora da Ação, Nome do Usuário, Email/Login do Usuário e uma Descrição da Atividade do que foi feito.

Essa Descrição da Atividade deve conter todos os dados em um único texto.

Caso 1: 
Criado novo produto.. 
- Denominação (codigo_estacao): Estação 01 (ETEN_001) 
- Cliente (codigo_cliente): Cemitério Memorial Jard<PERSON>o (CLI003)
- Meses para Exumação: 24 meses para exumar
- Observação: Estação principal para tratamento de restos mortais

Caso 2:
Editado produto.. 
- Denominação (codigo_estacao): Estação 01 (ETEN_001) 
- Cliente (codigo_cliente): Cemitério Memorial Jardim Eterno (CLI003)
- Meses para Exumação: 24 meses para exumar
- Observação: Estação principal para tratamento de restos mortais

Caso 3:
Deletado produto.. 
- Denominação (codigo_estacao): Estação 01 (ETEN_001) 
- Cliente (codigo_cliente): Cemitério Memorial Jardim Eterno (CLI003)
- Meses para Exumação: 24 meses para exumar
- Observação: Estação principal para tratamento de restos mortais


Tudo o que foi feito para produto, deve seguir a mesma lógica para os demais itens, a saber, cadastro, edição, deleção de blocos, sub-blocos e sepultamentos.

Apesar cada item ter seus dados específicos, a lógica de armazenamento deve ser a mesma e deve conter, na Descrição da Atividade, todos os dados em um único texto.

No caso de Sepultamentos, unicamente este, o registro de Exumações também deve ser inserido no LOG.