# ✅ CHECKLIST COMPLETO - PORTAL EVOLUTION VPS

## 📋 **PREPARAÇÃO DO PROJETO (SEGUINDO INSTRUCAO1.MD)**

### **✅ 1. LIMPEZA INICIAL**
- [ ] Remover arquivos antigos de deploy
- [ ] Remover `docker-compose.yml` antigo
- [ ] Remover pastas: `deploy/`, `infra/`, `vps/`, `old-config/`
- [ ] Remover scripts desatualizados

### **✅ 2. ESTRUTURA DE PASTAS CRIADA**
- [ ] `app/` - Código-fonte da aplicação
- [ ] `docker/` - Dockerfiles customizados
- [ ] `scripts/` - Scripts de instalação, migração, etc.
- [ ] `dados/` - Volumes ou arquivos de dados estáticos
- [ ] `.env` - Arquivo de variáveis de ambiente
- [ ] `README-VPS.md` - Documentação do projeto
- [ ] `portal-evolution.yaml` - Arquivo de orquestração Docker

### **✅ 3. ARQUIVOS YAML CRIADOS**
- [ ] `portal-evolution.yaml` - Arquivo principal seguindo padrão VPS
- [ ] Conectado à rede `traefik` externa
- [ ] Nomes de containers claros: `portal-database`, `portal-backend`, `portal-frontend`
- [ ] Volumes usando caminhos relativos: `./dados`, `./app`

### **✅ 4. CONFORMIDADE COM VPS**
- [ ] Todos os serviços conectados à rede `traefik`
- [ ] Volumes com caminhos relativos
- [ ] Nomes de containers descritivos
- [ ] Evita conflitos de portas com Redis, PostgreSQL, Portainer existentes

### **✅ 5. SCRIPTS AUXILIARES**
- [ ] `scripts/iniciar-servico.sh` - Iniciar sistema
- [ ] `scripts/backup-dados.sh` - Backup automático
- [ ] `scripts/atualizar-containers.sh` - Atualizar sistema
- [ ] `scripts/monitorar-servico.sh` - Monitoramento
- [ ] `scripts/preparar-scripts.sh` - Preparar scripts
- [ ] `scripts/validar-estrutura.sh` - Validar projeto
- [ ] `scripts/organizar-estrutura.sh` - Organizar estrutura
- [ ] Todos começam com `#!/bin/bash`
- [ ] Todos têm permissão de execução: `chmod +x script.sh`

---

## ⚙️ **CONFIGURAÇÃO COM COMENTÁRIOS DETALHADOS**

### **✅ ARQUIVO .ENV CONFIGURADO**

#### **Dados da VPS (OBRIGATÓRIO)**
- [ ] `VPS_HOST` - IP da VPS
  ```bash
  # Para coletar: Execute "hostname -I" no terminal da VPS ou "curl ifconfig.me" para IP público
  VPS_HOST=************
  ```

- [ ] `VPS_USER` - Usuário SSH
  ```bash
  # Para coletar: Execute "whoami" no terminal da VPS
  VPS_USER=root
  ```

- [ ] `VPS_PORT` - Porta SSH
  ```bash
  # Para verificar: Execute "ss -tlnp | grep :22" na VPS
  VPS_PORT=22
  ```

#### **Banco de Dados (OBRIGATÓRIO)**
- [ ] `DB_PASSWORD` - Senha do banco
  ```bash
  # Para gerar senha forte: Execute "openssl rand -base64 32" na VPS
  DB_PASSWORD=sua_senha_muito_forte_aqui
  ```

- [ ] `JWT_SECRET` - Chave JWT
  ```bash
  # Para gerar: Execute "openssl rand -hex 64" na VPS
  JWT_SECRET=sua_chave_jwt_muito_segura_aqui
  ```

#### **Domínio (OPCIONAL)**
- [ ] `DOMAIN` - Domínio personalizado
  ```bash
  # Para verificar DNS: Execute "nslookup seudominio.com"
  DOMAIN=meusite.com.br
  ```

- [ ] `SSL_EMAIL` - Email para SSL
  ```bash
  # Usado pelo Let's Encrypt para gerar certificados
  SSL_EMAIL=<EMAIL>
  ```

#### **Email (OPCIONAL)**
- [ ] `EMAIL_HOST` - Servidor SMTP
  ```bash
  # Gmail: smtp.gmail.com | Outlook: smtp-mail.outlook.com
  EMAIL_HOST=smtp.gmail.com
  ```

- [ ] `EMAIL_USER` e `EMAIL_PASS` - Credenciais
  ```bash
  # Para Gmail: Ative "Senhas de app" em https://myaccount.google.com/security
  EMAIL_USER=<EMAIL>
  EMAIL_PASS=sua_senha_de_app
  ```

#### **Recursos (OPCIONAL)**
- [ ] `FRONTEND_MEMORY`, `BACKEND_MEMORY`, `DATABASE_MEMORY`
  ```bash
  # Para verificar recursos: Execute "free -h" e "df -h" na VPS
  FRONTEND_MEMORY=256m
  BACKEND_MEMORY=512m
  DATABASE_MEMORY=1g
  ```

---

## 🐳 **DOCKER E TRAEFIK**

### **✅ VERIFICAÇÕES NA VPS**

#### **Docker Instalado**
- [ ] Docker instalado e rodando
  ```bash
  # Para verificar: docker --version && docker info
  ```

- [ ] Docker Compose instalado
  ```bash
  # Para verificar: docker-compose --version
  ```

#### **Rede Traefik**
- [ ] Rede traefik existe
  ```bash
  # Para verificar: docker network ls | grep traefik
  # Se não existir: docker network create traefik
  ```

#### **Portas Disponíveis**
- [ ] Porta 80 disponível
- [ ] Porta 443 disponível
- [ ] Porta 3001 disponível (backend)
  ```bash
  # Para verificar: ss -tlnp | grep -E ":80|:443|:3001"
  ```

#### **Recursos Suficientes**
- [ ] Memória: mínimo 2GB
- [ ] Disco: mínimo 10GB livres
  ```bash
  # Para verificar: free -h && df -h
  ```

---

## 🚀 **DEPLOY E TESTE**

### **✅ EXECUÇÃO DOS SCRIPTS**

#### **Preparação**
- [ ] `bash scripts/organizar-estrutura.sh` - Organizar estrutura
- [ ] `bash scripts/preparar-scripts.sh` - Preparar scripts
- [ ] `bash scripts/validar-estrutura.sh` - Validar projeto

#### **Deploy**
- [ ] `bash scripts/iniciar-servico.sh` - Iniciar sistema
- [ ] Aguardar containers iniciarem (30-60 segundos)

#### **Verificação**
- [ ] `docker-compose -f portal-evolution.yaml ps` - Status containers
- [ ] `curl http://localhost/health` - Frontend funcionando
- [ ] `curl http://localhost/api/health` - Backend funcionando

### **✅ TESTES DE FUNCIONAMENTO**

#### **Acesso ao Sistema**
- [ ] Frontend carrega: `http://SEU_IP_VPS`
- [ ] Login funciona com credenciais padrão
- [ ] Dashboard carrega dados
- [ ] API responde: `http://SEU_IP_VPS/api/health`

#### **Credenciais Padrão**
- [ ] Admin: `<EMAIL>` / `adminnbr5410!`
- [ ] Cliente: `<EMAIL>` / `54321`

---

## 📊 **MONITORAMENTO E MANUTENÇÃO**

### **✅ SCRIPTS DE MANUTENÇÃO**

#### **Monitoramento**
- [ ] `bash scripts/monitorar-servico.sh` - Status completo
- [ ] `watch -n 30 'bash scripts/monitorar-servico.sh'` - Monitor contínuo

#### **Backup**
- [ ] `bash scripts/backup-dados.sh` - Backup manual
- [ ] Verificar backups: `ls -la dados/backups/`
- [ ] Configurar cron para backup automático

#### **Atualização**
- [ ] `bash scripts/atualizar-containers.sh` - Atualizar sistema
- [ ] Verificar funcionamento após atualização

### **✅ COMANDOS DE DIAGNÓSTICO**

#### **Logs**
- [ ] `docker-compose -f portal-evolution.yaml logs -f` - Logs em tempo real
- [ ] `docker logs portal-backend` - Logs do backend
- [ ] `docker logs portal-frontend` - Logs do frontend
- [ ] `docker logs portal-database` - Logs do banco

#### **Debug**
- [ ] `docker exec -it portal-backend sh` - Conectar no backend
- [ ] `docker exec -it portal-database psql -U portal_user -d portal_evolution` - Conectar no banco
- [ ] `docker exec portal-frontend nginx -t` - Testar config Nginx

---

## 🔧 **COMANDOS ÚTEIS PARA COLETA DE INFORMAÇÕES**

### **✅ INFORMAÇÕES DO SISTEMA**

```bash
# IP da VPS
curl ifconfig.me                    # IP público
hostname -I                         # IP local

# Usuário atual
whoami                              # Nome do usuário

# Recursos do sistema
free -h                             # Memória
df -h                               # Disco
uptime                              # Load do sistema

# Portas em uso
ss -tlnp | grep -E ":80|:443|:3001|:5432"

# Serviços Docker
docker ps                           # Containers rodando
docker network ls                   # Redes Docker
docker volume ls                    # Volumes Docker

# Gerar senhas seguras
openssl rand -base64 32             # Senha do banco
openssl rand -hex 64                # Chave JWT

# Verificar DNS
nslookup seudominio.com             # Verificar domínio

# Timezone
timedatectl                         # Fuso horário
```

---

## 📦 **EXPORTAÇÃO DO PROJETO**

### **✅ COMPACTAÇÃO FINAL**
- [ ] Projeto organizado conforme estrutura
- [ ] Arquivo `.env` configurado
- [ ] Scripts testados e funcionando
- [ ] Documentação atualizada

#### **Comando de Compactação**
```bash
# Compactar projeto para envio
tar -czvf portal-evolution.tar.gz \
    app/ \
    docker/ \
    scripts/ \
    dados/ \
    .env \
    portal-evolution.yaml \
    README-VPS.md \
    CHECKLIST-VPS.md
```

---

## 🎯 **RESUMO FINAL**

### **✅ CHECKLIST COMPLETO**

#### **Estrutura do Projeto**
- [ ] ✅ Pastas organizadas seguindo padrão VPS
- [ ] ✅ Arquivos YAML criados por serviço
- [ ] ✅ Scripts auxiliares funcionando
- [ ] ✅ Dockerfiles customizados
- [ ] ✅ Configuração centralizada em .env

#### **Configuração**
- [ ] ✅ Variáveis de ambiente configuradas
- [ ] ✅ Senhas alteradas dos valores padrão
- [ ] ✅ Rede traefik configurada
- [ ] ✅ Comentários detalhados para coleta de dados

#### **Deploy**
- [ ] ✅ Sistema iniciando corretamente
- [ ] ✅ Containers rodando
- [ ] ✅ Serviços respondendo
- [ ] ✅ Frontend e backend funcionando

#### **Manutenção**
- [ ] ✅ Scripts de backup funcionando
- [ ] ✅ Monitoramento implementado
- [ ] ✅ Atualização automatizada
- [ ] ✅ Logs organizados

---

## 🎉 **PROJETO PRONTO PARA VPS!**

**Seguindo este checklist, o Portal Evolution estará:**
- ✅ **Organizado** conforme padrão da VPS
- ✅ **Configurado** com comentários detalhados
- ✅ **Documentado** para fácil manutenção
- ✅ **Testado** e funcionando
- ✅ **Pronto** para deploy em produção

**Execute os scripts na ordem e siga o checklist para garantir sucesso!** 🚀
