Lógica de funcionamento dos cadastros, edições, remoções e exumações do sepultamentos.

Lógica geral: Todo registro de sepultamento está associado à um codigo_cliente (que sempre é o mesmo ao qual usuário cliente está associado), um codigo_estacao (que são os produtos que aparecem disponíveis na Aba "Produtos" ao usuário cliente), um codigo_bloco (que é algum dos blocos criados e associados ao codigo_estacao) e um codigo_sub_bloco (que é o subbloco que está associado ao respectivo codigo_bloco).

Caso 1: Toda vez que o usuário entra na aba "Produtos" e selecionar algum dos produtos que estão disponíveis para o mesmo, além de todos os dados do produto, devem ter uma aba "Listar Sepultados". Nesta aba devem ser listados todos os sepultamentos que estão associados à este codigo_cliente e codigo_estacao ao qual o usuário selecionou. Devem aparecer os seguintes dados:
  - Nome do sepultado.
  - Denominação do bloco.
  - Número da gaveta.
  - Data e hora do sepultamento.


Caso 2: Ainda nesta aba "Listar Sepultados", perceba que o usuário cliente já está associado à um codigo_cliente e também já clicou num produto que possui um codigo_estacao associado antes de clicar em "Listar Sepultados". Desta forma, pelo botão "Cadastrar Novo Sepultamento", o usuário irá preencher os seguintes dados:

  - Nome do sepultado -> Descrever o nome do Sepultado
  - Código do bloco -> Abra todas as opções disponíveis de blocos, baseado no codigo_cliente e codigo_estacao de todos os filtros até onde o usuário chegou, para que o usuário selecione apenas uma.
  - Número da gaveta -> Abra todas as opções disponíveis de gavetas, baseado no codigo_cliente, codigo_estacao e codigo_bloco. Caso já tenha algum sepultado em alguma gaveta, esta não deverá aparecer dentre as opções de numerações de gavetas.
  - Data do sepultamento -> A data do sepultamento.
  - Hora do sepultamento -> A hora do sepultamento.
  - Observações -> Observações adicionais sobre o sepultamento...


Caso 3: Caso o usuário clique em editar em algum dos sepultamentos que estão listados na aba "Listar Sepultados", ele poderá editar os seguintes dados:

  - Nome do sepultado -> Descrever o nome do Sepultado
  - Data do sepultamento -> A data do sepultamento.
  - Hora do sepultamento -> A hora do sepultamento.
  - Observações -> Observações adicionais sobre o sepultamento.

    Se o usuário tiver cadastrado no Bloco ou Gaveta errado, ele precisará deletar o sepultamento e cadastrar novamente.

Caso 4: Caso o usuário clique em deletar em algum dos sepultamentos que estão listados na aba "Listar Sepultados", ele poderá deletar o sepultamento, desde que preencha o motivo da deleção. Este motivo da deleção deve ficar registrado junto ao registro desta gaveta específica.

Caso 5: Todo registro de sepultamento deve estar associado à um Status sendo "Disponível", "Sepultada", "Deletada" ou "Exumada". 
    Disponível -> Onde o Ativo é uma gaveta que foi criada e está disponível para o usuário cliente cadastrar novo sepultamento nela.
    Sepultada -> Onde a gaveta já tem um cadastro de sepultado nela.
    Deletada -> Onde o sepultamento foi deletado e informado o motivo da deleção da mesma.
    Exumada -> Onde foi realizado a exumação do sepultado que estava na mesma, registrando a data e hora da exumação e observações, e tornando a gaveta Disponível novamente.

Toda vez que o usuário fizer algum procedimento, envie uma mensagem perguntando ao usuário se ele confirma o procedimento. Exemplo: "Tem certeza que deseja deletar este sepultamento?" ou "Tem certeza que deseja exumar este sepultamento?", ou "Você confirma todos os dados registrados deste Sepultamento?"
