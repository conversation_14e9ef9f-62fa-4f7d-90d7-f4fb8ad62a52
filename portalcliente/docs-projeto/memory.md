# Portal Evolution System Requirements
- Implement CRUD for products/blocks/sub-blocks with flexible gaveta numbering.
- Unify Products and Gavetas into Dashboard Geral tab, implement clickable items with detail modals and action buttons (Edit/Remove).
- Implement user management with role-based access (admin vs client): admin users have full access while client users can only add/edit/exhume in Cadastros tab and view details in dashboard.
- Implement responsive design.
- Implement product-associated sepultamento lists with burial details.
- Implement form-based sepultamento management with admin-only exhumation deletion.
- Clients have unique codigo_cliente format (SAF_001).
- Products have codigo_estacao format (ETEN_001) unique per client.
- Implement hierarchical structure (Client->Product->Block->SubBlock->Gavetas).
- Users automatically get access to all client products.
- Remove birth/death dates from sepultamentos.
- Add burial time field to sepultamentos.
- Remove responsible person fields from sepultamentos (keep only observations).
- Show denominations instead of codes in UI.
- Create admin-only product management tab with filters.
- In product management interface, sub-blocks should display gaveta ranges in their information and allow editing ranges, and product items should be selectable to show detailed views with all blocks, sub-blocks, and gaveta ranges organized by blocks.
- Remove 'Cadastros' tab as it's not needed, and implement full logica_app.md functionality when client users click on product cards in the 'Produtos' tab.
- User prefers product modal to show only 'Lista de Sepultados' tab (remove Details tab), wants proper denominação do bloco display in sepultamentos table, and prefers appropriately sized action buttons.
- Admin users should not have access to 'Produtos' tab (only for client users), and logs functionality should be developed according to logica_logs.md specifications.
- User prefers Brazilian timezone (GMT-3) for displaying dates and times in the system.
- User prefers project name 'Portal do Cliente', wants homepage redesigned per logica_inicio.md with indicators (quantities, next exhumation dates), and wants Material-UI restyled following material-kit-react design system from devias-io/material-kit-react.
- User prefers dashboard functionality should only be implemented on the 'Início' page without modifying other application structures.
- User prefers dashboard cards to be clickable with detailed modal views showing product-specific breakdowns.
- User prefers combined gavetas card with red/green progress bar showing occupancy percentages to 2 decimal places, and prefers streamlined UI without activities tab or exhumations total.
- User prefers simplified dropdown displays in sepultamento forms: show only product denomination (hide codigo_estacao), only block denomination (hide codigo_bloco), and only gaveta number (hide sub-block denomination and x/y positioning).
- User prefers consistent card sizing (same x and y dimensions).
- Dashboard cards should be clickable to show detailed breakdowns by product: sepultamentos card shows quantity and daily rate per product, gavetas card shows total/available/occupied counts per product, with admin users having access to all products regardless of client.
- User prefers complete project restyling according to estilizacao_padrao_evo.md file while preserving all functionality, indicating preference for standardized styling patterns across all pages, tabs, forms, and buttons.
- User prefers consistent button styling across different pages and wants dashboard cards to have the same width as the list below them for better visual alignment.
- User prefers dashboard cards to have uniform sizing and be arranged horizontally in a single row rather than stacked vertically, with the list below aligned with the cards.
- User prefers navigation buttons to have consistent highlight behavior (no permanent selection appearance).
- User prefers consistent button styling across different pages - wants +Novo Produto button to match +Novo Cliente button format, dimensions and colors.
- Admin users should have a client filter in the dashboard's top-right corner to filter data by specific client or view all clients when none selected.
- Client filter should allow single selection only, and clients should only appear in filter options if they have at least one complete hierarchy: product/station → block → sub-block with gaveta ranges configured.
- User prefers filter components to have uniform sizing even when no options are selected, rather than allowing them to shrink when empty.
- User prefers consistent label positioning in form controls regardless of selection state, and requires success messages to be displayed after every edit/delete operation confirmation across all project tabs.
- User prefers Evolution Tecnologia Funerária company logos to be placed in strategic frontend locations including the login page for branding purposes.

# Gaveta Numbering and Selection Logic
- Gavetas must have unique numbering within each bloco (not sub-bloco) - Gaveta 12 can exist in different blocos but not in different sub-blocos of the same bloco.
- Sub-bloco selection should be automatic/hidden from users in sepultamento forms.

# Deletion Logic
- System deletion logic and rules are documented in logica_app.md file.

# Testing
- User prefers comprehensive end-to-end testing of all specified requirements, tested individually and systematically at project completion.

# Login Form and Password Reset
- User prefers clean login form with only essential elements: Email/Login field, Password field, Enter button, and Forgot Password link, while maintaining error message functionality.
- User prefers forgot password functionality to send credentials via email to the user's registered email address rather than displaying them in the frontend interface for security reasons.

# Logs Tab
- User prefers Logs tab to show descriptive action descriptions (e.g., 'Usuário Realizou Login' for LOGIN action, 'Usuário realizou um novo cadastro' for CADASTRO action) and wants the 'Tabela' column removed as unnecessary.