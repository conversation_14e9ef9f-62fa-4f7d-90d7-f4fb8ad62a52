
---

## **Desenvolvimento do Portal Evolution em React com PostgreSQL**

### **Objetivo**
Criar um sistema web interativo para o gerenciamento de **Estações de Tratamento de Efluentes em Necrópole (ETENs) e Ossuários**, com **hierarquia de acesso**, controle de **sepultamentos** e interface moderna. O sistema deve ser desenvolvido em **React**, utilizando **PostgreSQL** como banco de dados.

---

## **1. Configuração do Banco de Dados**
- Utilizar **PostgreSQL** para armazenar todas as informações.
- Credenciais do banco:
  - **Hostname:** `************`
  - **Porta:** `5432`
  - **Banco de Dados:** `dbetens`
  - **Nome de Usuário:** `postgres`
  - **Senha:** `ab3780bd73ee4e2804d566ce6fd96209`
- <PERSON><PERSON> as tabelas devem seguir a estrutura existente no banco.

---

## **2. Autenticação e Controle de Acesso**
- Criar **tela de login personalizada** com tema **azul escuro e verde**, destacando o nome **Portal Evolution**.
- Implementar login via **email e senha** (sem Google).
- Usuário **admin**:
  - **Email:** `"admin"`
  - **Senha:** `"adminnbr5410!"`
  - Controle total sobre o sistema.
- Usuário **cliente**:
  - Pode:
    - **Cadastrar, editar e exumar** sepultamentos.
  - Não pode:
    - **Visualizar logs administrativos**.

---

## **3. Estrutura Hierárquica do Sistema**
- **Admin**:
  - Pode visualizar **todas** as informações e cadastrar **novos clientes**.
  - Cada cliente deve ter **usuário, senha e código de cliente**.
  - Pode cadastrar **ETENs e Ossuários**, associando cada produto a um código de cliente.
- **Clientes**:
  - Apenas acessam **produtos** vinculados ao seu **código de cliente**.
  - Podem gerenciar seus próprios sepultamentos.

---

## **4. Gestão de Produtos e Gavetas**
### **Dados Obrigatórios para cada Produto**
Cada produto cadastrado deve conter os seguintes campos:
- **Código do Cliente (`codigo_cliente`)** – Relacionado à conta do cliente.
- **Código do Bloco (`codigo_bloco`)** – Identificação do bloco onde o sepultado será registrado.
- **Código do Sub-bloco (`codigo_sub_bloco`)** – Definição da sub-seção dentro do bloco.
- **Nome do Sepultado**
- **Número da Gaveta** – Dinâmico conforme bloco e sub-bloco.
- **Localização** – Campo de texto descritivo.
- **Data e Hora do Sepultamento**

### **Regras para Gavetas**
- Disponíveis: **cor verde**.
- Ocupadas: **cor vermelha**.
- Passando o mouse sobre gavetas ocupadas, exibir **histórico de exumações**.

### **Personalização da Ordem das Gavetas**
- **Configuração manual**: Permitir que o usuário defina a numeração diretamente.
- **Configuração automática**: Opções predefinidas, incluindo:
  - **Esquerda para direita**, **direita para esquerda**.
  - **Cima para baixo**, **baixo para cima**.
- Criar **tabelas personalizadas** para disposição de gavetas.

### **Gavetas Especiais**
- Algumas ocupam **1,5 vezes** o espaço em altura.
- Ossuários podem conter **múltiplos sepultados** diferenciados por posição (`posição 1, posição 2, posição 3, posição 4`).

### **Tabela de Sepultamentos**
- Em vez de células individuais como em uma planilha, exibir **uma tabela detalhada** com:
  - Nome do sepultado.
  - Código do cliente.
  - Código do bloco.
  - Código do sub-bloco.
  - Número da gaveta.
  - Data e hora do sepultamento.
  - Histórico de exumações (caso existam).

---

## **5. Logs e Auditoria**
- Registrar **apenas** ações de **cadastro, edição e exumação**.
- Logs são visíveis **apenas para o admin**.
- Clientes **não** podem visualizar logs.

---

## **6. Interface e Estilo**
- **Refatoração visual** com tema **azul escuro e verde**.
- Ajuste da **cor da navegação** para melhorar a legibilidade.
- Criar **interface amigável** com tabelas organizadas, trazendo **o máximo de informações sobre cada sepultamento**.

---

## **7. Comunicação via Webhook**
- Todas as requisições devem ser enviadas para:
  - `https://webhook.vps.evo-eden.site/webhook/c8e01995-0591-4989-87a1-77d6d51a97e9`
- Enviar detalhes sobre **cadastro, edição e exumação**, incluindo todos os dados relevantes.

---

## **8. Tecnologias a serem utilizadas**
- **Frontend:** React.js + Styled Components.
- **Gerenciamento de estado:** Redux ou Context API.
- **Banco de Dados:** PostgreSQL.
- **Autenticação:** JWT.
- **Comunicação com Webhook:** Axios para requisições HTTP.

---





Desenvolva a aba Gestão de Clientes.

Preciso que faça tudo funcionar da seguinte forma:

Na aba Clientes, eu preciso ter as opções de Cadastrar Novo, Editar, Inativar cadastros ativos. Todo cliente precisa ter:
Status -> Se ativo ou inativo.
codigo_cliente -> Este código terá formato conforme exemplo: "SAF_001" e não poderá, em hipótese nenhuma, ser repetido com o de outro cliente.
CNPJ -> Numeração no formato "18.384.274/0002-78"
Nome Fantasia -> Nome Fantasia do Cliente
Razão Social -> Razão Social do Cliente
CEP -> CEP do CLiente
Logradouro -> Endereço do Cliente
Número -> Número da Localização do Cliente
Complemento -> Caso exista
Bairro -> Bairro da localização do cliente
Cidade -> Cidade onde reside o cliente
Estado -> Estado onde reside o cliente


Na Aba Dashboard Geral, renomeie-a para "Produtos".

Nesta nova aba produtos, todo produto precisa, antes de qualquer coisa, ser associado à algum cliente já cadastrado. Em seguida, ele precisa dos seguintes parâmetros:

codigo_estacao -> Onde seguirá o seguinte formato "ETEN_001" e não poderá, em hipótese nenhuma, ser repetido com algum outro produto deste mesmo cliente (ou seja, o cliente 1 não pode ter dois produtos de ETEN_001. Mas o cliente 1 e o cliente 2 podem ter os produtos ETEN_001).
meses para exumar -> Essa é a quantidade em meses que se deve colocar para que os sepultados desta estação estejam apropriados para exumar.
Denominação -> Um nome genérico para estação, como "ESTAÇÃO DE TRATAMENTO 001"
Observação -> Detalhes Gerais da Estação

Atente-se que dentro de cada produto, existem Blocos, e estes também devem ser cadastrados. Todo Bloco para ser cadastrado precisa, obrigatoriamente, estar associado à um codigo_cliente e a um codigo_estacao. O bloco terá:
codigo_bloco -> Código do Bloco, onde seguirá o formato "BL_001" e não poderá, em hipótese nenhuma, ser repetido com algum outro bloco deste mesmo produto (ou seja, o produto 1 não pode ter 2 "bloco 1", mas o produto 1 e o produto 2 podem ter "bloco 1").
Denominação -> Nome geral do bloco (exemplo: Bloco 1).

Atente-se também que dentro de cada Bloco existem SubBlocos e estes também deve ser cadastrados. Todo Subbloco para ser cadastrado precisa, obrigatoriamente, estar associado à um codigo_bloco, codigo_cliente e a um codigo_estacao. O subbloco terá:
codigo_sub_bloco -> Código do SubBloco onde seguirá o formato "SUB_001" e não poderá, em hipótese nenhuma, ser repetido com algum outro bloco deste mesmo produto (ou seja, o Bloco 1 não pode ter 2 "Sub Bloco 1". Mas o Bloco 1 e Bloco 2 podem ter 1 "Sub Bloco 1").
Denominação -> Nome geral do subbloco (Exemplo: Sub Bloco 1).
Numeração de Gaveta -> Esta numeração são obrigatórias e dizem respeito à numeração das gavetas que estão neste SubBloco. O cadastro destas numerações precisa ter flexibilidade de tal forma que possa ser informado mais de 1 sequencial de numerações diferentes. Exemplo: Sub Bloco 1 tem Numeração de Gavetas de 001 até 020, e também tem Numeração de Gavetas de 050 até 070.


Na aba "Usuários", cada usuário do tipo "Cliente" deve estar, obrigatoriamente, associado à algum Cliente. Pode remover a aba "Produtos com acesso". O Usuário cadastrado, automaticamente deverá ter acesso à todos os produtos associados ao Cliente.

A Aba de "Sepultamentos" deve ser alterada para "Cadastros". Em geral, esta aba será usada pelos usuários. Ao acessá-la, o usuário poderá ver todos os produtos associados ao cliente. Esta aba deve permitir cadastros, edições, exumações e deletar registros. Ela deve funcionar da seguinte forma:
Ao clicar em cadastrar Novo Sepultado, o usuário deverá *ESCOLHER*, obrigatoriamente, alguma opção para os itens abaixo, onde:
1 - codigo_estacao -> Como o usuário está associado à um Cliente, este usuário terá acesso à todos os produtos que estão associados à este cliente. O usuário deve escolher apenas um produto.
2 - codigo_bloco -> Devem aparecer somente as opções cadastradas para o respectivo codigo_estacao que o usuário selecionou anteriormente.
3 - Número da Gaveta -> Este item é extremamente importante. No momento em que o usuário selecione qual codigo_estacao e qual codigo_bloco, todas as numerações cadastradas dos codigo_sub_bloco que estão associadas ao codigo_bloco, devem aparecer como opções ao usuário. Exemplo: Bloco 1 tem Sub Bloco 1 (Gavetas 001 até 020) e Sub Bloco 2 (Gavetas 050 até 070). O usuário deve ter as opções de escolha de 001 até 020 e de 050 até 070. OBS: Caso já tenha algum cadastro de sepultamento em uma determinada gaveta, então a numeração da respectiva gaveta não deve ficar disponível para o usuário selecionar, até que haja alguma exumação naquela gaveta.
4 - Localização -> Um local de texto que o usuário fique livre para preencher.
5 - Nome do Sepultado -> Um local de texto que o usuário fique livre para preencher.
6 - Data de Sepultamento -> Um local ao qual o usuário deve informar a data do sepultamento.
7 - Hora do Sepultamento -> Um local ao qual o usuário deve informar a hora em que houve o sepultamento.

