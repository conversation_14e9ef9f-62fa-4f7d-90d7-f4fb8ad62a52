dbetens=# SELECT current_database();
 current_database
------------------
 dbetens
(1 row)

dbetens=# SELECT current_schema();
 current_schema
----------------
 public
(1 row)

dbetens=# SELECT schema_name FROM information_schema.schemata;
    schema_name
--------------------
 pg_toast
 pg_catalog
 public
 information_schema
(4 rows)

dbetens=# \dt public.*
               List of relations
 Schema |        Name        | Type  |  Owner
--------+--------------------+-------+----------
 public | blocos             | table | postgres
 public | clientes           | table | postgres
 public | dadosc             | table | postgres
 public | dadose             | table | postgres
 public | dadost             | table | postgres
 public | gavetas            | table | postgres
 public | logs_auditoria     | table | postgres
 public | numeracoes_gavetas | table | postgres
 public | produtos           | table | postgres
 public | sepultamentos      | table | postgres
 public | sub_blocos         | table | postgres
 public | usuarios           | table | postgres
(12 rows)

dbetens=# \d public.blocos
                                         Table "public.blocos"
    Column    |            Type             | Collation | Nullable |              Default
--------------+-----------------------------+-----------+----------+------------------------------------
 id           | integer                     |           | not null | nextval('blocos_id_seq'::regclass)
 produto_id   | integer                     |           | not null |
 codigo_bloco | character varying(50)       |           | not null |
 nome         | character varying(255)      |           | not null |
 descricao    | text                        |           |          |
 ativo        | boolean                     |           |          | true
 created_at   | timestamp without time zone |           |          | CURRENT_TIMESTAMP
 updated_at   | timestamp without time zone |           |          | CURRENT_TIMESTAMP
Indexes:
    "blocos_pkey" PRIMARY KEY, btree (id)
    "blocos_produto_id_codigo_bloco_key" UNIQUE CONSTRAINT, btree (produto_id, codigo_bloco)
Foreign-key constraints:
    "blocos_produto_id_fkey" FOREIGN KEY (produto_id) REFERENCES produtos(id)
Referenced by:
    TABLE "sub_blocos" CONSTRAINT "sub_blocos_bloco_id_fkey" FOREIGN KEY (bloco_id) REFERENCES blocos(id)

dbetens=#