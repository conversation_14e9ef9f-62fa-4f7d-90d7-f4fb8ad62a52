# 🚀 Guia de Deploy Simples - Portal Evolution

## 📋 **V<PERSON><PERSON>eral**

Este guia mostra como fazer deploy do Portal Evolution na sua VPS de forma simples, com atualização automática via `git pull`.

## 🎯 **Fluxo Simples**

```
1. Configurar VPS (uma vez)
2. Editar arquivo configuracao.env
3. Fazer deploy inicial
4. Para atualizar: git pull + script de atualização
```

---

## 📝 **PASSO 1: Configurar VPS (Executar UMA VEZ)**

### **1.1 Conectar na VPS**
```bash
ssh root@************
```

### **1.2 Executar Script de Configuração (MÉTODO CORRIGIDO)**
```bash
# Método 1: Script simplificado (RECOMENDADO)
curl -fsSL https://raw.githubusercontent.com/MauricioFilh/portalevo/master/portalcliente/configurar-vps-simples.sh -o configurar-vps-simples.sh
chmod +x configurar-vps-simples.sh
bash configurar-vps-simples.sh
```

**OU se o método acima não funcionar:**

```bash
# Método 2: Manual
apt update && apt upgrade -y
apt install -y curl wget git unzip nano htop ufw docker.io docker-compose

# Configurar firewall
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# Criar diretório e baixar código
mkdir -p /opt/portal-evolution
cd /opt/portal-evolution
wget -O repo.zip https://github.com/MauricioFilh/portalevo/archive/refs/heads/master.zip
unzip repo.zip
mv portalevo-master/* .
rm -rf portalevo-master repo.zip
chmod +x portalcliente/*.sh
```

**O script irá:**
- ✅ Instalar Docker e dependências
- ✅ Configurar firewall
- ✅ Clonar o repositório em `/opt/portal-evolution`
- ✅ Configurar auto-start (inicia automaticamente quando VPS reinicia)
- ✅ Configurar backup automático
- ✅ Configurar monitoramento

---

## 📝 **PASSO 2: Configurar Dados da VPS**

### **2.1 Editar Arquivo de Configuração**
```bash
# Na VPS, editar configurações
nano /opt/portal-evolution/portalcliente/configuracao.env
```

### **2.2 Configurar Seus Dados**
```env
# ===================================
# DADOS DA VPS
# ===================================
VPS_HOST=************
VPS_USER=root
VPS_PORT=22

# ===================================
# DOMÍNIO (OPCIONAL)
# ===================================
DOMAIN=seu-dominio.com
SSL_EMAIL=<EMAIL>

# ===================================
# BANCO DE DADOS
# ===================================
DB_NAME=portal_evolution
DB_USER=portal_user
DB_PASSWORD=SUA_SENHA_SEGURA_AQUI

# ===================================
# APLICAÇÃO
# ===================================
JWT_SECRET=SUA_CHAVE_JWT_MUITO_SEGURA_AQUI

# ===================================
# EMAIL (OPCIONAL)
# ===================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=sua-senha-app
```

**⚠️ IMPORTANTE**: Altere as senhas padrão!

---

## 📝 **PASSO 3: Deploy Inicial**

### **3.1 Executar Deploy**
```bash
# Na VPS
cd /opt/portal-evolution/portalcliente
bash deploy.sh
```

### **3.2 Verificar Deploy**
```bash
# Verificar containers
docker-compose -f docker-compose.prod.yml ps

# Verificar logs
docker-compose -f docker-compose.prod.yml logs -f

# Testar aplicação
curl http://localhost/health
curl http://localhost/api/health
```

### **3.3 Acessar Sistema**
- **Por IP**: http://************
- **Por domínio**: http://seu-dominio.com (se configurado)

**Credenciais padrão:**
- Admin: `<EMAIL>` / `adminnbr5410!`
- Cliente: `<EMAIL>` / `54321`

---

## 📝 **PASSO 4: Atualizar Sistema (Sempre que precisar)**

### **4.1 No Seu Computador Local**
```bash
# Fazer alterações no código
git add .
git commit -m "Suas alterações"
git push origin master
```

### **4.2 Na VPS**
```bash
# Conectar na VPS
ssh root@************

# Navegar para o projeto
cd /opt/portal-evolution/portalcliente

# Atualizar sistema
bash atualizar.sh
```

**O script de atualização irá:**
- ✅ Fazer `git pull` para pegar últimas alterações
- ✅ Reconstruir containers com novo código
- ✅ Reiniciar aplicação
- ✅ Verificar saúde do sistema
- ✅ Limpar imagens antigas

---

## 🔧 **Comandos Úteis**

### **Gerenciar Aplicação**
```bash
# Iniciar aplicação
systemctl start portal-evolution

# Parar aplicação
systemctl stop portal-evolution

# Reiniciar aplicação
systemctl restart portal-evolution

# Ver status
systemctl status portal-evolution

# Ver logs do sistema
journalctl -u portal-evolution -f
```

### **Gerenciar Containers**
```bash
# Ver status dos containers
docker-compose -f docker-compose.prod.yml ps

# Ver logs
docker-compose -f docker-compose.prod.yml logs -f

# Reiniciar containers
docker-compose -f docker-compose.prod.yml restart

# Parar containers
docker-compose -f docker-compose.prod.yml down

# Iniciar containers
docker-compose -f docker-compose.prod.yml up -d
```

### **Monitoramento**
```bash
# Script de monitoramento
bash /opt/portal-evolution/monitor.sh

# Verificar saúde
curl http://localhost/health
curl http://localhost/api/health

# Ver uso de recursos
docker stats

# Ver espaço em disco
df -h
```

### **Backup**
```bash
# Backup manual
bash /opt/portal-evolution/backup.sh

# Ver backups
ls -la /opt/portal-evolution/backups/

# Restaurar backup
cat /opt/portal-evolution/backups/db_backup_YYYYMMDD_HHMMSS.sql | docker exec -i portal_database psql -U portal_user -d portal_evolution
```

---

## 🔒 **Configurar SSL (Opcional)**

### **Se você tem um domínio:**
```bash
# Instalar certificado SSL
apt install certbot python3-certbot-nginx
certbot --nginx -d seu-dominio.com

# Configurar renovação automática
crontab -e
# Adicionar linha:
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🚨 **Troubleshooting**

### **Problema: Containers não iniciam**
```bash
# Ver logs detalhados
docker-compose -f docker-compose.prod.yml logs

# Verificar configuração
cat configuracao.env

# Reiniciar Docker
systemctl restart docker
```

### **Problema: Aplicação não responde**
```bash
# Verificar se containers estão rodando
docker ps

# Verificar portas
netstat -tlnp | grep :80
netstat -tlnp | grep :3001

# Verificar firewall
ufw status
```

### **Problema: Banco de dados com erro**
```bash
# Ver logs do banco
docker logs portal_database

# Conectar no banco
docker exec -it portal_database psql -U portal_user -d portal_evolution

# Verificar tabelas
\dt
```

### **Problema: Erro de permissão**
```bash
# Corrigir permissões
chown -R root:root /opt/portal-evolution
chmod +x /opt/portal-evolution/portalcliente/*.sh
```

---

## 📊 **Estrutura de Arquivos**

```
/opt/portal-evolution/
├── portalcliente/
│   ├── configuracao.env          # ⚙️ Suas configurações
│   ├── docker-compose.prod.yml   # 🐳 Configuração Docker
│   ├── deploy.sh                 # 🚀 Script de deploy
│   ├── atualizar.sh             # 🔄 Script de atualização
│   ├── Dockerfile.frontend       # 🖥️ Build do frontend
│   ├── Dockerfile.backend        # ⚙️ Build do backend
│   └── nginx.conf               # 🌐 Configuração Nginx
├── backups/                     # 💾 Backups automáticos
├── backup.sh                    # 📦 Script de backup
└── monitor.sh                   # 📊 Script de monitoramento
```

---

## ✅ **Checklist de Deploy**

### **Configuração Inicial (Uma vez)**
- [ ] VPS configurada com `configurar-vps.sh`
- [ ] Arquivo `configuracao.env` editado
- [ ] Senhas alteradas (DB_PASSWORD, JWT_SECRET)
- [ ] Deploy inicial executado
- [ ] Aplicação acessível via browser
- [ ] SSL configurado (se aplicável)

### **Atualizações (Sempre que precisar)**
- [ ] Código alterado e commitado
- [ ] `git push origin master` executado
- [ ] `bash atualizar.sh` executado na VPS
- [ ] Aplicação funcionando após atualização

---

## 🎉 **Sistema Funcionando!**

Após seguir este guia, você terá:

- ✅ **Portal Evolution** rodando na VPS
- ✅ **Auto-start** configurado (inicia automaticamente)
- ✅ **Backup automático** diário
- ✅ **Atualização simples** via git pull
- ✅ **Monitoramento** básico
- ✅ **SSL** configurado (se aplicável)

**Para atualizar**: Apenas faça `git push` no seu computador e execute `bash atualizar.sh` na VPS!

**Acesse**: http://************ ou http://seu-dominio.com
