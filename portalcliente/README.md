<<<<<<< HEAD
# Portal Evolution - Sistema de Gestão de Cemitérios

![Portal Evolution](https://img.shields.io/badge/Portal-Evolution-blue?style=for-the-badge)
![Version](https://img.shields.io/badge/version-2025.08-green?style=for-the-badge)
![Status](https://img.shields.io/badge/status-production-success?style=for-the-badge)

Sistema completo e moderno para gestão de cemitérios com interface web responsiva, funcionalidades avançadas de relatórios e arquitetura escalável.

---

## 🚀 **Visão Geral**

O Portal Evolution é uma solução completa para gestão de cemitérios que oferece:

- **🏗️ Gestão Hierárquica**: Clientes → Produtos → Blocos → Sub-blocos → Gavetas → Sepultamentos
- **👥 Multi-usuário**: Perfis Cliente e Administrador com permissões específicas
- **📊 Relatórios Avançados**: PDFs, Excel, dashboards interativos
- **🔐 Segurança**: Autenticação JWT, criptografia, auditoria completa
- **📱 Responsivo**: Interface adaptável para desktop, tablet e mobile
- **🐳 Containerizado**: Deploy com Docker Swarm e alta disponibilidade

---

## 💻 **Stack Tecnológico**

### **Frontend**
- **React 18** - Biblioteca principal para UI
- **Vite** - Build tool moderno e rápido
- **Material-UI (MUI)** - Componentes de interface
- **Chart.js** - Gráficos e visualizações
- **Axios** - Cliente HTTP para APIs

### **Backend**
- **Node.js 18** - Runtime JavaScript
- **Express.js** - Framework web
- **PostgreSQL 14** - Banco de dados relacional
- **JWT** - Autenticação stateless
- **bcryptjs** - Criptografia de senhas

### **Infraestrutura**
- **Docker** - Containerização
- **Docker Swarm** - Orquestração
- **Traefik** - Proxy reverso
- **Nginx** - Servidor web
- **Prometheus + Grafana** - Monitoramento

---

## 📁 **Estrutura do Projeto**

```
portalcliente/
├── 📂 src/                     # Frontend React
│   ├── 📂 components/          # Componentes reutilizáveis
│   ├── 📂 pages/              # Páginas da aplicação
│   ├── 📂 services/           # Serviços e APIs
│   ├── 📂 contexts/           # Context API
│   └── 📂 utils/              # Utilitários
├── 📂 server/                  # Backend Node.js
│   ├── 📂 routes/             # Rotas da API
│   ├── 📂 services/           # Lógica de negócio
│   ├── 📂 middleware/         # Middlewares
│   └── 📂 database/           # Configurações DB
├── 📂 scripts/                # Scripts de deploy e manutenção
├── 📂 docs/                   # Documentação completa
├── 📂 docker/                 # Configurações Docker
└── 📂 public/                 # Assets estáticos
```

---

## 🛠️ **Como Executar**

### **Desenvolvimento Local**

#### **1. Pré-requisitos**
- Node.js 18+
- PostgreSQL 14+
- Docker (opcional)

#### **2. Backend**
```bash
cd server
npm install
cp .env.example .env
# Configure as variáveis de ambiente
npm run dev
```

#### **3. Frontend**
```bash
npm install
npm run dev
```

### **Deploy com Docker**

#### **1. Build das Imagens**
```bash
# Backend
docker build -f Dockerfile.backend -t portal-evolution-backend:latest .

# Frontend
docker build -f Dockerfile.frontend -t portal-evolution-frontend:latest .
```

#### **2. Deploy da Stack**
```bash
# Produção
docker stack deploy -c portal-evolution.yaml portal-production

# Desenvolvimento
docker stack deploy -c portal-evolution-dev.yaml portal-development
```

#### **3. Scripts Automatizados**
```bash
# Deploy completo (desenvolvimento)
./deploy-dev-only.sh

# Deploy completo (produção)
./deploy.sh

# Limpeza do sistema
./scripts/cleanup-docker.sh
=======
# Portal Evolution - Sistema de Gestão Cemiterial

Sistema completo de gestão cemiterial integrado com infraestrutura moderna usando Docker Swarm, Traefik e SSL automático.

## 🌐 Acesso

- **Portal**: https://portal.evo-eden.site
- **API**: https://portal.evo-eden.site/api/health

## 🚀 Características

- **Frontend**: React 19 + Material-UI + Vite
- **Backend**: Node.js + Express + PostgreSQL
- **Infraestrutura**: Docker Swarm + Traefik + Let's Encrypt
- **SSL**: Automático via Let's Encrypt
- **Monitoramento**: Scripts integrados de monitoramento
- **Escalabilidade**: Suporte a múltiplas réplicas

## 📁 Estrutura do Projeto

```
portalevo/
├── portalcliente/          # 🎯 APLICAÇÃO PRINCIPAL
│   ├── src/                # Frontend React
│   ├── server/             # Backend Node.js
│   ├── public/             # Arquivos estáticos
│   ├── docs/               # Documentação técnica
│   ├── scripts/            # Scripts auxiliares
│   ├── backup/             # Configurações antigas
│   ├── docker-compose.prod.yml  # Configuração Docker Swarm
│   ├── Dockerfile.backend       # Build do backend
│   ├── Dockerfile.frontend      # Build do frontend
│   ├── nginx-traefik.conf       # Configuração Nginx
│   ├── configuracao.env         # Variáveis de ambiente
│   ├── deploy-traefik.sh        # 🚀 Script de deploy
│   ├── monitor.sh               # 📊 Script de monitoramento
│   ├── validate-setup.sh        # ✅ Validação de configuração
│   └── remove-stack.sh          # 🗑️ Remover stack
├── docs-projeto/           # Documentação do projeto
├── assets-projeto/         # Logos e imagens
├── scripts-projeto/        # Scripts diversos
└── backup-projeto/         # Arquivos antigos
```

## ⚡ Quick Start

### 1. Validar Configuração
```bash
cd portalcliente
bash validate-setup.sh
```

### 2. Configurar Ambiente
```bash
cp configuracao.env.example configuracao.env
nano configuracao.env
```

### 3. Deploy
```bash
bash deploy-traefik.sh
```

### 4. Monitorar
```bash
bash monitor.sh
```

## 🔧 Comandos Principais

### Deploy e Gestão
```bash
# Deploy completo
bash deploy-traefik.sh

# Monitoramento interativo
bash monitor.sh

# Validar configuração
bash validate-setup.sh

# Remover stack
bash remove-stack.sh
```

### Docker Swarm
```bash
# Status dos serviços
docker stack services portal-evolution

# Logs em tempo real
docker service logs -f portal-evolution_portal_backend
docker service logs -f portal-evolution_portal_frontend

# Escalar serviços
docker service scale portal-evolution_portal_backend=2
docker service scale portal-evolution_portal_frontend=2
```

## 🔒 Credenciais Padrão

### Administrador
- **Email**: <EMAIL>
- **Senha**: adminnbr5410!

### Cliente Teste
- **Email**: <EMAIL>
- **Senha**: 54321

## 🏗️ Infraestrutura

### Serviços Integrados
- **Traefik**: Proxy reverso com SSL automático
- **PostgreSQL**: Banco de dados principal
- **N8N**: Automação de workflows
- **Portainer**: Gestão de containers
- **Redis**: Cache e sessões

### Domínios Configurados
- `portal.evo-eden.site` - Portal Evolution
- `n8n.vps.evo-eden.site` - N8N Editor
- `webhook.vps.evo-eden.site` - N8N Webhook
- `port.vps.evo-eden.site` - Portainer

## 📊 Monitoramento

### Health Checks
- **Frontend**: https://portal.evo-eden.site/health
- **Backend**: https://portal.evo-eden.site/api/health

### Logs
```bash
# Logs do Portal
docker service logs portal-evolution_portal_backend
docker service logs portal-evolution_portal_frontend
docker service logs portal-evolution_portal_database

# Logs do Traefik
docker service logs traefik_traefik
```

## 🔧 Troubleshooting

### Problemas Comuns

1. **Stack não inicia**
   ```bash
   bash validate-setup.sh
   docker service logs portal-evolution_portal_backend
   ```

2. **SSL não funciona**
   ```bash
   docker service logs traefik_traefik
   ```

3. **Banco não conecta**
   ```bash
   docker service ls | grep postgres
   ```

### Scripts de Diagnóstico
```bash
# Validação completa
bash validate-setup.sh

# Monitor interativo
bash monitor.sh

# Status da infraestrutura
docker stack ls
docker service ls
docker network ls
```

## 📞 Suporte

- **Email**: <EMAIL>
- **Documentação**: `portalcliente/docs/`
- **Logs**: Use `bash monitor.sh` para diagnóstico

## 🔄 Atualizações

Para atualizar o sistema:
```bash
cd portalcliente
git pull
bash deploy-traefik.sh
>>>>>>> bf310e3defdf886e8e0fc5f8822b697c18ff98ae
```

---

<<<<<<< HEAD
## 🌟 **Funcionalidades Principais**

### **Para Clientes**
- ✅ **Dashboard Interativo**: Estatísticas e gráficos em tempo real
- ✅ **Gestão de Sepultamentos**: Cadastro, edição e consulta
- ✅ **Consulta de Gavetas**: Disponibilidade e ocupação
- ✅ **Relatórios Detalhados**: PDF e Excel com filtros avançados
- ✅ **Perfil Pessoal**: Gestão de dados e senha

### **Para Administradores**
- ✅ **Gestão de Usuários**: Criação, edição e controle de acesso
- ✅ **Configuração de Estrutura**: Blocos, sub-blocos e gavetas
- ✅ **Multi-cliente**: Gestão de múltiplos cemitérios
- ✅ **Relatórios Consolidados**: Visão geral de todos os clientes
- ✅ **Logs e Auditoria**: Monitoramento completo do sistema
- ✅ **Backup e Segurança**: Ferramentas de manutenção

---

## 🌐 **Ambientes**

| Ambiente | URL | Descrição |
|----------|-----|-----------|
| **Desenvolvimento** | https://portaldev.evo-eden.site | Testes e desenvolvimento |
| **Produção** | https://portal.evo-eden.site | Ambiente principal |

---

## 📚 **Documentação**

### **Manuais do Usuário**
- 📖 [Manual do Cliente](docs/Manual-Usuario-Perfil-Cliente.md)
- 📖 [Manual do Administrador](docs/Manual-Usuario-Perfil-Administrador.md)

### **Documentação Técnica**
- 🔧 [Documentação Técnica Completa](docs/Documentacao-Tecnica-Portal.md)

---

## 🆘 **Suporte**

### **Suporte Técnico**
- 📧 **Email**: <EMAIL>
- 📞 **Telefone**: (11) 9999-9999
- 🕒 **Horário**: Segunda a Sexta, 8h às 18h

---

**🎯 Portal Evolution - Transformando a gestão de cemitérios com tecnologia moderna e eficiência operacional.**
=======
**Nota**: Este projeto está configurado para funcionar com a infraestrutura existente de Docker Swarm e Traefik. Não modifique a configuração de rede ou portas sem verificar impactos nos outros serviços.
>>>>>>> bf310e3defdf886e8e0fc5f8822b697c18ff98ae
