version: '3.8'

services:
  portalevo-backend:
    image: portal-evolution-backend:v1.0.11-limpeza-prod
    networks:
      - redeinterna
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres_postgres
      - DB_PORT=5432
      - DB_NAME=dbetens
      - DB_USER=postgres
      - DB_PASSWORD=ab3780bd73ee4e2804d566ce6fd96209
      - JWT_SECRET=portal_jwt_secret_key_muito_segura_2025
      - PORT=3001
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=jgvhevmyjpuucbhp
      - TZ=America/Sao_Paulo
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 10s
        max_attempts: 5
      resources:
        limits:
          memory: 512m
        reservations:
          memory: 256M
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portal-api.rule=Host(`portal.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.portal-api.entrypoints=websecure"
        - "traefik.http.routers.portal-api.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal-api.loadbalancer.server.port=3001"

  portalevo-frontend:
    image: portal-evolution-frontend:v1.0.11-limpeza-prod
    networks:
      - redeinterna
    environment:
      - TZ=America/Sao_Paulo
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - portalevo-backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          memory: 256m
        reservations:
          memory: 128M
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portal.rule=Host(`portal.evo-eden.site`)"
        - "traefik.http.routers.portal.entrypoints=websecure"
        - "traefik.http.routers.portal.tls.certresolver=letsencrypt"
        - "traefik.http.services.portal.loadbalancer.server.port=80"

networks:
  redeinterna:
    external: true
