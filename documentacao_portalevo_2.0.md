# 📋 **DOCUMENTAÇÃO TÉCNICA COMPLETA - PORTAL EVOLUTION V2.0**

## 🎯 **VISÃO GERAL DO SISTEMA**

O **Portal Evolution** é um sistema web completo para gerenciamento de cemitérios, ETENs (Estações de Tratamento de Efluentes Necrotérios) e ossuários. Desenvolvido com arquitetura de microsserviços containerizada, oferece uma solução robusta e escalável para controle de sepultamentos, gavetas, exumações, relatórios administrativos e gestão hierárquica de produtos funerários.

### **📊 Características Principais:**
- ✅ **Gestão Hierárquica de Produtos** - Controle de clientes, produtos (ETENs/Ossuários), blocos e sub-blocos
- ✅ **Controle de Gavetas** - Gerenciamento de numerações, ranges e disponibilidade
- ✅ **Gestão de Sepultamentos** - Registro completo com data/hora e controle de exumações
- ✅ **Relatórios Avançados** - PDFs com marca d'água, filtros por período e métricas detalhadas
- ✅ **Dashboard Inteligente** - Estatísticas em tempo real e próximas exumações
- ✅ **Sistema de Transferência** - Movimentação de gavetas entre sub-blocos
- ✅ **Gestão de Usuários** - Controle de acesso por perfis (Admin/Cliente) com logs de auditoria
- ✅ **Interface Responsiva** - Design Material-UI adaptável a qualquer dispositivo
- ✅ **Arquitetura de Microsserviços** - Escalabilidade e manutenibilidade otimizadas

---

## 🏗️ **ARQUITETURA DO SISTEMA**

### **Arquitetura de Microsserviços**
O sistema é composto por microsserviços independentes que se comunicam através de APIs REST, orquestrados via Docker Swarm e roteados pelo Traefik como proxy reverso. Cada serviço possui responsabilidades específicas e pode ser escalado independentemente.

### **Stack Tecnológico:**

#### **🎨 Frontend (React + Vite):**
- **React 18.2** - Biblioteca principal com hooks e Context API
- **Material-UI (MUI) v5** - Framework de componentes com tema customizado
- **Vite 4.x** - Build tool otimizado com hot reload e code splitting
- **React Router v6** - Roteamento com guards de autenticação
- **Axios** - Cliente HTTP com interceptors para autenticação
- **jsPDF** - Geração de relatórios PDF com marca d'água
- **Context API** - Gerenciamento de estado para autenticação e loading

#### **⚙️ Backend (Node.js + Express):**
- **Node.js 18 Alpine** - Runtime JavaScript otimizado para containers
- **Express.js** - Framework web com middleware customizado
- **PostgreSQL 14** - Banco relacional com extensões uuid-ossp e pgcrypto
- **JWT** - Autenticação com refresh tokens e rate limiting
- **bcryptjs** - Criptografia de senhas com salt rounds
- **Nodemailer** - Envio de emails com templates HTML
- **Multer** - Upload de arquivos com validação de tipos
- **CORS** - Configurado para múltiplos domínios e ambientes

#### **🐳 Infraestrutura (Docker + Traefik):**
- **Docker Swarm** - Orquestração de containers com alta disponibilidade
- **Nginx Alpine** - Servidor web otimizado para servir arquivos estáticos
- **Traefik v2.11** - Proxy reverso com descoberta automática de serviços
- **Let's Encrypt** - Certificados SSL com renovação automática
- **PostgreSQL Container** - Banco de dados isolado com volumes persistentes
- **Rede Overlay** - Comunicação segura entre microsserviços

---

## 🗄️ **ESTRUTURA DO BANCO DE DADOS**

### **Modelo de Dados Hierárquico**
O sistema utiliza uma estrutura hierárquica baseada em códigos para organizar os dados de forma eficiente e escalável:

**Hierarquia:** Cliente → Produto (ETEN/Ossuário) → Bloco → Sub-Bloco → Gaveta → Sepultamento

### **Tabelas Principais:**

#### **🏢 Clientes**
Tabela base que representa as empresas/cemitérios que utilizam o sistema.
- **Chave Primária:** codigo_cliente (VARCHAR)
- **Campos:** CNPJ, razão social, nome fantasia, endereço completo, logo
- **Relacionamentos:** Um cliente pode ter múltiplos produtos e usuários

#### **👥 Usuários**
Controle de acesso ao sistema com dois tipos: admin e cliente.
- **Chave Primária:** id (SERIAL)
- **Tipos:** admin (acesso total) e cliente (acesso restrito ao seu código)
- **Autenticação:** Email único, senha criptografada com bcrypt
- **Relacionamentos:** Vinculado a um cliente específico (exceto admins)

#### **🏭 Produtos**
Representa ETENs (Estações de Tratamento) e Ossuários gerenciados.
- **Chave Primária:** codigo_cliente + codigo_estacao
- **Campos:** Denominação, meses para exumar (padrão 24), observações
- **Relacionamentos:** Pertence a um cliente, contém múltiplos blocos

#### **🧱 Blocos**
Divisões físicas dentro de cada produto para organização espacial.
- **Chave Primária:** codigo_cliente + codigo_estacao + codigo_bloco
- **Campos:** Denominação, status ativo
- **Relacionamentos:** Pertence a um produto, contém múltiplos sub-blocos

#### **📦 Sub-Blocos**
Subdivisões dos blocos para controle mais granular das gavetas.
- **Chave Primária:** codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco
- **Campos:** Denominação, status ativo
- **Relacionamentos:** Pertence a um bloco, contém múltiplas gavetas

#### **🗃️ Gavetas**
Unidades individuais onde são realizados os sepultamentos.
- **Chave Primária:** codigo_cliente + codigo_estacao + codigo_bloco + codigo_sub_bloco + numero_gaveta
- **Campos:** Posição X/Y (padrão 1), altura especial, disponibilidade
- **Status:** Disponível (true) ou ocupada (false)
- **Relacionamentos:** Pertence a um sub-bloco, pode ter um sepultamento ativo

#### **⚰️ Sepultamentos**
Registros dos sepultamentos realizados nas gavetas.
- **Chave Primária:** id (SERIAL)
- **Campos:** Nome do sepultado, data/hora do sepultamento, data de exumação, observações
- **Controle:** Apenas um sepultamento ativo por gaveta
- **Relacionamentos:** Vinculado a uma gaveta específica

#### **📊 Numerações de Gavetas**
Controle de ranges de numeração para geração automática de gavetas.
- **Campos:** Número início, número fim para definir ranges
- **Funcionalidade:** Gera automaticamente gavetas individuais baseadas nos ranges
- **Validação:** Impede sobreposição de ranges no mesmo sub-bloco

#### **📝 Logs de Auditoria**
Sistema completo de rastreabilidade de todas as ações no sistema.
- **Campos:** Usuário, ação, tabela afetada, dados antigos/novos, IP, user agent
- **Retenção:** Limpeza automática após 30 dias
- **Funcionalidade:** Rastreamento completo para compliance e debugging

### **� Relacionamentos e Integridade Referencial:**

#### **Estrutura Hierárquica:**
- **Clientes** (1:N) → **Produtos** (1:N) → **Blocos** (1:N) → **Sub-Blocos** (1:N) → **Gavetas** (1:1) → **Sepultamentos**
- **Clientes** (1:N) → **Usuários** (controle de acesso por cliente)
- **Numerações de Gavetas** → **Gavetas** (geração automática baseada em ranges)

#### **Constraints e Validações:**
- **Chaves Compostas:** Todas as tabelas principais usam códigos como chaves primárias
- **Cascata de Exclusão:** Deletar um produto remove todos os blocos, sub-blocos e gavetas relacionados
- **Proteção de Integridade:** Não é possível deletar entidades que possuem sepultamentos ativos
- **Validação de Ranges:** Sistema impede sobreposição de numerações no mesmo sub-bloco
- **Controle de Disponibilidade:** Gavetas são automaticamente marcadas como indisponíveis ao criar sepultamento

---

## 🔄 **API ENDPOINTS E FUNCIONALIDADES**

### **� Sistema de Autenticação:**

#### **Endpoints de Autenticação:**
- **POST /api/auth/login** - Autenticação com email e senha, retorna JWT token
- **POST /api/auth/logout** - Invalidação do token de acesso
- **GET /api/auth/verify** - Verificação de validade do token JWT
- **POST /api/auth/forgot-password** - Solicitação de reset de senha via email
- **GET /api/auth/validate-reset-token/:token** - Validação de token de reset
- **POST /api/auth/reset-password** - Redefinição de senha com token válido
- **GET /api/auth/password-criteria** - Critérios de validação de senha
- **POST /api/auth/validate-password** - Validação de força da senha

#### **Middleware de Segurança:**
- **Rate Limiting:** 20 tentativas de login por IP em 15 minutos
- **JWT Verification:** Validação automática de tokens em rotas protegidas
- **Admin Guard:** Middleware para rotas que exigem privilégios administrativos
- **Client Isolation:** Usuários clientes só acessam dados do próprio código

### **👥 Gestão de Usuários:**

#### **Endpoints de Usuários (Admin apenas):**
- **GET /api/usuarios** - Listagem completa de usuários com filtros
- **POST /api/usuarios** - Criação de novos usuários com validação
- **PUT /api/usuarios/:id** - Atualização de dados do usuário
- **DELETE /api/usuarios/:id** - Exclusão de usuário (soft delete)
- **PATCH /api/usuarios/:id/toggle-status** - Ativação/desativação de usuário
- **POST /api/usuarios/:id/reset-password** - Reset forçado de senha pelo admin

### **🏢 Gestão de Clientes:**

#### **Endpoints de Clientes (Admin apenas):**
- **GET /api/clientes** - Listagem de clientes com informações básicas
- **GET /api/clientes/com-gavetas** - Clientes com estatísticas de gavetas
- **GET /api/clientes/:codigo** - Detalhes específicos de um cliente
- **POST /api/clientes** - Criação de novo cliente com upload de logo
- **PUT /api/clientes/:codigo** - Atualização de dados do cliente
- **PATCH /api/clientes/:codigo/toggle-status** - Ativação/desativação
- **DELETE /api/clientes/:codigo/logo** - Remoção do logo do cliente

### **🏭 Gestão de Produtos:**

#### **Endpoints de Produtos:**
- **GET /api/produtos** - Listagem de produtos com filtros por cliente
- **GET /api/produtos/:codigoCliente/:codigoEstacao** - Detalhes específicos do produto
- **POST /api/produtos** - Criação de novo produto (ETEN/Ossuário)
- **PUT /api/produtos/:codigoCliente/:codigoEstacao** - Atualização do produto
- **DELETE /api/produtos/:codigoCliente/:codigoEstacao** - Exclusão com validação de dependências

### **🧱 Gestão de Blocos:**

#### **Endpoints de Blocos:**
- **GET /api/produtos/:codigoCliente/:codigoEstacao/blocos** - Blocos de um produto
- **POST /api/produtos/:codigoCliente/:codigoEstacao/blocos** - Criação de bloco
- **PUT /api/blocos/:codigoCliente/:codigoEstacao/:codigoBloco** - Atualização de bloco
- **DELETE /api/blocos/:codigoCliente/:codigoEstacao/:codigoBloco** - Exclusão de bloco

### **📦 Gestão de Sub-Blocos:**

#### **Endpoints de Sub-Blocos:**
- **GET /api/sub-blocos** - Listagem com filtros hierárquicos
- **POST /api/sub-blocos** - Criação de sub-bloco
- **PUT /api/sub-blocos/:codigoCliente/:codigoEstacao/:codigoBloco/:codigoSubBloco** - Atualização
- **DELETE /api/sub-blocos/:codigoCliente/:codigoEstacao/:codigoBloco/:codigoSubBloco** - Exclusão

### **🗃️ Gestão de Gavetas:**

#### **Endpoints de Gavetas:**
- **GET /api/gavetas** - Listagem com filtros de disponibilidade
- **GET /api/gavetas/disponiveis** - Apenas gavetas disponíveis para sepultamento
- **PATCH /api/gavetas/:codigoCliente/:codigoEstacao/:codigoBloco/:codigoSubBloco/:numeroGaveta/disponibilidade** - Alteração de status

### **📊 Gestão de Ranges:**

#### **Endpoints de Numerações:**
- **GET /api/ranges** - Listagem de ranges por sub-bloco
- **POST /api/ranges** - Criação de range com geração automática de gavetas
- **DELETE /api/ranges/:id** - Exclusão de range com remoção das gavetas associadas

### **⚰️ Gestão de Sepultamentos:**

#### **Endpoints de Sepultamentos:**
- **GET /api/sepultamentos** - Listagem com filtros por cliente, produto, período
- **POST /api/sepultamentos** - Criação de sepultamento com validação de gaveta disponível
- **PUT /api/sepultamentos/:id** - Atualização de dados do sepultamento
- **DELETE /api/sepultamentos/:id** - Exclusão permanente com liberação da gaveta
- **PATCH /api/sepultamentos/:id/exumar** - Processo de exumação com data/hora
- **GET /api/sepultamentos/proximas-exumacoes** - Lista de exumações previstas

### **🔄 Transferência de Gavetas:**

#### **Endpoints de Transferência:**
- **POST /api/transferencia-gavetas** - Transferência de sepultamento entre gavetas
- **GET /api/transferencia-gavetas/historico** - Histórico de transferências realizadas

### **📊 Dashboard e Métricas:**

#### **Endpoints de Dashboard:**
- **GET /api/dashboard/stats** - Estatísticas gerais (gavetas ocupadas, disponíveis, sepultamentos)
- **GET /api/dashboard/sepultamentos-details** - Detalhes dos sepultamentos por período
- **GET /api/dashboard/gavetas-ocupadas-details** - Detalhamento de gavetas ocupadas
- **GET /api/dashboard/gavetas-disponiveis-details** - Detalhamento de gavetas disponíveis
- **GET /api/dashboard/exumacoes-details** - Detalhes de exumações por período
- **GET /api/dashboard/proximas-exumacoes** - Próximas exumações com alertas
- **GET /api/dashboard/taxa-sepultamento-detalhes** - Métricas de taxa de sepultamento
- **GET /api/dashboard/taxa-ocupacao-detalhes** - Métricas de ocupação por produto

### **📄 Relatórios:**

#### **Endpoints de Relatórios:**
- **GET /api/relatorios/sepultamentos** - Dados para relatórios de sepultamentos
- **GET /api/relatorios/ocupacao** - Relatório de ocupação por período
- **GET /api/relatorios/exumacoes** - Relatório de exumações realizadas
- **POST /api/relatorios/gerar-pdf** - Geração de PDF com filtros personalizados
- **GET /api/relatorio-mensal** - Relatório mensal consolidado com métricas

### **📝 Sistema de Logs:**

#### **Endpoints de Auditoria (Admin apenas):**
- **GET /api/logs** - Listagem de logs com filtros por usuário, ação, período
- **GET /api/logs/:id** - Detalhes específicos de um log de auditoria
- **DELETE /api/logs/cleanup** - Limpeza automática de logs antigos (>30 dias)

#### **Funcionalidades de Logging:**
- **Rastreamento Automático:** Todas as ações CRUD são automaticamente logadas
- **Dados Capturados:** Usuário, ação, tabela, dados antigos/novos, IP, user agent
- **Middleware Transparente:** Logging automático sem impacto na performance
- **Retenção Inteligente:** Limpeza automática para otimização de espaço

---

## 🐳 **ARQUITETURA DE MICROSSERVIÇOS**

### **Containerização Docker:**

#### **Frontend Container (Nginx Alpine):**
- **Base Image:** nginx:alpine para máxima performance
- **Funcionalidades:** Servir arquivos estáticos, SPA routing, compressão gzip
- **Health Check:** Endpoint /health para monitoramento
- **Configuração:** Nginx otimizado para React SPA com fallback

#### **Backend Container (Node.js Alpine):**
- **Base Image:** node:18-alpine para menor footprint
- **Funcionalidades:** API REST, autenticação JWT, conexão com banco
- **Health Check:** Endpoint /api/health com verificação de dependências
- **Otimizações:** npm ci --only=production, multi-stage build

#### **Banco de Dados (PostgreSQL 14):**
- **Container Externo:** Utiliza container postgres:14 existente na VPS
- **Rede:** Comunicação via rede overlay 'redeinterna'
- **Persistência:** Volumes Docker para dados e configurações
- **Backup:** Scripts automatizados com retenção de 30 dias

### **Orquestração Docker Swarm:**

#### **Stack de Produção:**
- **Serviços:** portal-evolution_portalevo-frontend, portal-evolution_portalevo-backend
- **Rede:** redeinterna (overlay network) para comunicação segura
- **Replicas:** 1 réplica por serviço com restart automático
- **Resources:** Limites de memória e CPU definidos para estabilidade

#### **Proxy Reverso Traefik:**
- **Descoberta Automática:** Labels Docker para configuração automática
- **SSL/TLS:** Let's Encrypt com renovação automática
- **Roteamento:** portal.evo-eden.site para frontend, /api para backend
- **Load Balancing:** Distribuição automática de carga entre réplicas

---

## 🚀 **PROCESSO DE DEPLOYMENT**

### **Ambientes de Deploy:**

#### **Produção (portal.evo-eden.site):**
- **Stack:** portal-evolution
- **Imagens:** portal-evolution-backend:v1.0.11-limpeza-prod, portal-evolution-frontend:v1.0.11-limpeza-prod
- **Configuração:** docker-compose.prod.yml com variáveis de ambiente de produção
- **SSL:** Certificado Let's Encrypt automático via Traefik

#### **Desenvolvimento (portaldev.evo-eden.site):**
- **Stack:** portal-evolution-dev
- **Portas:** Backend 5001, Frontend 3001 para acesso direto
- **Configuração:** docker-compose.dev.yml com logs detalhados
- **Debugging:** Variáveis de ambiente para desenvolvimento

### **Fluxo de Deploy:**

#### **Processo Automatizado:**
1. **Parar Stack Atual:** docker stack rm portal-evolution
2. **Remover Imagens Antigas:** Limpeza de imagens obsoletas
3. **Build Novas Imagens:** Construção com tags versionadas
4. **Deploy Stack:** docker stack deploy com docker-compose.prod.yml
5. **Verificação:** Health checks e validação de serviços

#### **Monitoramento Pós-Deploy:**
- **Health Checks:** Verificação automática de saúde dos containers
- **Logs:** Monitoramento de logs para identificação de problemas
- **Métricas:** Acompanhamento de performance e disponibilidade

---

## 📊 **SISTEMA DE RELATÓRIOS**

### **Funcionalidades de Relatórios:**

#### **Relatórios de Sepultamentos:**
- **Filtros:** Cliente, produto, período, status de exumação
- **Dados:** Nome do sepultado, gaveta, data/hora, observações
- **Exportação:** PDF com marca d'água e logo do cliente
- **Métricas:** Taxa de ocupação, sepultamentos por dia, próximas exumações

#### **Relatório Mensal Consolidado:**
- **Visão Geral:** Estatísticas mensais por cliente e produto
- **Ocupação:** Percentual de gavetas ocupadas vs disponíveis
- **Tendências:** Análise temporal de sepultamentos e exumações
- **Projeções:** Estimativas baseadas em dados históricos

#### **Geração de PDFs:**
- **Marca d'água:** Imagem margem_evolution.jpg em todas as páginas
- **Logo:** Logo do cliente (logo_sem_fundo_branco.png) no cabeçalho
- **Formatação:** Layout profissional com tabelas e gráficos
- **Filtros Dinâmicos:** Período personalizável e filtros por entidade

---

## 🔐 **SEGURANÇA E AUTENTICAÇÃO**

### **Sistema de Segurança Multicamadas:**

#### **Autenticação JWT:**
- **Token Expiration:** 24 horas com renovação automática
- **Secret Key:** Chave secreta robusta para assinatura de tokens
- **Payload:** ID, email, tipo de usuário, código do cliente
- **Verificação:** Middleware automático em todas as rotas protegidas

#### **Controle de Acesso Baseado em Roles:**
- **Admin:** Acesso total ao sistema, gestão de usuários e clientes
- **Cliente:** Acesso restrito aos dados do próprio código de cliente
- **Isolamento:** Usuários clientes não visualizam dados de outros clientes
- **Guards:** Middleware específico para rotas administrativas

#### **Rate Limiting e Proteção:**
- **Login:** Máximo 20 tentativas por IP em 15 minutos
- **Reset de Senha:** Máximo 3 tentativas por IP por hora
- **API:** Throttling para prevenir ataques de força bruta
- **Logs:** Rastreamento de tentativas suspeitas

#### **Validação e Sanitização:**
- **SQL Injection:** Prepared statements e parametrização de queries
- **XSS Protection:** Sanitização de inputs e escape de caracteres
- **CORS:** Configuração específica para domínios autorizados
- **Input Validation:** Validação rigorosa de todos os dados de entrada

#### **Criptografia e Proteção de Dados:**
- **Senhas:** bcryptjs com salt rounds para hash seguro
- **Variáveis Sensíveis:** Todas em variáveis de ambiente (.env)
- **HTTPS:** SSL/TLS obrigatório via Let's Encrypt
- **Headers de Segurança:** X-Frame-Options, X-Content-Type-Options, etc.

#### **Auditoria e Compliance:**
- **Logs Completos:** Rastreamento de todas as ações com IP e user agent
- **Retenção:** Logs mantidos por 30 dias para auditoria
- **Integridade:** Validação de integridade referencial no banco
- **Backup:** Backups automáticos com criptografia

---

## 🎯 **FUNCIONALIDADES PRINCIPAIS**

### **Para Usuários Administradores:**

#### **Gestão Completa:**
- **Clientes:** CRUD completo com upload de logos
- **Usuários:** Criação, edição, ativação/desativação
- **Produtos:** Gestão de ETENs e Ossuários
- **Estrutura:** Criação de blocos, sub-blocos e ranges de gavetas
- **Relatórios:** Acesso a todos os relatórios e métricas
- **Logs:** Visualização completa de auditoria do sistema

### **Para Usuários Clientes:**

#### **Operações do Dia a Dia:**
- **Sepultamentos:** Registro completo com validação de gavetas
- **Exumações:** Processo de exumação com controle de datas
- **Transferências:** Movimentação de sepultamentos entre gavetas
- **Book de Sepultamentos:** Visualização organizada por produtos
- **Relatórios:** Geração de PDFs com filtros personalizados
- **Dashboard:** Métricas específicas do cliente

---

## 📈 **MÉTRICAS E INDICADORES**

### **Dashboard Inteligente:**

#### **Estatísticas em Tempo Real:**
- **Gavetas Totais:** Contagem total por cliente/produto
- **Gavetas Ocupadas:** Sepultamentos ativos sem exumação
- **Gavetas Disponíveis:** Gavetas livres para novos sepultamentos
- **Taxa de Ocupação:** Percentual de ocupação por produto
- **Próximas Exumações:** Alertas baseados em meses para exumar

#### **Análises Temporais:**
- **Sepultamentos por Período:** Filtros por data e cliente
- **Tendências de Ocupação:** Evolução histórica da ocupação
- **Previsões:** Estimativas baseadas em dados históricos
- **Comparativos:** Análise entre diferentes produtos e períodos

---

## � **TECNOLOGIAS E DEPENDÊNCIAS**

### **Frontend Dependencies:**
- **React 18.2:** Biblioteca principal com hooks e Context API
- **Material-UI v5:** Framework de componentes com tema customizado
- **React Router v6:** Roteamento com proteção de rotas
- **Axios:** Cliente HTTP com interceptors
- **jsPDF:** Geração de relatórios PDF
- **Vite:** Build tool com otimizações de produção

### **Backend Dependencies:**
- **Express.js:** Framework web minimalista e flexível
- **jsonwebtoken:** Implementação JWT para autenticação
- **bcryptjs:** Criptografia de senhas com salt
- **pg (node-postgres):** Driver PostgreSQL com pool de conexões
- **nodemailer:** Envio de emails para reset de senha
- **multer:** Upload de arquivos (logos de clientes)
- **cors:** Configuração de CORS para múltiplos domínios

### **Infraestrutura:**
- **Docker:** Containerização de aplicações
- **Docker Swarm:** Orquestração de containers
- **Traefik v2.11:** Proxy reverso com descoberta automática
- **PostgreSQL 14:** Banco de dados relacional
- **Nginx Alpine:** Servidor web para arquivos estáticos
- **Let's Encrypt:** Certificados SSL automáticos

---

## 🎯 **CONSIDERAÇÕES FINAIS**

### **Arquitetura Escalável:**
O Portal Evolution foi desenvolvido com arquitetura de microsserviços, permitindo escalabilidade horizontal e manutenção independente de cada componente. A separação clara entre frontend e backend facilita atualizações e melhorias contínuas.

### **Segurança Robusta:**
Implementação de múltiplas camadas de segurança, incluindo autenticação JWT, rate limiting, validação rigorosa de dados e criptografia de senhas. O sistema atende aos padrões de segurança para aplicações web corporativas.

### **Performance Otimizada:**
Utilização de tecnologias modernas como Vite para build otimizado, pool de conexões PostgreSQL, cache de assets estáticos e compressão gzip para máxima performance.

### **Manutenibilidade:**
Código organizado em módulos, documentação completa, logs estruturados e sistema de auditoria facilitam a manutenção e evolução do sistema.

### **Compliance e Auditoria:**
Sistema completo de logs de auditoria com retenção de 30 dias, rastreabilidade de todas as ações e integridade referencial garantem compliance com regulamentações.

---

## 📞 **SUPORTE E MANUTENÇÃO**

### **Ambientes Disponíveis:**
- **Produção:** https://portal.evo-eden.site
- **Desenvolvimento:** https://portaldev.evo-eden.site

### **Monitoramento:**
- **Health Checks:** Endpoints automáticos de verificação
- **Logs Centralizados:** Sistema de logging estruturado
- **Métricas:** Acompanhamento de performance e disponibilidade

### **Backup e Recuperação:**
- **Backup Automático:** Scripts automatizados com retenção
- **Versionamento:** Controle de versão com Git
- **Rollback:** Capacidade de reversão rápida em caso de problemas

---

**Portal Evolution V2.0** - Sistema completo para gestão de cemitérios, ETENs e ossuários com arquitetura moderna e escalável.
