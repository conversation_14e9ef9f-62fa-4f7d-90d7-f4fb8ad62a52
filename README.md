# Portal Evolution - Sistema de Gestão Cemiterial

Sistema completo de gestão cemiterial integrado com infraestrutura moderna usando Docker Swarm, Traefik e SSL automático.

## 🌐 Acesso

- **Portal**: https://portal.evo-eden.site
- **API**: https://portal.evo-eden.site/api/health

## 🚀 Características

- **Frontend**: React 19 + Material-UI + Vite
- **Backend**: Node.js + Express + PostgreSQL
- **Infraestrutura**: Docker Swarm + Traefik + Let's Encrypt
- **SSL**: Automático via Let's Encrypt
- **Monitoramento**: Scripts integrados de monitoramento
- **Escalabilidade**: Suporte a múltiplas réplicas

## 📁 Estrutura do Projeto

```
portalevo/
├── portalcliente/          # 🎯 APLICAÇÃO PRINCIPAL
│   ├── src/                # Frontend React
│   ├── server/             # Backend Node.js
│   ├── public/             # Arquivos estáticos
│   ├── docs/               # Documentação técnica
│   ├── scripts/            # Scripts auxiliares
│   ├── backup/             # Configurações antigas
│   ├── docker-compose.prod.yml  # Configuração Docker Swarm
│   ├── Dockerfile.backend       # Build do backend
│   ├── Dockerfile.frontend      # Build do frontend
│   ├── nginx-traefik.conf       # Configuração Nginx
│   ├── configuracao.env         # Variáveis de ambiente
│   ├── deploy-traefik.sh        # 🚀 Script de deploy
│   ├── monitor.sh               # 📊 Script de monitoramento
│   ├── validate-setup.sh        # ✅ Validação de configuração
│   └── remove-stack.sh          # 🗑️ Remover stack
├── docs-projeto/           # Documentação do projeto
├── assets-projeto/         # Logos e imagens
├── scripts-projeto/        # Scripts diversos
└── backup-projeto/         # Arquivos antigos
```

## ⚡ Quick Start

### 🚀 Método Automático (Recomendado)

#### 0. Testar Sistema (Opcional)
```powershell
cd portalcliente
.\test-system.ps1
```

#### 1. Configuração Inicial (Uma vez)
```powershell
.\quick-setup.ps1
```

#### 2. Desenvolvimento Local
```powershell
.\start-app.ps1 -Local
```

#### 3. Deploy Automático na VPS
```powershell
.\start-app.ps1 -Deploy
```

#### 4. Ambos (Local + Deploy)
```powershell
.\start-app.ps1
```

### 🔧 Método Manual

#### 1. Validar Configuração
```bash
cd portalcliente
bash validate-setup.sh
```

#### 2. Configurar Ambiente
```bash
cp configuracao.env.example configuracao.env
nano configuracao.env
```

#### 3. Deploy
```bash
bash deploy-traefik.sh
```

#### 4. Monitorar
```bash
bash monitor.sh
```

## 🔧 Comandos Principais

### 🚀 Scripts Automáticos (Windows PowerShell)

```powershell
# Configuração inicial rápida
.\quick-setup.ps1

# Deploy automático completo
.\start-app.ps1

# Apenas desenvolvimento local
.\start-app.ps1 -Local

# Apenas deploy na VPS
.\start-app.ps1 -Deploy

# Deploy forçado (sem confirmações)
.\start-app.ps1 -Deploy -Force

# Sincronizar arquivos com VPS
.\sync-to-vps.ps1

# Teste de sincronização (dry-run)
.\sync-to-vps.ps1 -DryRun

# Testar todo o sistema
.\test-system.ps1

# Teste rápido
.\test-system.ps1 -Quick
```

### 🐧 Scripts Manuais (Linux/Bash)

```bash
# Deploy completo
bash deploy-traefik.sh

# Deploy automático
bash deploy-auto.sh

# Monitoramento interativo
bash monitor.sh

# Validar configuração
bash validate-setup.sh

# Remover stack
bash remove-stack.sh

# Atualizar sistema
bash atualizar.sh
```

### Docker Swarm
```bash
# Status dos serviços
docker stack services portal-evolution

# Logs em tempo real
docker service logs -f portal-evolution_portal_backend
docker service logs -f portal-evolution_portal_frontend

# Escalar serviços
docker service scale portal-evolution_portal_backend=2
docker service scale portal-evolution_portal_frontend=2
```

## 🔒 Credenciais Padrão

### Administrador
- **Email**: <EMAIL>
- **Senha**: adminnbr5410!

### Cliente Teste
- **Email**: <EMAIL>
- **Senha**: 54321

## 🏗️ Infraestrutura

### Serviços Integrados
- **Traefik**: Proxy reverso com SSL automático
- **PostgreSQL**: Banco de dados principal
- **N8N**: Automação de workflows
- **Portainer**: Gestão de containers
- **Redis**: Cache e sessões

### Domínios Configurados
- `portal.evo-eden.site` - Portal Evolution
- `n8n.vps.evo-eden.site` - N8N Editor
- `webhook.vps.evo-eden.site` - N8N Webhook
- `port.vps.evo-eden.site` - Portainer

## 📊 Monitoramento

### Health Checks
- **Frontend**: https://portal.evo-eden.site/health
- **Backend**: https://portal.evo-eden.site/api/health

### Logs
```bash
# Logs do Portal
docker service logs portal-evolution_portal_backend
docker service logs portal-evolution_portal_frontend
docker service logs portal-evolution_portal_database

# Logs do Traefik
docker service logs traefik_traefik
```

## 🔧 Troubleshooting

### Problemas Comuns

1. **Stack não inicia**
   ```bash
   bash validate-setup.sh
   docker service logs portal-evolution_portal_backend
   ```

2. **SSL não funciona**
   ```bash
   docker service logs traefik_traefik
   ```

3. **Banco não conecta**
   ```bash
   docker service ls | grep postgres
   ```

### Scripts de Diagnóstico
```bash
# Validação completa
bash validate-setup.sh

# Monitor interativo
bash monitor.sh

# Status da infraestrutura
docker stack ls
docker service ls
docker network ls
```

## 📞 Suporte

- **Email**: <EMAIL>
- **Documentação**: `portalcliente/docs/`
- **Logs**: Use `bash monitor.sh` para diagnóstico

## 🔄 Atualizações

Para atualizar o sistema:
```bash
cd portalcliente
git pull
bash deploy-traefik.sh
```

---

**Nota**: Este projeto está configurado para funcionar com a infraestrutura existente de Docker Swarm e Traefik. Não modifique a configuração de rede ou portas sem verificar impactos nos outros serviços.
