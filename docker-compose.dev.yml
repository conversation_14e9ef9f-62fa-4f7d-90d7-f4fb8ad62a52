# DESENVOLVIMENTO - portaldev.evo-eden.site
version: '3.8'

services:
  portalevo-backend-dev:
    image: portal-evolution-backend:v2025-08-01-data-debug-final-dev
    ports:
      - "5001:3001"
    networks:
      - redeinterna
    environment:
      - NODE_ENV=development
      - DB_HOST=postgres_postgres
      - DB_PORT=5432
      - DB_NAME=dbetens
      - DB_USER=postgres
      - DB_PASSWORD=ab3780bd73ee4e2804d566ce6fd96209
      - JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
      - PORT=3001
      # Configurações de Email
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=jgvhevmyjpuucbhp
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portaldev-api.rule=Host(`portaldev.evo-eden.site`) && PathPrefix(`/api`)"
        - "traefik.http.routers.portaldev-api.entrypoints=websecure"
        - "traefik.http.routers.portaldev-api.tls.certresolver=letsencrypt"
        - "traefik.http.services.portaldev-api.loadbalancer.server.port=3001"

  portalevo-frontend-dev:
    image: portal-evolution-frontend:v2025-08-01-data-debug-final-dev
    ports:
      - "3001:80"
    networks:
      - redeinterna
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portaldev.rule=Host(`portaldev.evo-eden.site`)"
        - "traefik.http.routers.portaldev.entrypoints=websecure"
        - "traefik.http.routers.portaldev.tls.certresolver=letsencrypt"
        - "traefik.http.services.portaldev.loadbalancer.server.port=80"

networks:
  redeinterna:
    external: true
