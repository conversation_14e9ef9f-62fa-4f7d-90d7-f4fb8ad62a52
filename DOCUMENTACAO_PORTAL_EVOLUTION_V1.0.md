# 📋 **DOCUMENTAÇÃO TÉCNICA COMPLETA - PORTAL EVOLUTION V1.0**

## 🎯 **VISÃO GERAL DO SISTEMA**

O **Portal Evolution** é um sistema completo de gestão de atendimentos técnicos desenvolvido especificamente para empresas de manutenção e suporte técnico. O sistema oferece funcionalidades avançadas de controle de protocolos, relatórios detalhados, gestão de usuários e análise de dados operacionais.

### **📊 Características Principais:**
- ✅ **Gestão Completa de Atendimentos** - Controle total do ciclo de vida dos protocolos
- ✅ **Relatórios Avançados** - Análises detalhadas com exportação em PDF
- ✅ **Dashboard Inteligente** - Métricas em tempo real e indicadores de performance
- ✅ **Gestão de Usuários** - Controle de acesso por perfis (Admin/Cliente)
- ✅ **Sistema de Logs** - Rastreabilidade completa de todas as ações
- ✅ **Interface Responsiva** - Design moderno e adaptável a qualquer dispositivo

---

## 🏗️ **ARQUITETURA DO SISTEMA**

### **Stack Tecnológico:**

#### **🎨 Frontend:**
- **React 18.3.1** - Biblioteca principal para interface de usuário
- **Material-UI (MUI) 6.1.6** - Framework de componentes visuais
- **Vite 6.3.5** - Build tool e bundler moderno
- **Axios 1.7.7** - Cliente HTTP para comunicação com API
- **React Router DOM 6.28.0** - Roteamento de páginas
- **jsPDF 2.5.2** - Geração de relatórios em PDF
- **Chart.js 4.4.6** - Gráficos e visualizações de dados

#### **⚙️ Backend:**
- **Node.js 18** - Runtime JavaScript
- **Express.js 4.21.1** - Framework web
- **PostgreSQL 14** - Banco de dados relacional
- **JWT (jsonwebtoken 9.0.2)** - Autenticação e autorização
- **bcryptjs 2.4.3** - Criptografia de senhas
- **Multer 1.4.5** - Upload de arquivos
- **Nodemailer 6.9.16** - Envio de emails
- **CORS 2.8.5** - Controle de acesso entre domínios

#### **🐳 Infraestrutura:**
- **Docker & Docker Swarm** - Containerização e orquestração
- **Nginx Alpine** - Servidor web para frontend
- **Traefik v2.11.2** - Proxy reverso e load balancer
- **Let's Encrypt** - Certificados SSL automáticos
- **VPS Linux** - Servidor de produção

---

## 🗄️ **ESTRUTURA DO BANCO DE DADOS**

### **📋 Tabelas Principais:**

#### **1. usuarios**
```sql
- id (SERIAL PRIMARY KEY)
- nome (VARCHAR(255) NOT NULL)
- email (VARCHAR(255) UNIQUE NOT NULL)
- senha (VARCHAR(255) NOT NULL)
- tipo_usuario (VARCHAR(50) NOT NULL) -- 'admin' ou 'cliente'
- ativo (BOOLEAN DEFAULT true)
- data_criacao (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
- data_atualizacao (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
```

#### **2. atendimentos**
```sql
- id (SERIAL PRIMARY KEY)
- protocolo (VARCHAR(50) UNIQUE NOT NULL)
- status (VARCHAR(50) NOT NULL) -- 'aberto', 'em_andamento', 'concluido'
- tecnico_responsavel (VARCHAR(255))
- descricao_completa (TEXT)
- solucao (TEXT)
- data_abertura (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
- data_fechamento (TIMESTAMP)
- usuario_id (INTEGER REFERENCES usuarios(id))
- produto (VARCHAR(255))
- custo_kwh (DECIMAL(10,4))
- consumo_mensal (DECIMAL(10,2))
```

#### **3. logs_sistema**
```sql
- id (SERIAL PRIMARY KEY)
- usuario_id (INTEGER REFERENCES usuarios(id))
- acao (VARCHAR(255) NOT NULL)
- detalhes (TEXT)
- ip_address (VARCHAR(45))
- user_agent (TEXT)
- data_criacao (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)
```

### **🔗 Relacionamentos:**
- **usuarios** ↔ **atendimentos** (1:N) - Um usuário pode ter múltiplos atendimentos
- **usuarios** ↔ **logs_sistema** (1:N) - Um usuário pode gerar múltiplos logs
- **atendimentos** são independentes mas vinculados ao usuário criador

---

## 🔄 **FLUXO DE DADOS**

### **📥 Coleta de Dados (Backend):**

#### **1. Autenticação:**
```javascript
// Middleware de autenticação JWT
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) return res.sendStatus(401);
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
};
```

#### **2. Endpoints Principais:**
- **POST /api/auth/login** - Autenticação de usuários
- **GET /api/atendimentos** - Listagem de atendimentos
- **POST /api/atendimentos** - Criação de novos protocolos
- **PUT /api/atendimentos/:id** - Atualização de atendimentos
- **DELETE /api/atendimentos/:id** - Exclusão de protocolos
- **GET /api/relatorios/mensal** - Geração de relatórios mensais
- **GET /api/dashboard/metricas** - Métricas do dashboard

#### **3. Validação e Sanitização:**
```javascript
// Exemplo de validação de entrada
const validateAtendimento = (req, res, next) => {
  const { protocolo, descricao_completa, produto } = req.body;
  
  if (!protocolo || !descricao_completa || !produto) {
    return res.status(400).json({ 
      error: 'Campos obrigatórios não preenchidos' 
    });
  }
  
  next();
};
```

### **📤 Exibição de Dados (Frontend):**

#### **1. Gerenciamento de Estado:**
```javascript
// Context para autenticação
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  // Lógica de autenticação
  return (
    <AuthContext.Provider value={{ user, setUser, loading }}>
      {children}
    </AuthContext.Provider>
  );
};
```

#### **2. Comunicação com API:**
```javascript
// Serviço de API
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

#### **3. Componentes Principais:**
- **Dashboard** - Métricas e indicadores principais
- **AtendimentosList** - Listagem e filtros de protocolos
- **AtendimentoModal** - Formulário de criação/edição
- **RelatorioMensal** - Geração e exportação de relatórios
- **UserManagement** - Gestão de usuários (apenas admin)

---

## 📊 **SISTEMA DE RELATÓRIOS**

### **🔍 Funcionalidades:**

#### **1. Relatório Mensal:**
- **Filtros:** Produto, mês, ano, custo kWh
- **Métricas:** Total de atendimentos, taxa de resolução, tempo médio
- **Exportação:** PDF com layout profissional
- **Gráficos:** Distribuição por status, evolução temporal

#### **2. Estrutura do PDF:**
```javascript
// Geração de PDF com jsPDF
const gerarRelatorioPDF = (dados) => {
  const doc = new jsPDF();
  
  // Cabeçalho com logo
  doc.addImage(logoBase64, 'PNG', 20, 10, 40, 20);
  
  // Título e período
  doc.setFontSize(18);
  doc.text('Relatório Mensal de Atendimentos', 20, 50);
  
  // Tabelas de dados
  dados.atendimentos.forEach((item, index) => {
    // Renderização de cada protocolo
  });
  
  return doc;
};
```

#### **3. Métricas Calculadas:**
- **Taxa de Ocupação:** (Atendimentos Concluídos / Total) × 100
- **Tempo Médio de Resolução:** Média em dias
- **Distribuição por Técnico:** Quantidade por responsável
- **Evolução Mensal:** Comparativo com períodos anteriores

---

## 🔐 **SEGURANÇA E AUTENTICAÇÃO**

### **🛡️ Medidas Implementadas:**

#### **1. Autenticação JWT:**
- **Token Expiration:** 24 horas
- **Refresh Token:** Renovação automática
- **Secure Storage:** localStorage com validação

#### **2. Controle de Acesso:**
```javascript
// Middleware de autorização por tipo de usuário
const requireAdmin = (req, res, next) => {
  if (req.user.tipo_usuario !== 'admin') {
    return res.status(403).json({ 
      error: 'Acesso negado. Apenas administradores.' 
    });
  }
  next();
};
```

#### **3. Validação de Entrada:**
- **SQL Injection Prevention:** Prepared statements
- **XSS Protection:** Sanitização de inputs
- **CORS Configuration:** Domínios permitidos específicos

#### **4. Criptografia:**
- **Senhas:** bcrypt com salt rounds 10
- **Dados Sensíveis:** Variáveis de ambiente
- **HTTPS:** Certificados SSL/TLS obrigatórios

---

## 🚀 **DEPLOY E INFRAESTRUTURA**

### **📦 Containerização:**

#### **1. Dockerfile Backend:**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY server/package*.json ./
RUN npm ci --only=production
COPY server/ .
RUN mkdir -p uploads
EXPOSE 3001
CMD ["node", "index.js"]
```

#### **2. Dockerfile Frontend:**
```dockerfile
FROM nginx:alpine
RUN echo 'events { worker_connections 1024; }' > /etc/nginx/nginx.conf
COPY dist /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### **3. Docker Compose Produção:**
```yaml
version: '3.8'
services:
  portalevo-backend:
    image: portal-evolution-backend:v1.0.0-portal-evolution-prod
    networks: [redeinterna]
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres_postgres
      - JWT_SECRET=portal_jwt_secret_key_muito_segura_2025
    deploy:
      replicas: 1
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.portal-api.rule=Host(`portal.evo-eden.site`) && PathPrefix(`/api`)"
```

### **🌐 Proxy Reverso (Traefik):**
- **SSL Automático:** Let's Encrypt
- **Load Balancing:** Distribuição de carga
- **Health Checks:** Monitoramento de saúde dos serviços
- **Rate Limiting:** Proteção contra ataques DDoS

---

## 📈 **MONITORAMENTO E LOGS**

### **📊 Sistema de Logs:**

#### **1. Logs de Aplicação:**
```javascript
// Middleware de logging
const logAction = async (req, res, next) => {
  const logData = {
    usuario_id: req.user?.id,
    acao: `${req.method} ${req.path}`,
    detalhes: JSON.stringify(req.body),
    ip_address: req.ip,
    user_agent: req.get('User-Agent')
  };
  
  await db.query(
    'INSERT INTO logs_sistema (usuario_id, acao, detalhes, ip_address, user_agent) VALUES ($1, $2, $3, $4, $5)',
    [logData.usuario_id, logData.acao, logData.detalhes, logData.ip_address, logData.user_agent]
  );
  
  next();
};
```

#### **2. Limpeza Automática:**
- **Retenção:** 30 dias
- **Cron Job:** Limpeza diária automática
- **Backup:** Arquivamento antes da exclusão

### **🔍 Métricas de Performance:**
- **Response Time:** Tempo de resposta das APIs
- **Error Rate:** Taxa de erros por endpoint
- **User Activity:** Atividade por usuário
- **Resource Usage:** Uso de CPU, memória e disco

---

## 🎨 **INTERFACE DE USUÁRIO**

### **📱 Design Responsivo:**

#### **1. Material-UI Components:**
- **DataGrid:** Tabelas avançadas com filtros
- **Modal:** Formulários de criação/edição
- **Dashboard Cards:** Métricas visuais
- **Charts:** Gráficos interativos

#### **2. Temas e Cores:**
```javascript
const theme = createTheme({
  palette: {
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
    background: { default: '#f5f5f5' }
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif'
  }
});
```

#### **3. Navegação:**
- **Sidebar:** Menu lateral com ícones
- **Breadcrumbs:** Navegação hierárquica
- **Tabs:** Organização de conteúdo
- **Search:** Busca global integrada

---

## 🔧 **CONFIGURAÇÃO E DEPLOYMENT**

### **⚙️ Variáveis de Ambiente:**

#### **Backend (.env):**
```env
NODE_ENV=production
DB_HOST=postgres_postgres
DB_PORT=5432
DB_NAME=dbetens
DB_USER=postgres
DB_PASSWORD=ab3780bd73ee4e2804d566ce6fd96209
JWT_SECRET=portal_jwt_secret_key_muito_segura_2025
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=jgvhevmyjpuucbhp
```

#### **Frontend (.env):**
```env
VITE_API_URL=https://portal.evo-eden.site/api
VITE_APP_NAME=Portal Evolution
VITE_VERSION=1.0.0
```

### **🚀 Scripts de Deploy:**

#### **1. Deploy Desenvolvimento:**
```bash
#!/bin/bash
TAG="v1.0.0-portal-evolution-dev"
docker build -t portal-evolution-backend:$TAG .
docker build -t portal-evolution-frontend:$TAG .
docker stack deploy -c docker-compose.dev.yml portal-evolution-dev
```

#### **2. Deploy Produção:**
```bash
#!/bin/bash
TAG="v1.0.0-portal-evolution-prod"
docker build -t portal-evolution-backend:$TAG .
docker build -t portal-evolution-frontend:$TAG .
docker stack deploy -c docker-compose.prod.yml portal-evolution
```

---

## 📋 **FUNCIONALIDADES POR PERFIL**

### **👨‍💼 Administrador:**
- ✅ **Gestão Completa de Usuários** - Criar, editar, desativar usuários
- ✅ **Acesso Total aos Atendimentos** - Visualizar todos os protocolos
- ✅ **Relatórios Avançados** - Todos os tipos de relatórios
- ✅ **Dashboard Completo** - Todas as métricas e indicadores
- ✅ **Logs do Sistema** - Visualização completa de logs
- ✅ **Configurações** - Parâmetros do sistema

### **👤 Cliente:**
- ✅ **Gestão de Atendimentos** - Criar, editar, visualizar próprios protocolos
- ✅ **Relatórios Básicos** - Relatórios dos próprios atendimentos
- ✅ **Dashboard Pessoal** - Métricas dos próprios dados
- ❌ **Gestão de Usuários** - Não tem acesso
- ❌ **Logs Completos** - Apenas próprios logs
- ❌ **Configurações** - Acesso limitado

---

## 🎯 **VERSÃO 1.0 - MARCOS ALCANÇADOS**

### **✅ Funcionalidades Implementadas:**
1. **Sistema de Autenticação Completo** - Login, logout, controle de sessão
2. **CRUD de Atendimentos** - Criar, ler, atualizar, deletar protocolos
3. **Relatórios Mensais com PDF** - Exportação profissional
4. **Dashboard Interativo** - Métricas em tempo real
5. **Gestão de Usuários** - Controle completo de acesso
6. **Sistema de Logs** - Rastreabilidade total
7. **Interface Responsiva** - Design moderno e adaptável
8. **Deploy Automatizado** - CI/CD com Docker
9. **Segurança Avançada** - JWT, HTTPS, validações
10. **Monitoramento** - Logs, métricas, alertas

### **🌐 URLs de Acesso:**
- **Produção:** https://portal.evo-eden.site
- **Desenvolvimento:** https://portaldev.evo-eden.site

### **📊 Estatísticas da V1.0:**
- **Linhas de Código:** ~15.000 linhas
- **Componentes React:** 25+ componentes
- **Endpoints API:** 20+ endpoints
- **Tabelas DB:** 3 tabelas principais
- **Tempo de Desenvolvimento:** 6 meses
- **Tecnologias:** 15+ tecnologias integradas

---

## 🚀 **PRÓXIMOS PASSOS (V2.0)**

### **🎯 Roadmap Futuro:**
1. **Notificações Push** - Alertas em tempo real
2. **Mobile App** - Aplicativo nativo
3. **Integração WhatsApp** - Comunicação direta
4. **BI Avançado** - Business Intelligence
5. **API Pública** - Integrações externas
6. **Multi-tenancy** - Múltiplos clientes
7. **Workflow Engine** - Automação de processos
8. **Chat Interno** - Comunicação da equipe

---

**🎉 Portal Evolution V1.0 - Sistema Completo de Gestão de Atendimentos**
**Desenvolvido com excelência técnica e foco na experiência do usuário**
